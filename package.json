{"name": "durdoo-portal-framework-vue", "version": "3.1.0", "private": true, "scripts": {"dev": "NODE_OPTIONS=--max-old-space-size=4096 VITE_CJS_IGNORE_WARNING=true vite", "serve": "pnpm dev", "build": "rimraf dist && NODE_OPTIONS=--max-old-space-size=8192 VITE_CJS_IGNORE_WARNING=true vite build && node build/build-and-archive.js", "build:staging": "rimraf dist && vite build --mode staging", "report": "rimraf dist && vite build", "preview": "vite preview", "preview:build": "pnpm build && vite preview", "typecheck": "tsc --noEmit && vue-tsc --noEmit --skipLib<PERSON><PERSON><PERSON>", "svgo": "svgo -f src/assets/svg -o src/assets/svg", "cloc": "NODE_OPTIONS=--max-old-space-size=4096 cloc . --exclude-dir=node_modules --exclude-lang=YAML", "clean:cache": "rimraf node_modules && pnpm install", "lint:eslint": "eslint --cache --max-warnings 0  \"{src,mock,build}/**/*.{vue,js,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,ts,json,tsx,css,scss,vue,html,md}\"", "lint:stylelint": "stylelint \"**/*.{html,vue,css,scss}\" --fix --cache --cache-location node_modules/.cache/stylelint/", "lint:pretty": "pretty-quick --staged", "lint": "pnpm lint:eslint && pnpm lint:prettier && pnpm lint:stylelint", "preinstall": "npx only-allow pnpm"}, "browserslist": ["> 1%", "not ie 11", "not op_mini all"], "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fingerprintjs/fingerprintjs": "^4.6.2", "@kjgl77/datav-vue3": "^1.7.4", "@pureadmin/descriptions": "^1.2.1", "@pureadmin/table": "^2.4.7", "@pureadmin/utils": "^1.18.0", "@smallwei/avue": "^3.7.0", "@vue-office/docx": "^1.6.3", "@vue-office/excel": "^1.7.14", "@vue-office/pdf": "^2.0.10", "@vueuse/core": "^10.11.1", "@vueuse/motion": "^2.2.6", "animate.css": "^4.1.1", "axios": "^1.10.0", "bpmn-js": "^18.6.2", "dayjs": "^1.11.13", "dompurify": "^3.2.6", "echarts": "^5.6.0", "element-plus": "^2.10.2", "js-cookie": "^3.0.5", "mitt": "^3.0.1", "mockjs": "^1.1.0", "moment": "^2.29.4", "nprogress": "^0.2.0", "path": "^0.12.7", "pinia": "^2.3.1", "pinyin-pro": "^3.26.0", "qs": "^6.14.0", "responsive-storage": "^2.2.0", "sm-crypto": "^0.3.13", "socket.io-client": "^4.0.0", "sortablejs": "^1.15.6", "swiper": "^9.4.1", "uuid": "^9.0.0", "vue": "^3.5.17", "vue-demi": "^0.14.10", "vue-router": "^4.5.1", "vue-types": "^5.1.3", "vue3-seamless-scroll": "^2.0.1", "xgplayer": "^3.0.22", "xgplayer-flv": "^3.0.22"}, "devDependencies": {"@iconify-icons/ep": "^1.2.12", "@iconify-icons/ri": "^1.2.10", "@iconify/vue": "^4.3.0", "@pureadmin/theme": "^3.3.0", "@types/js-cookie": "^3.0.6", "@types/node": "^20.19.1", "@types/nprogress": "0.2.0", "@types/qs": "^6.14.0", "@types/sm-crypto": "^0.3.4", "@types/sortablejs": "^1.15.8", "@typescript-eslint/eslint-plugin": "^8.35.0", "@typescript-eslint/parser": "^8.35.0", "@vitejs/plugin-vue": "^4.6.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "archiver": "^7.0.1", "autoprefixer": "^10.4.21", "cloc": "^2.11.0", "cssnano": "^6.1.2", "eslint": "^9.29.0", "eslint-plugin-prettier": "^4.2.1", "eslint-plugin-vue": "^9.33.0", "picocolors": "^1.1.1", "postcss": "^8.5.6", "postcss-html": "^1.8.0", "postcss-import": "^15.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.6.0", "pretty-quick": "^4.2.2", "rimraf": "^5.0.10", "rollup": "4.28.1", "rollup-plugin-visualizer": "^5.14.0", "sass": "1.79.5", "sass-loader": "^13.3.3", "stylelint": "^15.11.0", "stylelint-config-html": "^1.1.0", "stylelint-config-recess-order": "^4.6.0", "stylelint-config-recommended": "^12.0.0", "stylelint-config-recommended-scss": "^12.0.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard": "^33.0.0", "stylelint-config-standard-scss": "^9.0.0", "stylelint-order": "^6.0.4", "stylelint-prettier": "^3.0.0", "stylelint-scss": "^5.3.2", "svgo": "^3.3.2", "tailwindcss": "^3.4.17", "terser": "^5.43.1", "typescript": "5.0.4", "unplugin-auto-import": "^0.15.1", "unplugin-vue-components": "^0.24.1", "vite": "^5.4.19", "vite-plugin-build-info": "^0.1.2", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-remove-console": "^2.2.0", "vite-svg-loader": "^4.0.0", "vue-eslint-parser": "^9.4.3", "vue-tsc": "^1.8.27"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["rollup", "webpack", "core-js"]}, "allowedDeprecatedVersions": {"sourcemap-codec": "*", "w3c-hr-time": "*", "stable": "*"}}, "repository": "git@*************:9022/view-common/portal-frontend.git", "author": "liyun", "license": "MIT"}