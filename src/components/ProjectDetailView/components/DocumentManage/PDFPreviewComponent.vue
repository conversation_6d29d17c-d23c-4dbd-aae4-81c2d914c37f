<template>
  <div class="pdf-preview-component">
    <!-- PDF工具栏 -->
    <div class="pdf-toolbar">
      <div class="toolbar-left">
        <el-button-group size="small">
          <el-button @click="prevPage" :disabled="currentPage <= 1">
            <el-icon><ArrowLeft /></el-icon>
            上一页
          </el-button>
          <el-button disabled>{{ currentPage }} / {{ totalPages }}</el-button>
          <el-button @click="nextPage" :disabled="currentPage >= totalPages">
            下一页
            <el-icon><ArrowRight /></el-icon>
          </el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-center">
        <el-button-group size="small">
          <el-button @click="zoomOut" :disabled="scale <= 0.5">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="resetZoom">{{ Math.round(scale * 100) }}%</el-button>
          <el-button @click="zoomIn" :disabled="scale >= 3">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>
      </div>
      
      <div class="toolbar-right">
        <el-button size="small" @click="toggleFullscreen">
          <el-icon><FullScreen /></el-icon>
          {{ isFullscreen ? '退出全屏' : '全屏' }}
        </el-button>
        <el-button size="small" @click="downloadPDF">
          <el-icon><Download /></el-icon>
          下载
        </el-button>
      </div>
    </div>

    <!-- PDF内容区域 -->
    <div class="pdf-content" :class="{ 'fullscreen': isFullscreen }" ref="contentRef">
      <div v-if="isLoading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <p>正在加载PDF文档...</p>
        <el-progress :percentage="loadingProgress" :show-text="false" />
      </div>
      
      <div v-else-if="error" class="error-container">
        <el-result
          icon="error"
          title="PDF加载失败"
          :sub-title="error"
        >
          <template #extra>
            <el-button type="primary" @click="retryLoad">重新加载</el-button>
            <el-button @click="$emit('close')">关闭</el-button>
          </template>
        </el-result>
      </div>
      
      <div v-else class="pdf-viewer-container">
        <!-- 使用iframe方式预览PDF -->
        <iframe
          v-if="pdfUrl"
          :src="pdfUrl"
          width="100%"
          :height="viewerHeight"
          frameborder="0"
          class="pdf-iframe"
          :style="{ transform: `scale(${scale})`, transformOrigin: 'top center' }"
        ></iframe>
        
        <!-- 备选：使用embed标签 -->
        <embed
          v-else-if="pdfUrl"
          :src="pdfUrl"
          type="application/pdf"
          width="100%"
          :height="viewerHeight"
          class="pdf-embed"
        />
        
        <!-- 如果浏览器不支持PDF预览 -->
        <div v-else class="pdf-fallback">
          <el-result
            icon="warning"
            title="浏览器不支持PDF预览"
            sub-title="请下载文件后使用PDF阅读器查看"
          >
            <template #extra>
              <el-button type="primary" @click="downloadPDF">
                <el-icon><Download /></el-icon>
                下载PDF文件
              </el-button>
            </template>
          </el-result>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  ArrowLeft, 
  ArrowRight, 
  ZoomOut, 
  ZoomIn, 
  FullScreen, 
  Download, 
  Loading 
} from '@element-plus/icons-vue';

// Props
interface Props {
  fileName: string;
  fileData: string; // base64编码的PDF数据
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
  download: [];
}>();

// 响应式数据
const isLoading = ref(true);
const loadingProgress = ref(0);
const error = ref('');
const pdfUrl = ref('');
const currentPage = ref(1);
const totalPages = ref(0);
const scale = ref(1);
const isFullscreen = ref(false);
const contentRef = ref<HTMLElement>();

// 计算属性
const viewerHeight = computed(() => {
  return isFullscreen.value ? window.innerHeight - 60 : 600;
});

// PDF相关方法
const createPDFUrl = () => {
  try {
    // 将base64转换为Blob
    const binaryString = window.atob(props.fileData);
    const bytes = new Uint8Array(binaryString.length);
    
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    
    const blob = new Blob([bytes], { type: 'application/pdf' });
    const url = URL.createObjectURL(blob);
    
    return url;
  } catch (err) {
    console.error('创建PDF URL失败:', err);
    error.value = '文件格式错误，无法解析PDF数据';
    return '';
  }
};

const loadPDF = async () => {
  try {
    isLoading.value = true;
    loadingProgress.value = 0;
    error.value = '';
    
    // 模拟加载进度
    const progressInterval = setInterval(() => {
      if (loadingProgress.value < 90) {
        loadingProgress.value += 10;
      }
    }, 100);
    
    // 创建PDF URL
    const url = createPDFUrl();
    
    if (url) {
      pdfUrl.value = url;
      
      // 检测PDF是否可以加载
      const testImg = new Image();
      testImg.onload = () => {
        clearInterval(progressInterval);
        loadingProgress.value = 100;
        setTimeout(() => {
          isLoading.value = false;
        }, 300);
      };
      
      testImg.onerror = () => {
        clearInterval(progressInterval);
        // PDF可能无法作为图片加载，但这不意味着失败
        loadingProgress.value = 100;
        setTimeout(() => {
          isLoading.value = false;
        }, 300);
      };
      
      // 尝试加载PDF的第一页作为图片来检测
      testImg.src = url + '#page=1';
      
      // 设置超时
      setTimeout(() => {
        clearInterval(progressInterval);
        if (isLoading.value) {
          loadingProgress.value = 100;
          isLoading.value = false;
        }
      }, 2000);
      
    } else {
      clearInterval(progressInterval);
      isLoading.value = false;
    }
    
  } catch (err) {
    console.error('PDF加载失败:', err);
    error.value = 'PDF文件加载失败，请检查文件是否损坏';
    isLoading.value = false;
  }
};

// 页面控制
const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    updatePDFPage();
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    updatePDFPage();
  }
};

const updatePDFPage = () => {
  if (pdfUrl.value) {
    // 更新iframe的src以显示指定页面
    const iframe = document.querySelector('.pdf-iframe') as HTMLIFrameElement;
    if (iframe) {
      iframe.src = `${pdfUrl.value}#page=${currentPage.value}`;
    }
  }
};

// 缩放控制
const zoomIn = () => {
  if (scale.value < 3) {
    scale.value = Math.min(3, scale.value + 0.25);
  }
};

const zoomOut = () => {
  if (scale.value > 0.5) {
    scale.value = Math.max(0.5, scale.value - 0.25);
  }
};

const resetZoom = () => {
  scale.value = 1;
};

// 全屏控制
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  
  if (isFullscreen.value) {
    // 进入全屏
    if (contentRef.value?.requestFullscreen) {
      contentRef.value.requestFullscreen();
    }
  } else {
    // 退出全屏
    if (document.exitFullscreen) {
      document.exitFullscreen();
    }
  }
};

// 下载PDF
const downloadPDF = () => {
  try {
    if (pdfUrl.value) {
      const link = document.createElement('a');
      link.href = pdfUrl.value;
      link.download = props.fileName || 'document.pdf';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      ElMessage.success('PDF下载成功');
    } else {
      emit('download');
    }
  } catch (error) {
    console.error('PDF下载失败:', error);
    ElMessage.error('PDF下载失败');
  }
};

// 重新加载
const retryLoad = () => {
  loadPDF();
};

// 键盘事件处理
const handleKeydown = (e: KeyboardEvent) => {
  switch (e.key) {
    case 'ArrowLeft':
      e.preventDefault();
      prevPage();
      break;
    case 'ArrowRight':
      e.preventDefault();
      nextPage();
      break;
    case 'Escape':
      if (isFullscreen.value) {
        toggleFullscreen();
      }
      break;
    case '+':
    case '=':
      e.preventDefault();
      zoomIn();
      break;
    case '-':
      e.preventDefault();
      zoomOut();
      break;
    case '0':
      e.preventDefault();
      resetZoom();
      break;
  }
};

// 监听全屏状态变化
const handleFullscreenChange = () => {
  isFullscreen.value = !!document.fullscreenElement;
};

// 监听文件数据变化
watch(() => props.fileData, () => {
  if (props.fileData) {
    loadPDF();
  }
}, { immediate: true });

// 生命周期
onMounted(() => {
  document.addEventListener('keydown', handleKeydown);
  document.addEventListener('fullscreenchange', handleFullscreenChange);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleKeydown);
  document.removeEventListener('fullscreenchange', handleFullscreenChange);
  
  // 清理URL
  if (pdfUrl.value) {
    URL.revokeObjectURL(pdfUrl.value);
  }
});
</script>

<style lang="scss" scoped>
.pdf-preview-component {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f5f5f5;
  
  .pdf-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: white;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    .toolbar-left,
    .toolbar-center,
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .pdf-content {
    flex: 1;
    position: relative;
    overflow: auto;
    
    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9999;
      background: white;
    }
    
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 400px;
      color: #909399;
      
      .el-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      p {
        margin: 16px 0;
        font-size: 16px;
      }
      
      .el-progress {
        width: 200px;
      }
    }
    
    .error-container {
      padding: 40px;
    }
    
    .pdf-viewer-container {
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: flex-start;
      padding: 20px;
      
      .pdf-iframe,
      .pdf-embed {
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        transition: transform 0.3s ease;
      }
      
      .pdf-fallback {
        width: 100%;
        padding: 40px;
      }
    }
  }
}

// 全屏模式下的样式调整
:deep(.pdf-content.fullscreen) {
  .pdf-toolbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10000;
  }
  
  .pdf-viewer-container {
    padding-top: 60px;
  }
}
</style>
