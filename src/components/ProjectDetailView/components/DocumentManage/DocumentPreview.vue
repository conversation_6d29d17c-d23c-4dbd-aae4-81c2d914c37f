<template>
  <div class="document-preview-container">
    <!-- PDF预览 -->
    <div v-if="isPDF" class="pdf-preview">
      <iframe
        :src="pdfUrl"
        width="100%"
        height="600px"
        frameborder="0"
        style="border: 1px solid #ddd; border-radius: 4px;"
      ></iframe>
    </div>

    <!-- Office文档预览 -->
    <div v-else-if="isOfficeDocument" class="office-preview">
      <div class="preview-loading" v-if="isLoading">
        <el-icon class="is-loading"><Loading /></el-icon>
        <p>正在加载预览...</p>
      </div>
      <iframe
        v-else
        :src="officePreviewUrl"
        width="100%"
        height="600px"
        frameborder="0"
        style="border: 1px solid #ddd; border-radius: 4px;"
        @load="handleIframeLoad"
        @error="handleIframeError"
      ></iframe>
    </div>

    <!-- 图片预览 -->
    <div v-else-if="isImage" class="image-preview">
      <el-image
        :src="imageUrl"
        fit="contain"
        style="width: 100%; max-height: 600px;"
        :preview-src-list="[imageUrl]"
        :initial-index="0"
        preview-teleported
      />
    </div>

    <!-- 文本文件预览 -->
    <div v-else-if="isTextFile" class="text-preview">
      <pre class="text-content">{{ textContent }}</pre>
    </div>

    <!-- 不支持的文件类型 -->
    <div v-else class="unsupported-preview">
      <div class="unsupported-content">
        <el-icon size="64" color="#909399"><Document /></el-icon>
        <h3>{{ fileName }}</h3>
        <p>此文件类型暂不支持在线预览</p>
        <p>文件类型: {{ fileType.toUpperCase() }}</p>
        <el-button type="primary" @click="$emit('close')">
          关闭预览
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Loading, Document } from '@element-plus/icons-vue';

// Props
interface Props {
  fileName: string;
  fileType: string;
  fileData: string; // base64编码的文件数据
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
}>();

// 响应式数据
const isLoading = ref(true);
const pdfUrl = ref('');
const officePreviewUrl = ref('');
const imageUrl = ref('');
const textContent = ref('');

// 计算属性 - 判断文件类型
const isPDF = computed(() => props.fileType === 'pdf');

const isOfficeDocument = computed(() => {
  const officeTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
  return officeTypes.includes(props.fileType);
});

const isImage = computed(() => {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  return imageTypes.includes(props.fileType);
});

const isTextFile = computed(() => {
  const textTypes = ['txt', 'json', 'xml', 'csv', 'log'];
  return textTypes.includes(props.fileType);
});

// 获取文件MIME类型
const getMimeType = (fileType: string): string => {
  const mimeTypes: { [key: string]: string } = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'webp': 'image/webp',
    'txt': 'text/plain',
    'json': 'application/json',
    'xml': 'application/xml',
    'csv': 'text/csv'
  };
  return mimeTypes[fileType] || 'application/octet-stream';
};

// 将base64转换为Blob URL
const createBlobUrl = (base64Data: string, mimeType: string): string => {
  try {
    const binaryString = window.atob(base64Data);
    const bytes = new Uint8Array(binaryString.length);
    
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    
    const blob = new Blob([bytes], { type: mimeType });
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('创建Blob URL失败:', error);
    return '';
  }
};

// 处理PDF预览
const setupPDFPreview = () => {
  const mimeType = getMimeType(props.fileType);
  const blobUrl = createBlobUrl(props.fileData, mimeType);
  
  if (blobUrl) {
    pdfUrl.value = blobUrl;
    isLoading.value = false;
  } else {
    ElMessage.error('PDF预览加载失败');
  }
};

// 处理Office文档预览 - 使用Microsoft Office Online
const setupOfficePreview = () => {
  try {
    const mimeType = getMimeType(props.fileType);
    const blobUrl = createBlobUrl(props.fileData, mimeType);
    
    if (blobUrl) {
      // 使用Microsoft Office Online Viewer
      // 注意：这需要文件能够通过公网访问，本地blob URL可能不工作
      // 实际项目中建议上传到临时存储服务或使用专门的预览服务
      officePreviewUrl.value = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(blobUrl)}`;
      
      // 由于blob URL限制，这里提供备选方案
      ElMessage.warning('Office文档预览需要文件服务器支持，当前显示可能有限制');
    }
  } catch (error) {
    console.error('Office预览设置失败:', error);
    ElMessage.error('Office文档预览加载失败');
  }
};

// 处理图片预览
const setupImagePreview = () => {
  const mimeType = getMimeType(props.fileType);
  const blobUrl = createBlobUrl(props.fileData, mimeType);
  
  if (blobUrl) {
    imageUrl.value = blobUrl;
    isLoading.value = false;
  } else {
    ElMessage.error('图片预览加载失败');
  }
};

// 处理文本文件预览
const setupTextPreview = () => {
  try {
    const binaryString = window.atob(props.fileData);
    textContent.value = binaryString;
    isLoading.value = false;
  } catch (error) {
    console.error('文本预览解析失败:', error);
    ElMessage.error('文本文件预览加载失败');
  }
};

// iframe加载完成
const handleIframeLoad = () => {
  isLoading.value = false;
};

// iframe加载错误
const handleIframeError = () => {
  isLoading.value = false;
  ElMessage.error('文档预览加载失败');
};

// 初始化预览
const initPreview = () => {
  isLoading.value = true;
  
  if (isPDF.value) {
    setupPDFPreview();
  } else if (isOfficeDocument.value) {
    setupOfficePreview();
  } else if (isImage.value) {
    setupImagePreview();
  } else if (isTextFile.value) {
    setupTextPreview();
  } else {
    isLoading.value = false;
  }
};

// 监听文件数据变化
watch(() => props.fileData, () => {
  if (props.fileData) {
    initPreview();
  }
}, { immediate: true });

// 组件卸载时清理URL
onMounted(() => {
  return () => {
    if (pdfUrl.value) URL.revokeObjectURL(pdfUrl.value);
    if (officePreviewUrl.value && officePreviewUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(officePreviewUrl.value);
    }
    if (imageUrl.value) URL.revokeObjectURL(imageUrl.value);
  };
});
</script>

<style lang="scss" scoped>
.document-preview-container {
  width: 100%;
  min-height: 600px;
  
  .preview-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 600px;
    color: #909399;
    
    .el-icon {
      font-size: 32px;
      margin-bottom: 16px;
    }
    
    p {
      margin: 0;
      font-size: 14px;
    }
  }
  
  .text-preview {
    .text-content {
      background: #f5f5f5;
      padding: 16px;
      border-radius: 4px;
      max-height: 600px;
      overflow: auto;
      font-family: 'Courier New', monospace;
      font-size: 14px;
      line-height: 1.5;
      white-space: pre-wrap;
      word-wrap: break-word;
    }
  }
  
  .unsupported-preview {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 600px;
    
    .unsupported-content {
      text-align: center;
      color: #909399;
      
      .el-icon {
        margin-bottom: 16px;
      }
      
      h3 {
        margin: 16px 0;
        color: #303133;
        font-size: 18px;
      }
      
      p {
        margin: 8px 0;
        font-size: 14px;
      }
      
      .el-button {
        margin-top: 16px;
      }
    }
  }
}
</style>
