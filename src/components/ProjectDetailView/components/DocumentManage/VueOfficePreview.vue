<template>
  <div class="vue-office-preview">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar">
      <div class="toolbar-left">
        <span class="file-name">{{ fileName }}</span>
        <el-tag :type="getFileTypeColor(fileType)" size="small">
          {{ fileType.toUpperCase() }}
        </el-tag>
      </div>
      <div class="toolbar-right">
        <el-button size="small" type="primary" @click="downloadFile" v-if="supportDownload">
          <el-icon><Download /></el-icon>
          下载
        </el-button>
        <el-button size="small" @click="$emit('close')">
          <el-icon><Close /></el-icon>
          关闭
        </el-button>
      </div>
    </div>

    <!-- 预览内容区域 -->
    <div class="preview-content">
      <!-- Word文档预览 -->
      <vue-office-docx
        v-if="isWordDocument && fileArrayBuffer"
        :src="fileArrayBuffer"
        style="height: 600px;"
        @rendered="onRendered"
        @error="onError"
      />

      <!-- Excel文档预览 -->
      <vue-office-excel
        v-else-if="isExcelDocument && fileArrayBuffer"
        :src="fileArrayBuffer"
        style="height: 600px;"
        @rendered="onRendered"
        @error="onError"
      />

      <!-- PDF文档预览 -->
      <vue-office-pdf
        v-else-if="isPdfDocument && fileArrayBuffer"
        :src="fileArrayBuffer"
        style="height: 600px;"
        @rendered="onRendered"
        @error="onError"
      />

      <!-- PowerPoint文档预览 -->
      <div v-else-if="isPowerPointDocument" class="ppt-preview">
        <el-result
          icon="warning"
          title="PowerPoint预览"
          sub-title="vue-office暂不支持PPT预览，建议下载后查看"
        >
          <template #extra>
            <el-button type="primary" @click="downloadFile">
              <el-icon><Download /></el-icon>
              下载文件
            </el-button>
          </template>
        </el-result>
      </div>

      <!-- 图片预览 -->
      <div v-else-if="isImageFile" class="image-preview">
        <el-image
          :src="imageUrl"
          fit="contain"
          style="width: 100%; height: 600px;"
          :preview-src-list="[imageUrl]"
          :initial-index="0"
          preview-teleported
        />
      </div>

      <!-- 文本文件预览 -->
      <div v-else-if="isTextFile" class="text-preview">
        <pre class="text-content">{{ textContent }}</pre>
      </div>

      <!-- 加载中状态 -->
      <div v-else-if="isLoading" class="loading-container">
        <el-icon class="is-loading"><Loading /></el-icon>
        <p>正在加载文件预览...</p>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="hasError" class="error-container">
        <el-result
          icon="error"
          title="预览失败"
          :sub-title="errorMessage"
        >
          <template #extra>
            <el-button type="primary" @click="retryPreview">重试</el-button>
            <el-button @click="downloadFile">下载文件</el-button>
          </template>
        </el-result>
      </div>

      <!-- 不支持的文件类型 -->
      <div v-else class="unsupported-preview">
        <el-result
          icon="info"
          title="文件类型不支持预览"
          :sub-title="`文件类型: ${fileType.toUpperCase()}`"
        >
          <template #extra>
            <el-button type="primary" @click="downloadFile">
              <el-icon><Download /></el-icon>
              下载文件
            </el-button>
            <el-button @click="$emit('close')">关闭</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { Download, Close, Loading } from '@element-plus/icons-vue';

// 动态导入vue-office组件
import VueOfficeDocx from '@vue-office/docx';
import VueOfficeExcel from '@vue-office/excel';
import VueOfficePdf from '@vue-office/pdf';

// 导入样式
import '@vue-office/docx/lib/index.css';
import '@vue-office/excel/lib/index.css';
import '@vue-office/pdf/lib/index.css';

// Props
interface Props {
  fileName: string;
  fileType: string;
  fileData: string; // base64编码的文件数据
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
}>();

// 响应式数据
const isLoading = ref(true);
const hasError = ref(false);
const errorMessage = ref('');
const fileArrayBuffer = ref<ArrayBuffer | null>(null);
const imageUrl = ref('');
const textContent = ref('');

// 计算属性 - 文件类型判断
const isWordDocument = computed(() => {
  return ['doc', 'docx'].includes(props.fileType.toLowerCase());
});

const isExcelDocument = computed(() => {
  return ['xls', 'xlsx'].includes(props.fileType.toLowerCase());
});

const isPdfDocument = computed(() => {
  return props.fileType.toLowerCase() === 'pdf';
});

const isPowerPointDocument = computed(() => {
  return ['ppt', 'pptx'].includes(props.fileType.toLowerCase());
});

const isImageFile = computed(() => {
  return ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(props.fileType.toLowerCase());
});

const isTextFile = computed(() => {
  return ['txt', 'json', 'xml', 'csv', 'log', 'md'].includes(props.fileType.toLowerCase());
});

const supportDownload = computed(() => {
  return true; // 所有文件都支持下载
});

// 获取文件类型标签颜色
const getFileTypeColor = (type: string): string => {
  const colorMap: { [key: string]: string } = {
    'pdf': 'danger',
    'doc': 'primary',
    'docx': 'primary',
    'xls': 'success',
    'xlsx': 'success',
    'ppt': 'warning',
    'pptx': 'warning',
    'jpg': 'info',
    'jpeg': 'info',
    'png': 'info',
    'gif': 'info',
    'txt': '',
    'json': '',
    'xml': ''
  };
  return colorMap[type.toLowerCase()] || '';
};

// 将base64转换为ArrayBuffer
const base64ToArrayBuffer = (base64: string): ArrayBuffer => {
  const binaryString = window.atob(base64);
  const bytes = new Uint8Array(binaryString.length);
  
  for (let i = 0; i < binaryString.length; i++) {
    bytes[i] = binaryString.charCodeAt(i);
  }
  
  return bytes.buffer;
};

// 创建图片URL
const createImageUrl = (base64: string): string => {
  const mimeType = getMimeType(props.fileType);
  return `data:${mimeType};base64,${base64}`;
};

// 获取MIME类型
const getMimeType = (fileType: string): string => {
  const mimeTypes: { [key: string]: string } = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'webp': 'image/webp',
    'txt': 'text/plain',
    'json': 'application/json',
    'xml': 'application/xml',
    'csv': 'text/csv'
  };
  return mimeTypes[fileType.toLowerCase()] || 'application/octet-stream';
};

// 初始化预览
const initPreview = async () => {
  try {
    isLoading.value = true;
    hasError.value = false;
    errorMessage.value = '';

    if (!props.fileData) {
      throw new Error('文件数据为空');
    }

    // 根据文件类型处理数据
    if (isWordDocument.value || isExcelDocument.value || isPdfDocument.value) {
      // Office文档和PDF需要ArrayBuffer格式
      fileArrayBuffer.value = base64ToArrayBuffer(props.fileData);
    } else if (isImageFile.value) {
      // 图片文件创建data URL
      imageUrl.value = createImageUrl(props.fileData);
    } else if (isTextFile.value) {
      // 文本文件直接解码
      const binaryString = window.atob(props.fileData);
      textContent.value = binaryString;
    }

    isLoading.value = false;
  } catch (error) {
    console.error('预览初始化失败:', error);
    hasError.value = true;
    errorMessage.value = error instanceof Error ? error.message : '未知错误';
    isLoading.value = false;
  }
};

// vue-office事件处理
const onRendered = () => {
  console.log('文档渲染完成');
  ElMessage.success('文档加载完成');
};

const onError = (error: any) => {
  console.error('文档渲染失败:', error);
  hasError.value = true;
  errorMessage.value = '文档渲染失败，可能是文件格式不支持或文件已损坏';
  ElMessage.error('文档预览失败');
};

// 重试预览
const retryPreview = () => {
  initPreview();
};

// 下载文件
const downloadFile = () => {
  try {
    const mimeType = getMimeType(props.fileType);
    const arrayBuffer = base64ToArrayBuffer(props.fileData);
    const blob = new Blob([arrayBuffer], { type: mimeType });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = props.fileName || `document.${props.fileType}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    
    URL.revokeObjectURL(url);
    ElMessage.success('文件下载成功');
  } catch (error) {
    console.error('文件下载失败:', error);
    ElMessage.error('文件下载失败');
  }
};

// 监听文件数据变化
watch(() => props.fileData, () => {
  if (props.fileData) {
    initPreview();
  }
}, { immediate: true });

// 组件挂载
onMounted(() => {
  if (props.fileData) {
    initPreview();
  }
});
</script>

<style lang="scss" scoped>
.vue-office-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .preview-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    
    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .file-name {
        font-weight: 500;
        color: #303133;
        max-width: 300px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
    
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .preview-content {
    flex: 1;
    overflow: hidden;
    
    .loading-container,
    .error-container,
    .unsupported-preview,
    .ppt-preview {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 600px;
      
      .el-icon {
        font-size: 48px;
        margin-bottom: 16px;
      }
      
      p {
        margin: 0;
        color: #909399;
        font-size: 16px;
      }
    }
    
    .image-preview {
      padding: 20px;
      text-align: center;
    }
    
    .text-preview {
      padding: 20px;
      height: 600px;
      overflow: auto;
      
      .text-content {
        background: #f5f5f5;
        padding: 16px;
        border-radius: 4px;
        font-family: 'Courier New', monospace;
        font-size: 14px;
        line-height: 1.5;
        white-space: pre-wrap;
        word-wrap: break-word;
        margin: 0;
      }
    }
  }
}

// vue-office组件样式调整
:deep(.vue-office-docx),
:deep(.vue-office-excel),
:deep(.vue-office-pdf) {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}
</style>
