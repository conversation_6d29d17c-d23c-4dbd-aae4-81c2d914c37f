// 项目资料管理相关的API接口和数据类型定义
import {
  queryCompanyFilesProject,
  queryDataDirectory,
  queryPageDataDirectory,
  download,
  exportPdf,
  deleteFile
} from '@/script/api/common/commomApi';

// 文档数据接口 - 对应真实API返回的数据结构
export interface DocumentData {
  id: string; // API返回的是字符串ID
  fileName: string; // 对应API返回的fileName
  fileDirectoryPath: string; // 对应API返回的fileDirectoryPath
  fileSize: string; // 对应API返回的fileSize
  createTime: string; // 对应API返回的createTime
  // 新增真实API字段
  fileType?: string; // 文件类型
  fileUrl?: string; // 文件URL
  uploadUser?: string; // 上传用户
  firstDirectoryId?: string; // 一级目录ID
  secondDirectoryId?: string; // 二级目录ID
  // 为了兼容现有组件，保留原有字段
  documentType?: string;
  documentName?: string;
  documentContent?: string;
  updater?: string;
  updateTime?: string;
  operation?: string;
  stageId?: string;
  status?: string;
}

// 查询参数接口 - 对应真实API参数结构
export interface DocumentQueryParams {
  pageNo?: number; // 对应API的pageNo
  pageSize?: number; // 对应API的pageSize
  projectId?: string; // 项目ID（可选，重置时不传）
  firstDirectoryId?: string; // 一级目录ID
  secondDirectoryId?: string; // 二级目录ID
  fileName?: string; // 文件名搜索
  sortField?: string;
  sortOrder?: 'asc' | 'desc';
  // 为了兼容现有组件，保留原有字段
  currentPage?: number;
  stageId?: string;
  searchKeyword?: string;
}

export interface DocumentListResponse {
  rows: DocumentData[];
  total: number;
  success: boolean;
}

// 树节点数据接口 - 对应真实API返回的目录结构
export interface TreeNodeData {
  label: string;
  id: string;
  children?: TreeNodeData[];
  isLeaf?: boolean;
  expanded?: boolean;
  // 懒加载相关字段
  hasChildren?: boolean; // 标记是否有子节点（用于懒加载）
  loading?: boolean; // 标记是否正在加载子节点
  isLoading?: boolean; // 标记是否是加载占位节点
  // 项目相关字段（用于公司-项目树）
  projectId?: string;
  shortName?: string;
  // 目录相关字段（用于目录树）
  firstDirectoryId?: string;
  firstDirectoryName?: string;
  secondDirectoryId?: string;
  secondDirectoryName?: string;
  secondDescribe?: string;
}

/**
 * 获取项目的完整目录结构（直接获取两级目录）
 * @param projectId 项目ID
 */
export const getProjectDirectoryTree = async (projectId: string): Promise<TreeNodeData[]> => {
  try {
    const response = await queryDataDirectory({ projectId });

    if (response.status === 0 && response.data?.directoryVOList) {
      // 转换目录结构数据 - 直接构建两级目录树
      const directories: TreeNodeData[] = response.data.directoryVOList.map((firstLevel: any) => ({
        label: firstLevel.firstDirectoryName, // 一级目录名称，如"工程建设前期卷"
        id: `firstdir_${projectId}_${firstLevel.firstDirectoryId}`,
        projectId: projectId,
        firstDirectoryId: firstLevel.firstDirectoryId,
        firstDirectoryName: firstLevel.firstDirectoryName,
        expanded: false,
        // 直接构建二级目录，不需要懒加载
        children: firstLevel.directoryVO?.map((secondLevel: any) => ({
          label: secondLevel.secondDirectoryName, // 二级目录名称
          id: `seconddir_${projectId}_${secondLevel.secondDirectoryId}`,
          projectId: projectId,
          firstDirectoryId: firstLevel.firstDirectoryId,
          firstDirectoryName: firstLevel.firstDirectoryName,
          secondDirectoryId: secondLevel.secondDirectoryId,
          secondDirectoryName: secondLevel.secondDirectoryName,
          secondDescribe: secondLevel.secondDescribe,
          isLeaf: true // 二级目录是叶子节点
        })) || []
      }));

      // 在最外层添加"全部"节点
      const allNode: TreeNodeData = {
        label: '全部',
        id: `all_${projectId}`,
        projectId: projectId,
        expanded: true, // 默认展开
        children: directories
      };

      return [allNode];
    }

    return [];
  } catch (error) {
    console.error(`获取项目 ${projectId} 目录失败:`, error);
    return [];
  }
};



/**
 * 获取项目资料数据 - 对接真实API
 * @param params 查询参数
 */
export const getDocumentList = async (params: DocumentQueryParams): Promise<DocumentListResponse> => {
  try {
    // 构建API参数
    const apiParams: any = {
      pageNo: params.pageNo || params.currentPage || 1,
      pageSize: params.pageSize || 10
    };

    // 只有在有projectId时才添加项目过滤
    if (params.projectId) {
      apiParams.projectId = params.projectId;
    }

    // 根据选中的目录添加过滤条件
    if (params.firstDirectoryId) {
      apiParams.firstDirectoryId = params.firstDirectoryId;
    }
    if (params.secondDirectoryId) {
      apiParams.secondDirectoryId = params.secondDirectoryId;
    }
    if (params.fileName || params.searchKeyword) {
      apiParams.fileName = params.fileName || params.searchKeyword;
    }

    const response = await queryPageDataDirectory(apiParams);

    if (response.status === 0 && response.data) {
      // 转换API返回的数据结构 - 使用records字段
      const rows: DocumentData[] = (response.data.records || []).map((item: any) => ({
        id: item.id,
        fileName: item.fileName,
        fileDirectoryPath: item.fileDirectoryPath,
        fileSize: item.fileSize,
        createTime: item.createTime,
        // 新增字段
        fileType: item.fileType,
        fileUrl: item.fileUrl,
        uploadUser: item.uploadUser,
        firstDirectoryId: item.firstDirectoryId,
        secondDirectoryId: item.secondDirectoryId,
        // 为了兼容现有组件，映射字段
        documentName: item.fileName,
        documentType: item.fileDirectoryPath,
        documentContent: item.fileName,
        updater: item.uploadUser || '系统',
        updateTime: item.createTime,
        operation: '查看 编辑 删除',
        stageId: params.firstDirectoryId || params.secondDirectoryId || 'all',
        status: 'active'
      }));

      return {
        rows,
        total: response.data.total || 0,
        success: true
      };
    }

    return {
      rows: [],
      total: 0,
      success: false
    };
  } catch (error) {
    console.error('获取项目资料数据失败:', error);
    return {
      rows: [],
      total: 0,
      success: false
    };
  }
};
/**
 * 删除项目资料 - 对接真实API
 * @param documentId 项目资料ID
 */
export const deleteDocument = async (documentId: string): Promise<{ success: boolean }> => {
  try {
    const response = await deleteFile({ id: documentId });
    return { success: response.status === 0 };
  } catch (error) {
    console.error('删除项目资料失败:', error);
    return { success: false };
  }
};
/**
 * 批量删除项目资料 - 对接真实API
 * @param documentIds 项目资料ID数组
 */
export const batchDeleteDocuments = async (documentIds: string[]): Promise<{ success: boolean }> => {
  try {
    // 逐个删除文件（如果API支持批量删除，可以调整为批量接口）
    const deletePromises = documentIds.map(id => deleteFile({ id }));
    const results = await Promise.all(deletePromises);

    // 检查是否所有删除操作都成功
    const allSuccess = results.every(result => result.status === 0);
    return { success: allSuccess };
  } catch (error) {
    console.error('批量删除项目资料失败:', error);
    return { success: false };
  }
};
/**
 * 导出项目资料数据 - 对接真实API
 * @param documentIds 项目资料ID数组，为空时导出全部
 * @param projectId 项目ID
 * @param projectName 项目名称
 */
export const exportDocuments = async (
  documentIds?: string[],
  projectId?: string,
  projectName?: string
): Promise<{ success: boolean; downloadUrl?: string }> => {
  try {
    if (documentIds && documentIds.length > 0) {
      // 批量下载选中的文件
      const idList = documentIds.join(',');
      const response = await download(idList);
      return { success: true, downloadUrl: response.data };
    } else if (projectId && projectName) {
      // 导出整个项目的PDF
      const response = await exportPdf(projectId, projectName);
      return { success: response.status === 0, downloadUrl: response.data };
    }

    return { success: false };
  } catch (error) {
    console.error('导出项目资料失败:', error);
    return { success: false };
  }
};
/**
 * 上传文件到指定目录 - 使用query参数
 * @param file 文件对象
 * @param projectId 项目ID
 * @param firstDirId 一级目录ID（可选）
 * @param secondDirId 二级目录ID（可选）
 */
export const uploadFileToDirectory = async (
  file: File,
  projectId: string,
  firstDirId?: string,
  secondDirId?: string
): Promise<any> => {
  try {
    // 导入service
    const service = (await import('@/script/utils/requests')).default;

    // 构建query参数
    const params = new URLSearchParams();
    params.append('projectId', projectId);
    if (firstDirId) {
      params.append('firstDirId', firstDirId);
    }
    if (secondDirId) {
      params.append('secondDirId', secondDirId);
    }

    // 构建FormData，只包含文件
    const formData = new FormData();
    formData.append('file', file);

    // 使用service直接调用，手动添加query参数
    // axios配置已经处理了FormData的Content-Type
    const response = await service.post(`archivesFile/upload?${params.toString()}`, formData);

    return response;
  } catch (error) {
    console.error('上传文件失败:', error);
    throw error;
  }
};

// 移除所有模拟数据，使用真实API接口
