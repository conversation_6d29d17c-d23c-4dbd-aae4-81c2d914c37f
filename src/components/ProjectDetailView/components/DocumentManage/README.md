# Vue-Office 文档预览功能

## 功能说明

基于 vue-office 库实现的本地文档预览功能，支持多种文件格式的在线预览，无需依赖外部服务。

## 支持的文件格式

### 完全支持
- **Word文档**: `.doc`, `.docx` - 使用 @vue-office/docx
- **Excel表格**: `.xls`, `.xlsx` - 使用 @vue-office/excel  
- **PDF文档**: `.pdf` - 使用 @vue-office/pdf
- **图片文件**: `.jpg`, `.jpeg`, `.png`, `.gif`, `.bmp`, `.webp`
- **文本文件**: `.txt`, `.json`, `.xml`, `.csv`, `.log`, `.md`

### 部分支持
- **PowerPoint**: `.ppt`, `.pptx` - 暂不支持，显示下载提示

## 技术实现

### 依赖包
```json
{
  "@vue-office/docx": "^1.6.3",
  "@vue-office/excel": "^1.7.14", 
  "@vue-office/pdf": "^2.0.10",
  "vue-demi": "^0.14.6"
}
```

### 组件结构
```
DocumentManage/
├── index.vue              # 主组件
└── VueOfficePreview.vue   # 预览组件
```

### 核心功能
1. **文件数据处理**: 将后端返回的base64数据转换为ArrayBuffer
2. **类型识别**: 根据文件扩展名自动选择预览组件
3. **错误处理**: 完善的加载状态和错误提示
4. **下载功能**: 支持文件下载

## 使用方法

### 基本使用
1. 在文档列表中点击"查看"按钮
2. 系统自动获取文件数据并打开预览弹窗
3. 根据文件类型自动选择合适的预览方式

### 预览界面
- **工具栏**: 显示文件名、类型标签、下载和关闭按钮
- **预览区域**: 根据文件类型显示对应的预览内容
- **状态提示**: 加载中、错误、不支持等状态的友好提示

## 配置说明

### Vite配置
确保在vite.config.ts中正确配置了依赖优化：

```typescript
export default defineConfig({
  optimizeDeps: {
    include: [
      '@vue-office/docx',
      '@vue-office/excel', 
      '@vue-office/pdf'
    ]
  }
})
```

### 样式导入
组件自动导入对应的CSS样式：
- `@vue-office/docx/lib/v3/index.css`
- `@vue-office/excel/lib/v3/index.css`
- `@vue-office/pdf/lib/v3/index.css`

## API接口

### 文件获取接口
```typescript
// GET /archivesFile/download?idList=${fileId}
// 返回格式:
{
  "status": "0",
  "msg": "恭喜！操作成功！", 
  "data": {
    "fileName": "文件名.pdf",
    "file": "base64编码的文件数据"
  }
}
```

## 事件处理

### vue-office组件事件
- `@rendered`: 文档渲染完成
- `@error`: 文档渲染失败

### 自定义事件
- `@close`: 关闭预览弹窗

## 错误处理

### 常见问题
1. **文件加载失败**: 检查base64数据是否完整
2. **预览组件加载失败**: 确认依赖包是否正确安装
3. **样式显示异常**: 检查CSS文件是否正确导入

### 错误提示
- 文件数据为空
- 文档渲染失败
- 文件类型不支持预览

## 性能优化

### 内存管理
- 组件销毁时自动清理资源
- 大文件预览时显示加载状态
- 错误情况下提供重试机制

### 加载优化
- 按需加载预览组件
- 样式文件按类型导入
- 文件数据缓存机制

## 浏览器兼容性

### 支持的浏览器
- Chrome 80+
- Firefox 75+
- Safari 13+
- Edge 80+

### 已知限制
- IE浏览器不支持
- 移动端浏览器支持有限

## 开发建议

### 扩展新格式
1. 在文件类型判断中添加新的扩展名
2. 实现对应的预览逻辑
3. 添加相应的错误处理

### 自定义样式
可以通过CSS变量或深度选择器自定义预览组件的样式：

```scss
:deep(.vue-office-docx) {
  // 自定义Word预览样式
}

:deep(.vue-office-excel) {
  // 自定义Excel预览样式  
}

:deep(.vue-office-pdf) {
  // 自定义PDF预览样式
}
```

## 注意事项

1. **文件大小限制**: 建议单个文件不超过50MB
2. **网络环境**: 本地预览不依赖网络，但文件获取需要网络连接
3. **安全考虑**: 所有文件数据通过HTTPS传输，客户端不持久化存储
4. **版本兼容**: 确保vue-office版本与Vue版本兼容

## 更新日志

### v1.0.0
- 基础预览功能实现
- 支持Word、Excel、PDF预览
- 图片和文本文件预览
- 完善的错误处理机制
