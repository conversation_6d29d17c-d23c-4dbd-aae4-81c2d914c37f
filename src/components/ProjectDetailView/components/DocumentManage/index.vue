<template>
  <div class="document-manage-container" ref="containerRef">
    <div
      class="document-manage-layout"
      :style="{ gridTemplateColumns: `${leftWidth}px 4px 1fr` }"
    >
      <!-- 左侧项目阶段树 -->
      <div class="tree-section">
        <div class="tree-header">
          <div class="tree-title">项目资料</div>
          <el-button type="primary" size="small" link @click="handleTreeReset">
            重置
          </el-button>
        </div>
        <div class="tree-container">
          <el-scrollbar class="tree-scrollbar">
            <el-tree
              ref="treeRef"
              :data="treeData"
              :props="treeProps"
              node-key="id"
              :expand-on-click-node="false"
              highlight-current
              :default-expand-all="true"
              @current-change="handleTreeNodeClick"
            >
            <template #default="{ node, data }">
              <div class="tree-node-content">
                <svg
                  style="margin-right: 8px"
                  viewBox="0 0 16 16"
                  width="16"
                  height="16"
                >
                  <path
                    :d="`${
                      data.isLeaf
                        ? 'M13,6 L9,6 L9,5 L9,2 L3,2 L3,14 L13,14 L13,6 Z M12.5857864,5 L10,2.41421356 L10,5 L12.5857864,5 Z M2,1 L10,1 L14,5 L14,15 L2,15 L2,1 Z'
                        : 'M16,6 L14,14 L2,14 L0,6 L16,6 Z M14.7192236,7 L1.28077641,7 L2.78077641,13 L13.2192236,13 L14.7192236,7 Z M6,1 L8,3 L15,3 L15,5 L14,5 L14,4 L7.58578644,4 L5.58578644,2 L2,2 L2,5 L1,5 L1,1 L6,1 Z'
                    }`"
                    stroke-width="1"
                    fill="#8a8e99"
                  ></path>
                </svg>
                <span>{{ node.label }}</span>
                <span v-if="data.loading || data.isLoading" class="tree-node-loading">
                  <el-icon class="is-loading"><Loading /></el-icon>
                </span>
                <span v-if="data.id.startsWith('seconddir_') && data.secondDescribe" class="tree-node-desc">
                  ({{ data.secondDescribe }})
                </span>
              </div>
            </template>
            </el-tree>
          </el-scrollbar>
        </div>
      </div>

      <!-- 拖拽分割条 -->
      <div class="resize-handle" @mousedown="handleMouseDown"></div>

      <!-- 右侧内容区域 -->
      <div class="content-section">


        <!-- 项目资料表格 -->
        <div class="document-list-section">
          <div class="table-container">
            <im-table
              ref="documentTableRef"
              :data="documentList"
              :columns="documentColumns"
              :pagination="paginationConfig"
              :loading="loading"
              toolbar
              :column-storage="createColumnStorage('document_manage', 'local')"
              :height="666"
              stripe
              border
              center
              show-index
              show-checkbox
              show-overflow-tooltip
              @on-page-change="handlePageChange"
              @on-page-size-change="handlePageSizeChange"
              @on-reload="handleReload"
              @sort-change="handleSortChange"
              @selection-change="handleSelectionChange"
            >
              <!-- 工具栏左侧 -->
              <template #toolbar-left="{ checkedRows }">
                <!-- <el-button
                  type="primary"
                  size="small"
                  :disabled="checkedRows.length === 0"
                  @click="handleBatchExport(checkedRows)"
                >
                  导出
                </el-button> -->
                <el-input
                  v-model="searchKeyword"
                  :placeholder="!queryParams.projectId ? '请先选择项目' : '请输入文件名关键字'"
                  :disabled="!queryParams.projectId"
                  class="search-input"
                  clearable
                  @keyup.enter="handleSearch"
                  @clear="handleSearchClear"
                >
                  <template #suffix>
                    <el-icon class="search-icon" @click="handleSearch">
                      <Search />
                    </el-icon>
                  </template>
                </el-input>
              </template>
              <!-- 工具栏右侧 -->
              <template #toolbar-right="{ checkedRows }">
                <el-button
                  type="primary"
                  size="small"
                  :disabled="!canUpload"
                  @click="handleUpload"
                >
                  上传
                </el-button>
                <el-button type="primary" size="small" :disabled="checkedRows.length === 0" @click="handleDownload">
                  批量下载
                </el-button>
                <el-button
                  type="primary"
                  size="small"
                  :disabled="checkedRows.length === 0"
                  @click="handleBatchDelete(checkedRows)"
                >
                  批量删除
                </el-button>
                <!-- <el-button
                  type="primary"
                  size="small"
                  :disabled="checkedRows.length === 0"
                  @click="handleBatchAnalysis(checkedRows)"
                >
                  批量解析
                </el-button> -->
              </template>

              <!-- 序号列 -->
              <template #sequence="{ row }">
                <span class="sequence-text">{{ row.sequence }}</span>
              </template>

              <!-- 资料名称列 -->
              <template #fileName="{ row }">
                <span
                  v-if="row.fileName"
                  class="file-name-link"
                  @click="handleDownloadFile(row)"
                  :title="row.fileName"
                >
                  {{ row.fileName }}
                </span>
                <span v-else class="no-file">-</span>
              </template>

              <!-- 位置列 -->
              <template #fileDirectoryPath="{ row }">
                <span class="directory-path-text">{{ row.fileDirectoryPath }}</span>
              </template>

              <!-- 文件大小列 -->
              <template #fileSize="{ row }">
                <span class="file-size-text">{{ row.fileSize ? `${row.fileSize}MB` : '-' }}</span>
              </template>

              <!-- 上传人列 -->
              <template #uploadUser="{ row }">
                <span class="upload-user-text">{{ row.uploadUser }}</span>
              </template>

              <!-- 上传时间列 -->
              <template #createTime="{ row }">
                <span class="create-time-text">{{ row.createTime }}</span>
              </template>

              <!-- 操作列 -->
              <template #operation="{ row }">
                <div class="operation-buttons">
                  <el-button
                    type="primary"
                    link
                    size="small"
                    @click="handleView(row)"
                  >
                    查看
                  </el-button>
                  <!-- <el-button
                    type="primary"
                    link
                    size="small"
                    @click="handleEdit(row)"
                  >
                    编辑
                  </el-button> -->
                  <el-button
                    type="danger"
                    link
                    size="small"
                    @click="handleDelete(row)"
                  >
                    删除
                  </el-button>
                </div>
              </template>
            </im-table>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 上传文件弹窗 -->
  <el-dialog
    v-model="uploadDialogVisible"
    title="上传文件"
    width="600px"
    :close-on-click-modal="false"
  >
    <div class="upload-dialog-content">
      <!-- 显示当前选择的目录层级 -->
      <div class="directory-info">
        <h4>当前选择目录：</h4>
        <div class="directory-path">
          <span v-if="currentUploadPath.firstDirectoryName" class="path-item">
            {{ currentUploadPath.firstDirectoryName }}
          </span>
          <span v-if="currentUploadPath.secondDirectoryName" class="path-separator"> / </span>
          <span v-if="currentUploadPath.secondDirectoryName" class="path-item">
            {{ currentUploadPath.secondDirectoryName }}
          </span>
        </div>
      </div>

      <!-- 文件上传组件 -->
      <div class="upload-section">
        <el-upload
          ref="uploadRef"
          action="#"
          v-model:file-list="fileList"
          :auto-upload="false"
          :multiple="true"
          :on-change="handleFileChange"
          :on-remove="handleFileRemove"
          :before-upload="beforeUpload"
          drag
        >
          <el-icon class="el-icon--upload"><upload-filled /></el-icon>
          <div class="el-upload__text">
            将文件拖到此处，或<em>点击上传</em>
          </div>
          <template #tip>
            <div class="el-upload__tip">
              支持多文件上传
            </div>
          </template>
        </el-upload>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="cancelUpload">取消</el-button>
        <el-button
          type="primary"
          :loading="uploading"
          :disabled="!canConfirmUpload"
          @click="confirmUpload"
        >
          {{ uploading ? '上传中...' : `确认上传(${fileList.length}个文件)` }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from "vue";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { Loading, UploadFilled } from "@element-plus/icons-vue";
import { Search } from "@element-plus/icons-vue";
import { createColumnStorage } from "@/components/ItsmCommon";
import { http } from "@/utils/http";
import {
  getDocumentList,
  exportDocuments,
  getProjectDirectoryTree,
  uploadFileToDirectory,
  type DocumentData,
  type DocumentQueryParams,
  type TreeNodeData
} from "./api";

// Props
interface Props {
  projectId?: string;
}

const props = defineProps<Props>();

// 响应式数据
const documentTableRef = ref();
const treeRef = ref();
const containerRef = ref();
const loading = ref(false);
const documentList = ref<DocumentData[]>([]);
const totalCount = ref(0);
const searchKeyword = ref("");
const selectedStageId = ref("");
const selectedRows = ref<DocumentData[]>([]);
const treeData = ref<TreeNodeData[]>([]);
const selectedProjectId = ref("");
const selectedNodeData = ref<TreeNodeData | null>(null);

// 布局相关
const leftWidth = ref(240);
const isDragging = ref(false);

// 树组件配置
const treeProps = {
  label: "label",
  children: "children"
};

// 查询参数
const queryParams = reactive<DocumentQueryParams>({
  pageNo: 1,
  pageSize: 10,
  projectId: selectedProjectId.value || props.projectId,
  // 为了兼容现有组件，保留原有字段
  currentPage: 1,
  stageId: ""
});

// 当前选中的目录信息
const currentDirectory = reactive({
  firstDirectoryId: '',
  secondDirectoryId: '',
  pathData: [] as any[]
});

// 上传相关状态
const uploadDialogVisible = ref(false);
const uploading = ref(false);
const fileList = ref<any[]>([]);
const uploadRef = ref();

// 当前上传路径信息
const currentUploadPath = reactive({
  firstDirectoryId: '',
  secondDirectoryId: '',
  firstDirectoryName: '',
  secondDirectoryName: ''
});

// 上传配置已移除，现在手动处理上传

// 分页配置
const paginationConfig = computed(() => ({
  currentPage: queryParams.currentPage || 1,
  pageSize: queryParams.pageSize || 10,
  total: totalCount.value,
  background: true,
  layout: 'total, sizes, prev, pager, next, jumper',
  pageSizes: [10, 20, 50, 100],
  hideOnEmptyData: false,
  align: 'right' as const
}));

// 是否可以上传（只有选中三级目录时才能上传）
const canUpload = computed(() => {
  return selectedStageId.value && selectedStageId.value.startsWith('seconddir_');
});

// 是否可以确认上传
const canConfirmUpload = computed(() => {
  console.log('文件列表长度:', fileList.value.length, '上传中:', uploading.value);
  return fileList.value.length > 0 && !uploading.value;
});



// 表格列配置
const documentColumns = [
  {
    prop: "fileName",
    label: "资料名称",
    minWidth: 200,
    align: "left",
    sortable: true,
    showOverflowTooltip: true
  },
  {
    prop: "fileDirectoryPath",
    label: "位置",
    width: 200,
    align: "left",
    sortable: false,
    showOverflowTooltip: true
  },
  {
    prop: "fileSize",
    label: "文件大小",
    width: 120,
    align: "center",
    sortable: false,
    formatter: (row: DocumentData) => {
      return row.fileSize ? `${row.fileSize}MB` : '-';
    }
  },
  {
    prop: "uploadUser",
    label: "上传人",
    width: 120,
    align: "center",
    sortable: false
  },
  {
    prop: "createTime",
    label: "上传时间",
    width: 180,
    align: "center",
    sortable: true
  },
  {
    prop: "operation",
    label: "操作",
    width: 200,
    align: "center",
    fixed: "right",
    sortable: false
  }
];

// 事件处理 - 参考其他页面的正确实现
const handlePageChange = async (pageOrSize: number, pageSize: number, query?: any) => {
  console.log('DocumentManage - 分页变化:', { pageOrSize, pageSize, query });
  // 根据im-table源码，这个事件会在页码变化和分页大小变化时都触发
  // 判断是页码变化还是分页大小变化
  if (pageOrSize !== queryParams.pageSize) {
    // 页码变化
    console.log('DocumentManage - 页码变化:', pageOrSize);
    queryParams.currentPage = pageOrSize;
    queryParams.pageNo = pageOrSize;
    await loadDocumentData();
  } else {
    // 分页大小变化，但不在这里处理，避免重复调用
    console.log('DocumentManage - 分页大小变化(在page-change中)，跳过处理');
  }
};

const handlePageSizeChange = async (currentPage: number, size: number, query?: any) => {
  console.log('DocumentManage - 分页大小变化:', { currentPage, size, query });
  queryParams.pageSize = size;
  queryParams.currentPage = 1; // 分页大小变化时重置到第一页
  queryParams.pageNo = 1;
  await loadDocumentData();
};

const handleReload = () => {
  console.log('DocumentManage - 刷新数据');
  loadDocumentData();
};



const handleSortChange = async ({ prop, order }: { prop: string; order: string }) => {
  if (order) {
    queryParams.sortField = prop;
    queryParams.sortOrder = order === "ascending" ? "asc" : "desc";
  } else {
    queryParams.sortField = undefined;
    queryParams.sortOrder = undefined;
  }
  queryParams.currentPage = 1;
  queryParams.pageNo = 1;
  await loadDocumentData();
};

// 上传相关函数
const handleUpload = () => {
  if (!canUpload.value) {
    ElMessage.warning('请先选择具体的目录');
    return;
  }

  // 设置当前上传路径信息
  if (selectedNodeData.value) {
    currentUploadPath.firstDirectoryId = selectedNodeData.value.firstDirectoryId || '';
    currentUploadPath.secondDirectoryId = selectedNodeData.value.secondDirectoryId || '';
    currentUploadPath.firstDirectoryName = selectedNodeData.value.firstDirectoryName || '';
    currentUploadPath.secondDirectoryName = selectedNodeData.value.secondDirectoryName || '';
  }

  uploadDialogVisible.value = true;
};

const handleFileChange = (file: any, newFileList: any[]) => {
  // 文件选择变化时的处理
  console.log('文件变化:', file, newFileList);
  fileList.value = newFileList;
};

const handleFileRemove = (file: any, newFileList: any[]) => {
  // 文件移除时的处理
  console.log('文件移除:', file, newFileList);
  fileList.value = newFileList;
};

const beforeUpload = (_file: any) => {
  // 移除文件大小限制
  return true;
};

const cancelUpload = () => {
  uploadDialogVisible.value = false;
  fileList.value = [];
  uploading.value = false;
  if (uploadRef.value) {
    uploadRef.value.clearFiles();
  }
};

const confirmUpload = async () => {
  if (fileList.value.length === 0) {
    ElMessage.warning('请选择要上传的文件');
    return;
  }

  try {
    uploading.value = true;

    // 使用新的uploadFileToDirectory函数逐个上传文件
    for (const fileItem of fileList.value) {
      // 使用新的上传函数，参数通过query传递
      const res = await uploadFileToDirectory(
        fileItem.raw,
        props.projectId,
        currentUploadPath.firstDirectoryId || undefined,
        currentUploadPath.secondDirectoryId || undefined
      );
      console.log('上传响应:', res);

      // 根据API返回的status字段判断是否成功
      if (res.status !== 0) {
        throw new Error(res.msg || '上传失败');
      }
    }

    ElMessage.success('文件上传成功');
    uploadDialogVisible.value = false;
    fileList.value = [];

    // 清理上传组件
    if (uploadRef.value) {
      uploadRef.value.clearFiles();
    }

    // 重新加载文档列表
    await loadDocumentData();

  } catch (error) {
    console.error('上传失败:', error);
    ElMessage.error('文件上传失败');
  } finally {
    uploading.value = false;
  }
};

const handleSelectionChange = (selection: DocumentData[]) => {
  selectedRows.value = selection;
  console.log("Selection changed:", selection);
};



const handleTreeNodeClick = (nodeData: TreeNodeData) => {
  selectedStageId.value = nodeData.id;
  selectedNodeData.value = nodeData;
  queryParams.stageId = nodeData.id;
  queryParams.currentPage = 1;
  queryParams.pageNo = 1;

  // 设置项目ID（从父组件传递的props）
  selectedProjectId.value = props.projectId;
  queryParams.projectId = props.projectId;

  // 判断节点类型
  if (nodeData.id.startsWith('all_')) {
    // 选择了"全部"节点，显示该项目的所有文档
    currentDirectory.firstDirectoryId = '';
    currentDirectory.secondDirectoryId = '';
    queryParams.firstDirectoryId = undefined;
    queryParams.secondDirectoryId = undefined;
    currentDirectory.pathData = [];

    loadDocumentData();
  } else if (nodeData.id.startsWith('firstdir_')) {
    // 选择了一级目录，使用firstDirectoryId参数
    currentDirectory.firstDirectoryId = nodeData.firstDirectoryId!;
    currentDirectory.secondDirectoryId = '';
    queryParams.firstDirectoryId = nodeData.firstDirectoryId;
    queryParams.secondDirectoryId = undefined;
    currentDirectory.pathData = [{
      label: nodeData.firstDirectoryName!,
      idName: 'firstDirectoryId',
      id: nodeData.firstDirectoryId!
    }];

    loadDocumentData();
  } else if (nodeData.id.startsWith('seconddir_')) {
    // 选择了二级目录，使用secondDirectoryId参数
    currentDirectory.firstDirectoryId = nodeData.firstDirectoryId!;
    currentDirectory.secondDirectoryId = nodeData.secondDirectoryId!;
    // 注意：根据你提供的API参数，二级目录只传secondDirectoryId，不传firstDirectoryId
    queryParams.firstDirectoryId = undefined;
    queryParams.secondDirectoryId = nodeData.secondDirectoryId;
    currentDirectory.pathData = [
      {
        label: nodeData.firstDirectoryName!,
        idName: 'firstDirectoryId',
        id: nodeData.firstDirectoryId!
      },
      {
        label: nodeData.secondDirectoryName!,
        idName: 'secondDirectoryId',
        id: nodeData.secondDirectoryId!,
        secondDescribe: nodeData.secondDescribe
      }
    ];

    loadDocumentData();
  }
};

const handleTreeReset = () => {
  // 重置所有状态
  selectedProjectId.value = props.projectId;
  queryParams.projectId = props.projectId;
  queryParams.currentPage = 1;
  queryParams.pageNo = 1;

  // 清空目录过滤
  currentDirectory.firstDirectoryId = '';
  currentDirectory.secondDirectoryId = '';
  queryParams.firstDirectoryId = undefined;
  queryParams.secondDirectoryId = undefined;
  currentDirectory.pathData = [];

  // 重新加载项目目录树
  loadProjectTree();

  // 设置树的当前选中节点为"全部"节点
  setTimeout(() => {
    if (treeRef.value && props.projectId) {
      const allNodeId = `all_${props.projectId}`;
      treeRef.value.setCurrentKey(allNodeId);
      selectedStageId.value = allNodeId;
      selectedNodeData.value = {
        label: '全部',
        id: allNodeId,
        projectId: props.projectId
      };
      queryParams.stageId = allNodeId;
    }
  }, 100);

  // 加载默认的文档列表（只传递基本分页参数）
  loadDefaultDocumentData();

  ElMessage.success("已重置");
};

const handleSearch = () => {
  if (!queryParams.projectId) {
    ElMessage.warning('请先选择项目');
    return;
  }

  // 搜索接口就是列表查询接口，只需要加一个fileName字段
  queryParams.fileName = searchKeyword.value;
  queryParams.currentPage = 1;
  queryParams.pageNo = 1;
  loadDocumentData();
};

const handleSearchClear = () => {
  // 清空搜索时，移除fileName参数并重新加载数据
  queryParams.fileName = undefined;
  queryParams.currentPage = 1;
  queryParams.pageNo = 1;
  loadDocumentData();
};

const handleView = (row: DocumentData) => {
  ElMessage.info(`查看文档: ${row.fileName}`);
};

const handleEdit = (row: DocumentData) => {
  ElMessage.info(`编辑文档: ${row.fileName}`);
};

const handleDelete = async (row: DocumentData) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文档"${row.fileName}"吗？`,
      "删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    // 显示删除提示
    const loading = ElLoading.service({
      lock: true,
      text: '正在删除文件...',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    try {
      // 使用指定的删除接口：/archivesFile/delete?id=(取当前行的 id) GET
      const response = await http.get(`/archivesFile/delete?id=${row.id}`) as any;

      loading.close();

      // 响应格式：{"status":"0","msg":"恭喜！操作成功！","data":"删除成功"}
      if (response.status === "0") {
        ElMessage.success(response.data || "删除成功");
        await loadDocumentData();
      } else {
        ElMessage.error(response.msg || "删除失败");
      }
    } catch (error) {
      loading.close();
      console.error("Delete error:", error);
      ElMessage.error("删除失败，请稍后重试");
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("Delete confirmation error:", error);
    }
  }
};

const handleBatchExport = async (rows: DocumentData[]) => {
  try {
    if (rows.length === 0) {
      ElMessage.warning("请选择要导出的数据");
      return;
    }
    const ids = rows.map(row => row.id);
    const result = await exportDocuments(ids, props.projectId, '项目资料');
    if (result.success) {
      ElMessage.success(`导出${rows.length}条项目资料记录成功`);
    } else {
      ElMessage.error("导出失败");
    }
  } catch (error) {
    ElMessage.error("导出失败");
    console.error("Export error:", error);
  }
};

// 批量下载功能
const handleDownload = async () => {
  // 获取当前选中的行
  const checkedRows = selectedRows.value;

  if (checkedRows.length === 0) {
    ElMessage.warning("请先选择要下载的文件");
    return;
  }

  try {
    await ElMessageBox.confirm(
      `确定要下载选中的${checkedRows.length}个文件吗？`,
      "批量下载确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }
    );

    // 显示批量下载提示
    const loading = ElLoading.service({
      lock: true,
      text: `正在下载${checkedRows.length}个文件...`,
      background: 'rgba(0, 0, 0, 0.7)',
    });

    try {
      let successCount = 0;
      let failCount = 0;

      // 使用for循环逐个执行下载
      for (const row of checkedRows) {
        try {
          // 使用相同的下载逻辑
          const response = await http.get(`/archivesFile/download?idList=${row.id}`) as any;

          if (response && response.data && response.data.file) {
            const { fileName, file } = response.data;

            // 将base64字符串转换为二进制数据
            const binaryString = window.atob(file);
            const binaryLen = binaryString.length;
            const bytes = new Uint8Array(binaryLen);

            for (let i = 0; i < binaryLen; i++) {
              bytes[i] = binaryString.charCodeAt(i);
            }

            // 根据文件扩展名确定MIME类型
            const getFileType = (filename: string) => {
              const ext = filename.toLowerCase().split('.').pop();
              const mimeTypes: { [key: string]: string } = {
                'pdf': 'application/pdf',
                'doc': 'application/msword',
                'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
                'xls': 'application/vnd.ms-excel',
                'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'png': 'image/png',
                'jpg': 'image/jpeg',
                'jpeg': 'image/jpeg',
                'gif': 'image/gif',
                'txt': 'text/plain',
                'zip': 'application/zip',
                'rar': 'application/x-rar-compressed'
              };
              return mimeTypes[ext || ''] || 'application/octet-stream';
            };

            // 创建Blob对象
            const fileBlob = new Blob([bytes], {
              type: getFileType(fileName || row.fileName || '')
            });

            // 创建下载链接
            const url = window.URL.createObjectURL(fileBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName || row.fileName || `attachment_${row.id}`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            successCount++;

            // 添加延迟避免浏览器阻止多个下载
            await new Promise(resolve => setTimeout(resolve, 500));
          } else {
            failCount++;
            console.error(`下载文件 ${row.fileName} 失败: 文件数据格式错误`);
          }
        } catch (error) {
          failCount++;
          console.error(`下载文件 ${row.fileName} 失败:`, error);
        }
      }

      loading.close();

      if (failCount === 0) {
        ElMessage.success(`成功下载${successCount}个文件`);
      } else if (successCount > 0) {
        ElMessage.warning(`下载完成：成功${successCount}个，失败${failCount}个`);
      } else {
        ElMessage.error("批量下载失败");
      }
    } catch (error) {
      loading.close();
      ElMessage.error("批量下载失败");
      console.error("Batch download error:", error);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("Batch download confirmation error:", error);
    }
  }
};

// 处理文件下载
const handleDownloadFile = async (row: DocumentData) => {
  if (!row.id) {
    ElMessage.warning('该记录没有关联的附件文件');
    return;
  }

  let loading: any = null;

  try {
    // 显示下载提示
    loading = ElLoading.service({
      lock: true,
      text: '正在下载文件...',
      background: 'rgba(0, 0, 0, 0.7)',
    });

    // 使用GET请求下载文件，不设置responseType，接收JSON数据
    const response = await http.get(`/archivesFile/download?idList=${row.id}`) as any;

    // 检查响应数据格式
    if (!response || !response.data || !response.data.file) {
      throw new Error('文件数据格式错误');
    }

    const { fileName, file } = response.data;

    // 将base64字符串转换为二进制数据
    const binaryString = window.atob(file);
    const binaryLen = binaryString.length;
    const bytes = new Uint8Array(binaryLen);

    for (let i = 0; i < binaryLen; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // 根据文件扩展名确定MIME类型
    const getFileType = (filename: string) => {
      const ext = filename.toLowerCase().split('.').pop();
      const mimeTypes: { [key: string]: string } = {
        'pdf': 'application/pdf',
        'doc': 'application/msword',
        'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'xls': 'application/vnd.ms-excel',
        'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'png': 'image/png',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'gif': 'image/gif',
        'txt': 'text/plain',
        'zip': 'application/zip',
        'rar': 'application/x-rar-compressed'
      };
      return mimeTypes[ext || ''] || 'application/octet-stream';
    };

    // 创建Blob对象
    const fileBlob = new Blob([bytes], {
      type: getFileType(fileName || row.fileName || '')
    });

    // 创建下载链接
    const url = window.URL.createObjectURL(fileBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName || row.fileName || `attachment_${row.id}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    loading.close();
    ElMessage.success('文件下载成功');
  } catch (error) {
    // 确保loading被关闭
    if (loading) {
      loading.close();
    }
    console.error('文件下载失败:', error);
    ElMessage.error('文件下载失败，请稍后重试');
  }
};

const handleBatchDelete = async (rows: DocumentData[]) => {
  try {
    if (rows.length === 0) {
      ElMessage.warning("请选择要删除的数据");
      return;
    }

    await ElMessageBox.confirm(
      `确定要删除选中的${rows.length}条项目资料吗？`,
      "批量删除确认",
      {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }
    );

    // 显示批量删除提示
    const loading = ElLoading.service({
      lock: true,
      text: `正在删除${rows.length}个文件...`,
      background: 'rgba(0, 0, 0, 0.7)',
    });

    try {
      let successCount = 0;
      let failCount = 0;

      // 使用for循环逐个执行删除接口
      for (const row of rows) {
        try {
          const response = await http.get(`/archivesFile/delete?id=${row.id}`) as any;
          // 响应格式：{"status":"0","msg":"恭喜！操作成功！","data":"删除成功"}
          if (response.status === "0") {
            successCount++;
          } else {
            failCount++;
            console.error(`删除文件 ${row.fileName} 失败:`, response.msg);
          }
        } catch (error) {
          failCount++;
          console.error(`删除文件 ${row.fileName} 失败:`, error);
        }
      }

      loading.close();

      if (failCount === 0) {
        ElMessage.success(`成功删除${successCount}条项目资料`);
      } else if (successCount > 0) {
        ElMessage.warning(`删除完成：成功${successCount}条，失败${failCount}条`);
      } else {
        ElMessage.error("批量删除失败");
      }

      // 重新加载数据
      await loadDocumentData();
    } catch (error) {
      loading.close();
      ElMessage.error("批量删除失败");
      console.error("Batch delete error:", error);
    }
  } catch (error) {
    if (error !== "cancel") {
      console.error("Batch delete confirmation error:", error);
    }
  }
};

const handleBatchAnalysis = (rows: DocumentData[]) => {
  if (rows.length === 0) {
    ElMessage.warning("请选择要解析的数据");
    return;
  }
  ElMessage.success(`开始解析${rows.length}条项目资料`);
  // 这里可以实现具体的批量解析逻辑
};

// 拖拽处理方法
const handleMouseDown = (e: MouseEvent) => {
  isDragging.value = true;
  const startX = e.clientX;
  const startWidth = leftWidth.value;

  const handleMouseMove = (e: MouseEvent) => {
    if (!isDragging.value) return;

    const deltaX = e.clientX - startX;
    const newWidth = startWidth + deltaX;

    // 限制最小和最大宽度
    const minWidth = 200;
    const maxWidth = containerRef.value
      ? containerRef.value.clientWidth * 0.6
      : 600;

    leftWidth.value = Math.max(minWidth, Math.min(maxWidth, newWidth));
  };

  const handleMouseUp = () => {
    isDragging.value = false;
    document.removeEventListener("mousemove", handleMouseMove);
    document.removeEventListener("mouseup", handleMouseUp);
    document.body.style.cursor = "";
    document.body.style.userSelect = "";
  };

  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
  document.body.style.cursor = "col-resize";
  document.body.style.userSelect = "none";
};

// 数据加载
const loadDocumentData = async () => {
  // 如果没有选择项目，不加载文档数据
  if (!queryParams.projectId) {
    documentList.value = [];
    totalCount.value = 0;
    return;
  }

  try {
    loading.value = true;
    const response = await getDocumentList(queryParams);
    if (response.success) {
      documentList.value = response.rows || [];
      totalCount.value = response.total || 0;
    }
  } catch (error) {
    ElMessage.error("加载项目资料数据失败");
    console.error("Load document data error:", error);
  } finally {
    loading.value = false;
  }
};

// 加载默认文档数据（带上projectId和基本分页参数）
const loadDefaultDocumentData = async () => {
  try {
    loading.value = true;
    // 构建包含projectId和基本分页参数的请求
    const defaultParams = {
      projectId: props.projectId,
      pageNo: 1,
      pageSize: 10
    };

    const response = await getDocumentList(defaultParams);
    if (response.success) {
      documentList.value = response.rows || [];
      totalCount.value = response.total || 0;
    }
  } catch (error) {
    ElMessage.error("加载默认数据失败");
    console.error("Load default document data error:", error);
  } finally {
    loading.value = false;
  }
};

// 加载项目目录树数据
const loadProjectTree = async () => {
  if (!props.projectId) {
    console.warn('项目ID为空，无法加载目录树');
    return;
  }

  try {
    const treeDataResult = await getProjectDirectoryTree(props.projectId);
    treeData.value = treeDataResult;
  } catch (error) {
    console.error('加载项目目录树失败:', error);
    ElMessage.error('加载项目目录树失败');
  }
};

// 生命周期
onMounted(async () => {
  // 加载项目目录树
  await loadProjectTree();

  // 如果有传入的项目ID，直接加载该项目的文档并选中"全部"节点
  if (props.projectId) {
    selectedProjectId.value = props.projectId;
    queryParams.projectId = props.projectId;

    // 设置默认选中"全部"节点
    setTimeout(() => {
      if (treeRef.value) {
        const allNodeId = `all_${props.projectId}`;
        treeRef.value.setCurrentKey(allNodeId);
        selectedStageId.value = allNodeId;
        selectedNodeData.value = {
          label: '全部',
          id: allNodeId,
          projectId: props.projectId
        };
        queryParams.stageId = allNodeId;
      }
    }, 100);

    await loadDocumentData();
  }
});

// 组件选项
defineOptions({
  name: "DocumentManage"
});
</script>

<style lang="scss" scoped>
.document-manage-container {
  padding: 0;
  height: 100%;
  overflow: hidden;

  .document-manage-layout {
    display: grid;
    height: 100%;
    gap: 0;

    .tree-section {
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(255, 255, 255, 0.1);
      border-radius: 6px;
      padding: 16px;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      height: 660px; // 设置整个树区域的固定高度

      .tree-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;

        .tree-title {
          color: var(--el-color-primary);
          font-size: 14px;
          font-weight: 400;
          padding-left: 8px;
          border-left: 3px solid var(--el-color-primary);
        }

        .el-button {
          font-size: 12px;
          padding: 4px 8px;
          height: auto;
          min-height: auto;
        }
      }

      .tree-container {
        height: 600px; // 设置固定高度600px
        overflow: hidden;

        .tree-scrollbar {
          height: 100%;

          :deep(.el-scrollbar__view) {
            padding: 0;
          }

          :deep(.el-scrollbar__bar) {
            &.is-vertical {
              right: 2px;
              width: 6px;

              .el-scrollbar__thumb {
                background-color: rgba(255, 255, 255, 0.3);
                border-radius: 3px;

                &:hover {
                  background-color: rgba(255, 255, 255, 0.5);
                }
              }
            }
          }
        }

        :deep(.el-tree) {
          background: transparent;

          .el-tree-node {
            .el-tree-node__content {
              color: rgba(255, 255, 255, 0.85);
              background: transparent;
              padding: 8px 4px;
              border-radius: 4px;

              &:hover {
                background-color: rgba(255, 255, 255, 0.08);
              }

              .tree-node-content {
                display: flex;
                align-items: center;

                .tree-node-loading {
                  margin-left: 8px;
                  color: rgba(255, 255, 255, 0.6);

                  .el-icon {
                    font-size: 12px;
                  }
                }

                .tree-node-desc {
                  margin-left: 4px;
                  color: rgba(255, 255, 255, 0.5);
                  font-size: 12px;
                }
              }
            }

            &.is-current > .el-tree-node__content {
              background-color: rgba(64, 158, 255, 0.2);
              color: var(--el-color-primary);
            }

            .el-tree-node__expand-icon {
              color: rgba(255, 255, 255, 0.65);

              &:hover {
                color: var(--el-color-primary);
              }
            }
          }
        }
      }
    }

    .resize-handle {
      width: 4px;
      background: transparent;
      cursor: col-resize;
      position: relative;

      &:hover {
        background: rgba(64, 158, 255, 0.3);
      }

      &:active {
        background: var(--el-color-primary);
      }

      &::before {
        content: "";
        position: absolute;
        left: -2px;
        right: -2px;
        top: 0;
        bottom: 0;
        background: transparent;
      }
    }

    .content-section {
      display: flex;
      flex-direction: column;
      overflow: hidden;
      min-width: 0;

      .search-section {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 16px;
        gap: 16px;

        .action-buttons {
          display: flex;
          gap: 8px;
        }
      }

      .search-input {
          width: 300px;
          margin-left: 16px;
          :deep(.el-input__wrapper) {
            background-color: rgba(255, 255, 255, 0.05);
            border: 1px solid rgba(255, 255, 255, 0.1);
            box-shadow: none;

            &:hover {
              border-color: rgba(255, 255, 255, 0.2);
            }

            &.is-focus {
              border-color: var(--el-color-primary);
            }
          }

          :deep(.el-input__inner) {
            color: rgba(255, 255, 255, 0.85);

            &::placeholder {
              color: rgba(255, 255, 255, 0.45);
            }
          }

          .search-icon {
            cursor: pointer;
            color: rgba(255, 255, 255, 0.65);

            &:hover {
              color: var(--el-color-primary);
            }
          }
        }

      .document-list-section {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;

        .table-container {
          flex: 1;
          overflow: hidden;
          .sequence-text,
          .document-name-text,
          .directory-path-text,
          .file-size-text,
          .upload-user-text,
          .create-time-text {
            color: rgba(255, 255, 255, 0.85);
            font-size: 13px;
          }
        }
      }
    }
  }

  // 上传弹窗样式
  .upload-dialog-content {
    .directory-info {
      margin-bottom: 20px;
      padding: 15px;
      background-color: #f5f7fa;
      border-radius: 6px;

      h4 {
        margin: 0 0 10px 0;
        color: #303133;
        font-size: 14px;
        font-weight: 600;
      }

      .directory-path {
        .path-item {
          color: #409eff;
          font-weight: 500;
        }

        .path-separator {
          color: #909399;
          margin: 0 8px;
        }
      }
    }

    .upload-section {
      .el-upload {
        width: 100%;
      }

      .el-upload-dragger {
        width: 100%;
        height: 180px;
        border: 2px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
        transition: border-color 0.2s;

        &:hover {
          border-color: #409eff;
        }
      }

      .el-icon--upload {
        font-size: 67px;
        color: #c0c4cc;
        margin: 40px 0 16px;
        line-height: 50px;
      }

      .el-upload__text {
        color: #606266;
        font-size: 14px;
        text-align: center;

        em {
          color: #409eff;
          font-style: normal;
        }
      }

      .el-upload__tip {
        font-size: 12px;
        color: #909399;
        margin-top: 7px;
        text-align: center;
      }
    }
  }

  .dialog-footer {
    text-align: right;

    .el-button {
      margin-left: 10px;
    }
  }

}

// 文件名链接样式
.file-name-link {
  color: var(--el-color-primary);
  cursor: pointer;
  text-decoration: underline;

  &:hover {
    color: var(--el-color-primary-light-3);
  }
}

.no-file {
  color: var(--el-text-color-placeholder);
}
</style>
