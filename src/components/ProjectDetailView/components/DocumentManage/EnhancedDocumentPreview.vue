<template>
  <div class="enhanced-document-preview">
    <!-- 预览工具栏 -->
    <div class="preview-toolbar">
      <div class="toolbar-left">
        <span class="file-info">{{ fileName }}</span>
        <el-tag :type="getFileTypeTag(fileType)" size="small">{{ fileType.toUpperCase() }}</el-tag>
      </div>
      <div class="toolbar-right">
        <el-button-group size="small">
          <el-button @click="zoomOut" :disabled="zoom <= 0.5">
            <el-icon><ZoomOut /></el-icon>
          </el-button>
          <el-button @click="resetZoom">{{ Math.round(zoom * 100) }}%</el-button>
          <el-button @click="zoomIn" :disabled="zoom >= 3">
            <el-icon><ZoomIn /></el-icon>
          </el-button>
        </el-button-group>
        <el-button size="small" @click="toggleFullscreen">
          <el-icon><FullScreen /></el-icon>
        </el-button>
        <el-button size="small" @click="$emit('close')">
          <el-icon><Close /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 预览内容区域 -->
    <div class="preview-content" :class="{ 'fullscreen': isFullscreen }">
      <!-- PDF预览 - 使用PDF.js -->
      <div v-if="isPDF" class="pdf-viewer">
        <div v-if="isLoading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>正在加载PDF...</p>
        </div>
        <canvas
          v-else
          ref="pdfCanvas"
          :style="{ transform: `scale(${zoom})`, transformOrigin: 'top left' }"
        ></canvas>
        
        <!-- PDF页面控制 -->
        <div v-if="!isLoading && totalPages > 1" class="pdf-controls">
          <el-button-group size="small">
            <el-button @click="prevPage" :disabled="currentPage <= 1">
              <el-icon><ArrowLeft /></el-icon>
            </el-button>
            <el-button disabled>{{ currentPage }} / {{ totalPages }}</el-button>
            <el-button @click="nextPage" :disabled="currentPage >= totalPages">
              <el-icon><ArrowRight /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>

      <!-- Office文档预览 - 使用iframe嵌入 -->
      <div v-else-if="isOfficeDocument" class="office-viewer">
        <div v-if="isLoading" class="loading-container">
          <el-icon class="is-loading"><Loading /></el-icon>
          <p>正在加载Office文档...</p>
        </div>
        
        <!-- 选择预览服务 -->
        <div class="preview-service-selector">
          <el-radio-group v-model="selectedPreviewService" size="small">
            <el-radio-button label="microsoft">Microsoft Office Online</el-radio-button>
            <el-radio-button label="google">Google Docs Viewer</el-radio-button>
            <el-radio-button label="local">本地预览</el-radio-button>
          </el-radio-group>
        </div>
        
        <iframe
          v-if="officePreviewUrl"
          :src="officePreviewUrl"
          width="100%"
          :height="iframeHeight"
          frameborder="0"
          @load="handleIframeLoad"
          @error="handleIframeError"
        ></iframe>
        
        <div v-else class="preview-fallback">
          <el-result
            icon="warning"
            title="预览服务不可用"
            sub-title="请尝试切换其他预览服务或下载文件查看"
          >
            <template #extra>
              <el-button type="primary" @click="$emit('download')">下载文件</el-button>
            </template>
          </el-result>
        </div>
      </div>

      <!-- 图片预览 -->
      <div v-else-if="isImage" class="image-viewer">
        <div class="image-container" :style="{ transform: `scale(${zoom})` }">
          <img :src="imageUrl" :alt="fileName" @load="handleImageLoad" @error="handleImageError" />
        </div>
      </div>

      <!-- 文本文件预览 -->
      <div v-else-if="isTextFile" class="text-viewer">
        <div class="text-container" :style="{ fontSize: `${14 * zoom}px` }">
          <pre class="text-content">{{ textContent }}</pre>
        </div>
      </div>

      <!-- 不支持的文件类型 -->
      <div v-else class="unsupported-viewer">
        <el-result
          icon="info"
          title="文件类型不支持预览"
          :sub-title="`文件类型: ${fileType.toUpperCase()}`"
        >
          <template #extra>
            <el-button type="primary" @click="$emit('download')">下载文件</el-button>
            <el-button @click="$emit('close')">关闭</el-button>
          </template>
        </el-result>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch, nextTick } from 'vue';
import { ElMessage } from 'element-plus';
import { 
  Loading, 
  ZoomOut, 
  ZoomIn, 
  FullScreen, 
  Close, 
  ArrowLeft, 
  ArrowRight 
} from '@element-plus/icons-vue';

// Props
interface Props {
  fileName: string;
  fileType: string;
  fileData: string; // base64编码的文件数据
}

const props = defineProps<Props>();

// Emits
const emit = defineEmits<{
  close: [];
  download: [];
}>();

// 响应式数据
const isLoading = ref(true);
const zoom = ref(1);
const isFullscreen = ref(false);
const selectedPreviewService = ref('microsoft');

// PDF相关
const pdfCanvas = ref<HTMLCanvasElement>();
const currentPage = ref(1);
const totalPages = ref(0);
const pdfDoc = ref<any>(null);

// Office文档相关
const officePreviewUrl = ref('');
const iframeHeight = ref(600);

// 图片相关
const imageUrl = ref('');

// 文本相关
const textContent = ref('');

// 计算属性
const isPDF = computed(() => props.fileType === 'pdf');
const isOfficeDocument = computed(() => {
  const officeTypes = ['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx'];
  return officeTypes.includes(props.fileType);
});
const isImage = computed(() => {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];
  return imageTypes.includes(props.fileType);
});
const isTextFile = computed(() => {
  const textTypes = ['txt', 'json', 'xml', 'csv', 'log', 'md'];
  return textTypes.includes(props.fileType);
});

// 获取文件类型标签颜色
const getFileTypeTag = (type: string): string => {
  const tagMap: { [key: string]: string } = {
    'pdf': 'danger',
    'doc': 'primary',
    'docx': 'primary',
    'xls': 'success',
    'xlsx': 'success',
    'ppt': 'warning',
    'pptx': 'warning',
    'jpg': 'info',
    'jpeg': 'info',
    'png': 'info',
    'gif': 'info',
    'txt': '',
    'json': '',
    'xml': ''
  };
  return tagMap[type] || '';
};

// 缩放控制
const zoomIn = () => {
  if (zoom.value < 3) {
    zoom.value = Math.min(3, zoom.value + 0.25);
  }
};

const zoomOut = () => {
  if (zoom.value > 0.5) {
    zoom.value = Math.max(0.5, zoom.value - 0.25);
  }
};

const resetZoom = () => {
  zoom.value = 1;
};

// 全屏切换
const toggleFullscreen = () => {
  isFullscreen.value = !isFullscreen.value;
  if (isFullscreen.value) {
    iframeHeight.value = window.innerHeight - 100;
  } else {
    iframeHeight.value = 600;
  }
};

// PDF相关方法
const loadPDF = async () => {
  try {
    // 这里需要引入PDF.js库
    // 由于PDF.js较大，建议通过CDN引入或npm安装
    ElMessage.info('PDF预览功能需要PDF.js库支持，请联系开发者配置');
    isLoading.value = false;
  } catch (error) {
    console.error('PDF加载失败:', error);
    ElMessage.error('PDF预览加载失败');
    isLoading.value = false;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    renderPDFPage();
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    renderPDFPage();
  }
};

const renderPDFPage = () => {
  // PDF页面渲染逻辑
  // 需要PDF.js支持
};

// Office文档预览
const setupOfficePreview = () => {
  const fileUrl = createFileUrl();
  
  switch (selectedPreviewService.value) {
    case 'microsoft':
      officePreviewUrl.value = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(fileUrl)}`;
      break;
    case 'google':
      officePreviewUrl.value = `https://docs.google.com/gview?url=${encodeURIComponent(fileUrl)}&embedded=true`;
      break;
    case 'local':
      // 本地预览逻辑
      ElMessage.warning('本地预览功能开发中');
      break;
  }
};

// 创建文件URL
const createFileUrl = (): string => {
  try {
    const mimeType = getMimeType(props.fileType);
    const binaryString = window.atob(props.fileData);
    const bytes = new Uint8Array(binaryString.length);
    
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    
    const blob = new Blob([bytes], { type: mimeType });
    return URL.createObjectURL(blob);
  } catch (error) {
    console.error('创建文件URL失败:', error);
    return '';
  }
};

// 获取MIME类型
const getMimeType = (fileType: string): string => {
  const mimeTypes: { [key: string]: string } = {
    'pdf': 'application/pdf',
    'doc': 'application/msword',
    'docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'xls': 'application/vnd.ms-excel',
    'xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    'ppt': 'application/vnd.ms-powerpoint',
    'pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
    'png': 'image/png',
    'jpg': 'image/jpeg',
    'jpeg': 'image/jpeg',
    'gif': 'image/gif',
    'bmp': 'image/bmp',
    'webp': 'image/webp',
    'txt': 'text/plain',
    'json': 'application/json',
    'xml': 'application/xml',
    'csv': 'text/csv'
  };
  return mimeTypes[fileType] || 'application/octet-stream';
};

// 事件处理
const handleIframeLoad = () => {
  isLoading.value = false;
};

const handleIframeError = () => {
  isLoading.value = false;
  ElMessage.error('文档预览加载失败');
};

const handleImageLoad = () => {
  isLoading.value = false;
};

const handleImageError = () => {
  isLoading.value = false;
  ElMessage.error('图片预览加载失败');
};

// 初始化预览
const initPreview = () => {
  isLoading.value = true;
  
  if (isPDF.value) {
    loadPDF();
  } else if (isOfficeDocument.value) {
    setupOfficePreview();
  } else if (isImage.value) {
    imageUrl.value = createFileUrl();
  } else if (isTextFile.value) {
    try {
      const binaryString = window.atob(props.fileData);
      textContent.value = binaryString;
      isLoading.value = false;
    } catch (error) {
      console.error('文本解析失败:', error);
      ElMessage.error('文本文件预览失败');
      isLoading.value = false;
    }
  } else {
    isLoading.value = false;
  }
};

// 监听预览服务变化
watch(selectedPreviewService, () => {
  if (isOfficeDocument.value) {
    setupOfficePreview();
  }
});

// 监听文件数据变化
watch(() => props.fileData, () => {
  if (props.fileData) {
    initPreview();
  }
}, { immediate: true });

onMounted(() => {
  // 监听ESC键退出全屏
  const handleKeydown = (e: KeyboardEvent) => {
    if (e.key === 'Escape' && isFullscreen.value) {
      toggleFullscreen();
    }
  };
  
  document.addEventListener('keydown', handleKeydown);
  
  return () => {
    document.removeEventListener('keydown', handleKeydown);
  };
});
</script>

<style lang="scss" scoped>
.enhanced-document-preview {
  display: flex;
  flex-direction: column;
  height: 100%;
  
  .preview-toolbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    background: #f5f7fa;
    border-bottom: 1px solid #e4e7ed;
    
    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 12px;
      
      .file-info {
        font-weight: 500;
        color: #303133;
      }
    }
    
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 8px;
    }
  }
  
  .preview-content {
    flex: 1;
    position: relative;
    overflow: auto;
    
    &.fullscreen {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 9999;
      background: white;
    }
    
    .loading-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      height: 400px;
      color: #909399;
      
      .el-icon {
        font-size: 32px;
        margin-bottom: 16px;
      }
    }
    
    .pdf-viewer {
      position: relative;
      text-align: center;
      padding: 20px;
      
      .pdf-controls {
        position: fixed;
        bottom: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        border-radius: 20px;
        padding: 8px;
      }
    }
    
    .office-viewer {
      .preview-service-selector {
        padding: 12px 16px;
        background: #f9f9f9;
        border-bottom: 1px solid #e4e7ed;
      }
      
      .preview-fallback {
        padding: 40px;
      }
    }
    
    .image-viewer {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 400px;
      padding: 20px;
      
      .image-container {
        transition: transform 0.3s ease;
        
        img {
          max-width: 100%;
          max-height: 80vh;
          object-fit: contain;
        }
      }
    }
    
    .text-viewer {
      padding: 20px;
      
      .text-container {
        transition: font-size 0.3s ease;
        
        .text-content {
          background: #f5f5f5;
          padding: 16px;
          border-radius: 4px;
          font-family: 'Courier New', monospace;
          line-height: 1.5;
          white-space: pre-wrap;
          word-wrap: break-word;
          margin: 0;
        }
      }
    }
    
    .unsupported-viewer {
      padding: 40px;
    }
  }
}
</style>
