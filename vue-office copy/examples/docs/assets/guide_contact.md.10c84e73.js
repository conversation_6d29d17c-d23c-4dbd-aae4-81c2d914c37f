import{_ as e,c as t,o as a,O as o}from"./chunks/framework.935eb42c.js";const f=JSON.parse('{"title":"联系我","description":"","frontmatter":{},"headers":[],"relativePath":"guide/contact.md"}'),s={name:"guide/contact.md"},i=o('<h1 id="联系我" tabindex="-1">联系我 <a class="header-anchor" href="#联系我" aria-label="Permalink to &quot;联系我&quot;">​</a></h1><h2 id="提issue" tabindex="-1">提Issue <a class="header-anchor" href="#提issue" aria-label="Permalink to &quot;提Issue&quot;">​</a></h2><p>如果您遇到了问题，欢迎提Issue，同时请您尽可能详细的描述您遇到的问题，包括不限于</p><ul><li>您使用的是哪个库： @vue-office/docx、@vue-office/excel、@vue-office/pdf</li><li>您使用的环境：APP or Web，PC or 移动端，如果是浏览器兼容问题，请提供您的浏览器版本</li><li>如果有错误，请粘贴详细的报错信息</li></ul><p>详细的描述有助于我尽快定位问题，因为平时工作很忙，时间很有限，感谢理解</p><h2 id="赞助和微信交流" tabindex="-1">赞助和微信交流 <a class="header-anchor" href="#赞助和微信交流" aria-label="Permalink to &quot;赞助和微信交流&quot;">​</a></h2><p><strong><em>如果该项目确实帮助到了您</em></strong>，欢迎赞助，以鼓励我将更多的休息时间，投入到该项目的优化中，也欢迎赞助后添加微信交流：_hit757_</p><img src="https://501351981.github.io/vue-office/examples/dist/static/wx.png" alt="赞助二维码" width="260"><div class="tip custom-block"><p class="custom-block-title">跪求一赞</p><p>如果您觉得该项目帮助了您，还请伸出贵手帮忙点赞支持，万分感谢~~</p></div>',9),c=[i];function r(l,n,d,_,u,p){return a(),t("div",null,c)}const m=e(s,[["render",r]]);export{f as __pageData,m as default};
