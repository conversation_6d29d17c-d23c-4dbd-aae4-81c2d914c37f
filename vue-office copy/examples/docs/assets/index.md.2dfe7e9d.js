import{_ as e,c as t,o as a}from"./chunks/framework.935eb42c.js";const x=JSON.parse('{"title":"","description":"","frontmatter":{"layout":"home","hero":{"name":"vue-office","text":"更易用的文件预览","tagline":"支持docx、xlsx、pdf文件预览","actions":[{"theme":"brand","text":"指南","link":"/guide/"},{"theme":"alt","text":"配置参考","link":"/config/"}]},"features":[{"title":"一站式","details":"提供docx、xlsx、pdf 3种文档的在线预览方案"},{"title":"使用简单","details":"只需提供文档的src即可完成文档预览，支持远程地址、ArrayBuffer、Blob多种格式"},{"title":"支持样式","details":"不仅能预览内容，也支持文档样式，最大限度还原office文件内容"}]},"headers":[],"relativePath":"index.md"}'),o={name:"index.md"};function i(n,r,s,c,d,l){return a(),t("div")}const p=e(o,[["render",i]]);export{x as __pageData,p as default};
