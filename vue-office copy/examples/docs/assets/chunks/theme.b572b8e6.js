import{d as g,o as a,c as i,r as u,n as M,a as I,t as P,_ as p,u as Qe,b as c,e as Je,f as Ae,g as Ze,h as x,i as et,j as tt,w as q,k as Q,l as k,m as nt,p as F,q as st,P as ot,s as he,v as K,x as ee,y as fe,z as r,F as S,A as y,B as v,T as pe,C as m,D as se,E as h,G as R,H as He,I as U,J as Ee,K as at,L as N,M as H,N as E,O as ct,Q as Be,R as me,S as oe,U as lt,V as J,W as ge,X as it,Y as rt,Z as ut,$ as dt,a0 as _t,a1 as vt}from"./framework.935eb42c.js";const ht=g({__name:"VPBadge",props:{text:null,type:null},setup(e){return(t,n)=>(a(),i("span",{class:M(["VPBadge",e.type??"tip"])},[u(t.$slots,"default",{},()=>[I(P(e.text),1)],!0)],2))}});const ft=p(ht,[["__scopeId","data-v-b935f744"]]),V=Qe;function ze(e){return et()?(tt(e),!0):!1}function De(e){return typeof e=="function"?e():c(e)}const pt=typeof window<"u",Fe=()=>{};function mt(...e){if(e.length!==1)return Je(...e);const t=e[0];return typeof t=="function"?Ae(Ze(()=>({get:t,set:Fe}))):x(t)}function gt(e){var t;const n=De(e);return(t=n==null?void 0:n.$el)!=null?t:n}const ye=pt?window:void 0;function yt(...e){let t,n,s,o;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,s,o]=e,t=ye):[t,n,s,o]=e,!t)return Fe;Array.isArray(n)||(n=[n]),Array.isArray(s)||(s=[s]);const l=[],d=()=>{l.forEach(L=>L()),l.length=0},f=(L,C,w,$)=>(L.addEventListener(C,w,$),()=>L.removeEventListener(C,w,$)),_=q(()=>[gt(t),De(o)],([L,C])=>{d(),L&&l.push(...n.flatMap(w=>s.map($=>f(L,w,$,C))))},{immediate:!0,flush:"post"}),b=()=>{_(),d()};return ze(b),b}function bt(){const e=x(!1);return nt()&&F(()=>{e.value=!0}),e}function kt(e){const t=bt();return k(()=>(t.value,!!e()))}function de(e,t={}){const{window:n=ye}=t,s=kt(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function");let o;const l=x(!1),d=()=>{o&&("removeEventListener"in o?o.removeEventListener("change",f):o.removeListener(f))},f=()=>{s.value&&(d(),o=n.matchMedia(mt(e).value),l.value=!!(o!=null&&o.matches),o&&("addEventListener"in o?o.addEventListener("change",f):o.addListener(f)))};return Q(f),ze(()=>d()),l}function $t({window:e=ye}={}){if(!e)return{x:x(0),y:x(0)};const t=x(e.scrollX),n=x(e.scrollY);return yt(e,"scroll",()=>{t.value=e.scrollX,n.value=e.scrollY},{capture:!1,passive:!0}),{x:t,y:n}}function Pt(e,t){let n,s=!1;return()=>{n&&clearTimeout(n),s?n=setTimeout(e,t):(e(),s=!0,setTimeout(()=>{s=!1},t))}}function _e(e){return/^\//.test(e)?e:`/${e}`}function Z(e){if(st(e))return e.replace(ot,"");const{site:t}=V(),{pathname:n,search:s,hash:o}=new URL(e,"http://example.com"),l=n.endsWith("/")||n.endsWith(".html")?e:e.replace(/(?:(^\.+)\/)?.*$/,`$1${n.replace(/(\.md)?$/,t.value.cleanUrls?"":".html")}${s}${o}`);return he(l)}function Oe(e,t){if(Array.isArray(e))return e;if(e==null)return[];t=_e(t);const n=Object.keys(e).sort((s,o)=>o.split("/").length-s.split("/").length).find(s=>t.startsWith(_e(s)));return n?e[n]:[]}function Vt(e){const t=[];let n=0;for(const s in e){const o=e[s];if(o.items){n=t.push(o);continue}t[n]||t.push({items:[]}),t[n].items.push(o)}return t}function wt(e){const t=[];function n(s){for(const o of s)o.text&&o.link&&t.push({text:o.text,link:o.link}),o.items&&n(o.items)}return n(e),t}function ve(e,t){return Array.isArray(t)?t.some(n=>ve(e,n)):K(e,t.link)?!0:t.items?ve(e,t.items):!1}function z(){const e=ee(),{theme:t,frontmatter:n}=V(),s=de("(min-width: 960px)"),o=x(!1),l=k(()=>{const B=t.value.sidebar,T=e.data.relativePath;return B?Oe(B,T):[]}),d=k(()=>n.value.sidebar!==!1&&l.value.length>0&&n.value.layout!=="home"),f=k(()=>_?n.value.aside==null?t.value.aside==="left":n.value.aside==="left":!1),_=k(()=>n.value.layout==="home"?!1:n.value.aside!=null?!!n.value.aside:t.value.aside!==!1),b=k(()=>d.value&&s.value),L=k(()=>d.value?Vt(l.value):[]);function C(){o.value=!0}function w(){o.value=!1}function $(){o.value?w():C()}return{isOpen:o,sidebar:l,sidebarGroups:L,hasSidebar:d,hasAside:_,leftAside:f,isSidebarEnabled:b,open:C,close:w,toggle:$}}function xt(e,t){let n;Q(()=>{n=e.value?document.activeElement:void 0}),F(()=>{window.addEventListener("keyup",s)}),fe(()=>{window.removeEventListener("keyup",s)});function s(o){o.key==="Escape"&&e.value&&(t(),n==null||n.focus())}}function St(e){const{page:t}=V(),n=x(!1),s=k(()=>e.value.collapsed!=null),o=k(()=>!!e.value.link),l=k(()=>K(t.value.relativePath,e.value.link)),d=k(()=>l.value?!0:e.value.items?ve(t.value.relativePath,e.value.items):!1),f=k(()=>!!(e.value.items&&e.value.items.length));Q(()=>{n.value=!!(s.value&&e.value.collapsed)}),Q(()=>{(l.value||d.value)&&(n.value=!1)});function _(){s.value&&(n.value=!n.value)}return{collapsed:n,collapsible:s,isLink:o,isActiveLink:l,hasActiveLink:d,hasChildren:f,toggle:_}}const Lt=g({__name:"VPSkipLink",setup(e){const t=ee(),n=x();q(()=>t.path,()=>n.value.focus());function s({target:o}){const l=document.querySelector(decodeURIComponent(o.hash));if(l){const d=()=>{l.removeAttribute("tabindex"),l.removeEventListener("blur",d)};l.setAttribute("tabindex","-1"),l.addEventListener("blur",d),l.focus(),window.scrollTo(0,0)}}return(o,l)=>(a(),i(S,null,[r("span",{ref_key:"backToTop",ref:n,tabindex:"-1"},null,512),r("a",{href:"#VPContent",class:"VPSkipLink visually-hidden",onClick:s}," Skip to content ")],64))}});const Mt=p(Lt,[["__scopeId","data-v-7b581ae5"]]),Ct={key:0,class:"VPBackdrop"},Bt=g({__name:"VPBackdrop",props:{show:{type:Boolean}},setup(e){return(t,n)=>(a(),y(pe,{name:"fade"},{default:v(()=>[e.show?(a(),i("div",Ct)):m("",!0)]),_:1}))}});const It=p(Bt,[["__scopeId","data-v-fc31a1ca"]]);function Nt(){const e=x(!1);function t(){e.value=!0,window.addEventListener("resize",o)}function n(){e.value=!1,window.removeEventListener("resize",o)}function s(){e.value?n():t()}function o(){window.outerWidth>=768&&n()}const l=ee();return q(()=>l.path,n),{isScreenOpen:e,openScreen:t,closeScreen:n,toggleScreen:s}}function te({removeCurrent:e=!0,correspondingLink:t=!1}={}){const{site:n,localeIndex:s,page:o,theme:l}=V(),d=k(()=>{var _,b;return{label:(_=n.value.locales[s.value])==null?void 0:_.label,link:((b=n.value.locales[s.value])==null?void 0:b.link)||(s.value==="root"?"/":`/${s.value}/`)}});return{localeLinks:k(()=>Object.entries(n.value.locales).flatMap(([_,b])=>e&&d.value.label===b.label?[]:{text:b.label,link:Tt(b.link||(_==="root"?"/":`/${_}/`),l.value.i18nRouting!==!1&&t,o.value.relativePath.slice(d.value.link.length-1),!n.value.cleanUrls)})),currentLang:d}}function Tt(e,t,n,s){return t?e.replace(/\/$/,"")+_e(n.replace(/(^|\/)?index.md$/,"$1").replace(/\.md$/,s?".html":"")):e}const At=["src","alt"],Ht={inheritAttrs:!1},Et=g({...Ht,__name:"VPImage",props:{image:null,alt:null},setup(e){return(t,n)=>{const s=R("VPImage",!0);return e.image?(a(),i(S,{key:0},[typeof e.image=="string"||"src"in e.image?(a(),i("img",se({key:0,class:"VPImage"},typeof e.image=="string"?t.$attrs:{...e.image,...t.$attrs},{src:c(he)(typeof e.image=="string"?e.image:e.image.src),alt:e.alt??(typeof e.image=="string"?"":e.image.alt||"")}),null,16,At)):(a(),i(S,{key:1},[h(s,se({class:"dark",image:e.image.dark,alt:e.image.alt},t.$attrs),null,16,["image","alt"]),h(s,se({class:"light",image:e.image.light,alt:e.image.alt},t.$attrs),null,16,["image","alt"])],64))],64)):m("",!0)}}});const be=p(Et,[["__scopeId","data-v-65783d3f"]]),zt=["href"],Dt=g({__name:"VPNavBarTitle",setup(e){const{site:t,theme:n}=V(),{hasSidebar:s}=z(),{currentLang:o}=te();return(l,d)=>(a(),i("div",{class:M(["VPNavBarTitle",{"has-sidebar":c(s)}])},[r("a",{class:"title",href:c(Z)(c(o).link)},[u(l.$slots,"nav-bar-title-before",{},void 0,!0),c(n).logo?(a(),y(be,{key:0,class:"logo",image:c(n).logo},null,8,["image"])):m("",!0),c(n).siteTitle?(a(),i(S,{key:1},[I(P(c(n).siteTitle),1)],64)):c(n).siteTitle===void 0?(a(),i(S,{key:2},[I(P(c(t).title),1)],64)):m("",!0),u(l.$slots,"nav-bar-title-after",{},void 0,!0)],8,zt)],2))}});const Ft=p(Dt,[["__scopeId","data-v-754524d5"]]);const Ot={type:"button",class:"DocSearch DocSearch-Button","aria-label":"Search"},Gt={class:"DocSearch-Button-Container"},Rt=r("svg",{class:"DocSearch-Search-Icon",width:"20",height:"20",viewBox:"0 0 20 20","aria-label":"search icon"},[r("path",{d:"M14.386 14.386l4.0877 4.0877-4.0877-4.0877c-2.9418 2.9419-7.7115 2.9419-10.6533 0-2.9419-2.9418-2.9419-7.7115 0-10.6533 2.9418-2.9419 7.7115-2.9419 10.6533 0 2.9419 2.9418 2.9419 7.7115 0 10.6533z",stroke:"currentColor",fill:"none","fill-rule":"evenodd","stroke-linecap":"round","stroke-linejoin":"round"})],-1),Ut={class:"DocSearch-Button-Placeholder"},jt=r("span",{class:"DocSearch-Button-Keys"},[r("kbd",{class:"DocSearch-Button-Key"}),r("kbd",{class:"DocSearch-Button-Key"},"K")],-1),Ie=g({__name:"VPNavBarSearchButton",props:{placeholder:null},setup(e){return(t,n)=>(a(),i("button",Ot,[r("span",Gt,[Rt,r("span",Ut,P(e.placeholder),1)]),jt]))}});const qt={id:"local-search"},Kt={key:1,id:"docsearch"},Wt=g({__name:"VPNavBarSearch",setup(e){const t=()=>null,n=()=>null,{theme:s,localeIndex:o}=V(),l=x(!1),d=k(()=>{var $,B,T,A,D,ne,W;const w=(($=s.value.search)==null?void 0:$.options)??s.value.algolia;return((D=(A=(T=(B=w==null?void 0:w.locales)==null?void 0:B[o.value])==null?void 0:T.translations)==null?void 0:A.button)==null?void 0:D.buttonText)||((W=(ne=w==null?void 0:w.translations)==null?void 0:ne.button)==null?void 0:W.buttonText)||"Search"});F(()=>{});function f(){l.value||(l.value=!0,setTimeout(_,16))}function _(){const w=new Event("keydown");w.key="k",w.metaKey=!0,window.dispatchEvent(w),setTimeout(()=>{document.querySelector(".DocSearch-Modal")||_()},16)}const b=x(!1),L=x("'Meta'");F(()=>{L.value=/(Mac|iPhone|iPod|iPad)/i.test(navigator.platform)?"'⌘'":"'Ctrl'"});const C="";return(w,$)=>{var B;return a(),i("div",{class:"VPNavBarSearch",style:He({"--vp-meta-key":L.value})},[c(C)==="local"?(a(),i(S,{key:0},[b.value?(a(),y(c(t),{key:0,placeholder:c(d),onClose:$[0]||($[0]=T=>b.value=!1)},null,8,["placeholder"])):m("",!0),r("div",qt,[h(Ie,{placeholder:c(d),onClick:$[1]||($[1]=T=>b.value=!0)},null,8,["placeholder"])])],64)):c(C)==="algolia"?(a(),i(S,{key:1},[l.value?(a(),y(c(n),{key:0,algolia:((B=c(s).search)==null?void 0:B.options)??c(s).algolia},null,8,["algolia"])):(a(),i("div",Kt,[h(Ie,{placeholder:c(d),onClick:f},null,8,["placeholder"])]))],64)):m("",!0)],4)}}});const Yt={},Xt={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",height:"24px",viewBox:"0 0 24 24",width:"24px"},Qt=r("path",{d:"M0 0h24v24H0V0z",fill:"none"},null,-1),Jt=r("path",{d:"M9 5v2h6.59L4 18.59 5.41 20 17 8.41V15h2V5H9z"},null,-1),Zt=[Qt,Jt];function en(e,t){return a(),i("svg",Xt,Zt)}const tn=p(Yt,[["render",en]]),nn=g({__name:"VPLink",props:{tag:null,href:null,noIcon:{type:Boolean},target:null,rel:null},setup(e){const t=e,n=k(()=>t.tag??t.href?"a":"span"),s=k(()=>t.href&&Ee.test(t.href));return(o,l)=>(a(),y(U(c(n)),{class:M(["VPLink",{link:e.href}]),href:e.href?c(Z)(e.href):void 0,target:e.target||(c(s)?"_blank":void 0),rel:e.rel||(c(s)?"noreferrer":void 0)},{default:v(()=>[u(o.$slots,"default",{},void 0,!0),c(s)&&!e.noIcon?(a(),y(tn,{key:0,class:"icon"})):m("",!0)]),_:3},8,["class","href","target","rel"]))}});const O=p(nn,[["__scopeId","data-v-3ef3daad"]]),sn=g({__name:"VPNavBarMenuLink",props:{item:null},setup(e){const{page:t}=V();return(n,s)=>(a(),y(O,{class:M({VPNavBarMenuLink:!0,active:c(K)(c(t).relativePath,e.item.activeMatch||e.item.link,!!e.item.activeMatch)}),href:e.item.link,target:e.item.target,rel:e.item.rel,tabindex:"0"},{default:v(()=>[I(P(e.item.text),1)]),_:1},8,["class","href","target","rel"]))}});const on=p(sn,[["__scopeId","data-v-5075bf50"]]),ke=x();let Ge=!1,ue=0;function an(e){const t=x(!1);if(at){!Ge&&cn(),ue++;const n=q(ke,s=>{var o,l,d;s===e.el.value||(o=e.el.value)!=null&&o.contains(s)?(t.value=!0,(l=e.onFocus)==null||l.call(e)):(t.value=!1,(d=e.onBlur)==null||d.call(e))});fe(()=>{n(),ue--,ue||ln()})}return Ae(t)}function cn(){document.addEventListener("focusin",Re),Ge=!0,ke.value=document.activeElement}function ln(){document.removeEventListener("focusin",Re)}function Re(){ke.value=document.activeElement}const rn={},un={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},dn=r("path",{d:"M12,16c-0.3,0-0.5-0.1-0.7-0.3l-6-6c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l5.3,5.3l5.3-5.3c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-6,6C12.5,15.9,12.3,16,12,16z"},null,-1),_n=[dn];function vn(e,t){return a(),i("svg",un,_n)}const Ue=p(rn,[["render",vn]]),hn={},fn={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},pn=r("circle",{cx:"12",cy:"12",r:"2"},null,-1),mn=r("circle",{cx:"19",cy:"12",r:"2"},null,-1),gn=r("circle",{cx:"5",cy:"12",r:"2"},null,-1),yn=[pn,mn,gn];function bn(e,t){return a(),i("svg",fn,yn)}const kn=p(hn,[["render",bn]]),$n={class:"VPMenuLink"},Pn=g({__name:"VPMenuLink",props:{item:null},setup(e){const{page:t}=V();return(n,s)=>(a(),i("div",$n,[h(O,{class:M({active:c(K)(c(t).relativePath,e.item.activeMatch||e.item.link,!!e.item.activeMatch)}),href:e.item.link,target:e.item.target,rel:e.item.rel},{default:v(()=>[I(P(e.item.text),1)]),_:1},8,["class","href","target","rel"])]))}});const ie=p(Pn,[["__scopeId","data-v-509e62c5"]]),Vn={class:"VPMenuGroup"},wn={key:0,class:"title"},xn=g({__name:"VPMenuGroup",props:{text:null,items:null},setup(e){return(t,n)=>(a(),i("div",Vn,[e.text?(a(),i("p",wn,P(e.text),1)):m("",!0),(a(!0),i(S,null,N(e.items,s=>(a(),i(S,null,["link"in s?(a(),y(ie,{key:0,item:s},null,8,["item"])):m("",!0)],64))),256))]))}});const Sn=p(xn,[["__scopeId","data-v-5d9efa4b"]]),Ln={class:"VPMenu"},Mn={key:0,class:"items"},Cn=g({__name:"VPMenu",props:{items:null},setup(e){return(t,n)=>(a(),i("div",Ln,[e.items?(a(),i("div",Mn,[(a(!0),i(S,null,N(e.items,s=>(a(),i(S,{key:s.text},["link"in s?(a(),y(ie,{key:0,item:s},null,8,["item"])):(a(),y(Sn,{key:1,text:s.text,items:s.items},null,8,["text","items"]))],64))),128))])):m("",!0),u(t.$slots,"default",{},void 0,!0)]))}});const Bn=p(Cn,[["__scopeId","data-v-d88c1780"]]),In=["aria-expanded","aria-label"],Nn={key:0,class:"text"},Tn={class:"menu"},An=g({__name:"VPFlyout",props:{icon:null,button:null,label:null,items:null},setup(e){const t=x(!1),n=x();an({el:n,onBlur:s});function s(){t.value=!1}return(o,l)=>(a(),i("div",{class:"VPFlyout",ref_key:"el",ref:n,onMouseenter:l[1]||(l[1]=d=>t.value=!0),onMouseleave:l[2]||(l[2]=d=>t.value=!1)},[r("button",{type:"button",class:"button","aria-haspopup":"true","aria-expanded":t.value,"aria-label":e.label,onClick:l[0]||(l[0]=d=>t.value=!t.value)},[e.button||e.icon?(a(),i("span",Nn,[e.icon?(a(),y(U(e.icon),{key:0,class:"option-icon"})):m("",!0),I(" "+P(e.button)+" ",1),h(Ue,{class:"text-icon"})])):(a(),y(kn,{key:1,class:"icon"}))],8,In),r("div",Tn,[h(Bn,{items:e.items},{default:v(()=>[u(o.$slots,"default",{},void 0,!0)]),_:3},8,["items"])])],544))}});const $e=p(An,[["__scopeId","data-v-e01ae258"]]),Hn=g({__name:"VPNavBarMenuGroup",props:{item:null},setup(e){const{page:t}=V();return(n,s)=>(a(),y($e,{class:M({VPNavBarMenuGroup:!0,active:c(K)(c(t).relativePath,e.item.activeMatch,!!e.item.activeMatch)}),button:e.item.text,items:e.item.items},null,8,["class","button","items"]))}}),En=e=>(H("data-v-56c387a0"),e=e(),E(),e),zn={key:0,"aria-labelledby":"main-nav-aria-label",class:"VPNavBarMenu"},Dn=En(()=>r("span",{id:"main-nav-aria-label",class:"visually-hidden"},"Main Navigation",-1)),Fn=g({__name:"VPNavBarMenu",setup(e){const{theme:t}=V();return(n,s)=>c(t).nav?(a(),i("nav",zn,[Dn,(a(!0),i(S,null,N(c(t).nav,o=>(a(),i(S,{key:o.text},["link"in o?(a(),y(on,{key:0,item:o},null,8,["item"])):(a(),y(Hn,{key:1,item:o},null,8,["item"]))],64))),128))])):m("",!0)}});const On=p(Fn,[["__scopeId","data-v-56c387a0"]]),Gn={},Rn={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},Un=r("path",{d:"M0 0h24v24H0z",fill:"none"},null,-1),jn=r("path",{d:" M12.87 15.07l-2.54-2.51.03-.03c1.74-1.94 2.98-4.17 3.71-6.53H17V4h-7V2H8v2H1v1.99h11.17C11.5 7.92 10.44 9.75 9 11.35 8.07 10.32 7.3 9.19 6.69 8h-2c.73 1.63 1.73 3.17 2.98 4.56l-5.09 5.02L4 19l5-5 3.11 3.11.76-2.04zM18.5 10h-2L12 22h2l1.12-3h4.75L21 22h2l-4.5-12zm-2.62 7l1.62-4.33L19.12 17h-3.24z ",class:"css-c4d79v"},null,-1),qn=[Un,jn];function Kn(e,t){return a(),i("svg",Rn,qn)}const je=p(Gn,[["render",Kn]]),Wn={class:"items"},Yn={class:"title"},Xn=g({__name:"VPNavBarTranslations",setup(e){const{theme:t}=V(),{localeLinks:n,currentLang:s}=te({correspondingLink:!0});return(o,l)=>c(n).length&&c(s).label?(a(),y($e,{key:0,class:"VPNavBarTranslations",icon:je,label:c(t).langMenuLabel||"Change language"},{default:v(()=>[r("div",Wn,[r("p",Yn,P(c(s).label),1),(a(!0),i(S,null,N(c(n),d=>(a(),y(ie,{key:d.link,item:d},null,8,["item"]))),128))])]),_:1},8,["label"])):m("",!0)}});const Qn=p(Xn,[["__scopeId","data-v-e1109ea6"]]);const Jn={},Zn={class:"VPSwitch",type:"button",role:"switch"},es={class:"check"},ts={key:0,class:"icon"};function ns(e,t){return a(),i("button",Zn,[r("span",es,[e.$slots.default?(a(),i("span",ts,[u(e.$slots,"default",{},void 0,!0)])):m("",!0)])])}const ss=p(Jn,[["render",ns],["__scopeId","data-v-e018e593"]]),os={},as={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},cs=ct('<path d="M12,18c-3.3,0-6-2.7-6-6s2.7-6,6-6s6,2.7,6,6S15.3,18,12,18zM12,8c-2.2,0-4,1.8-4,4c0,2.2,1.8,4,4,4c2.2,0,4-1.8,4-4C16,9.8,14.2,8,12,8z"></path><path d="M12,4c-0.6,0-1-0.4-1-1V1c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,3.6,12.6,4,12,4z"></path><path d="M12,24c-0.6,0-1-0.4-1-1v-2c0-0.6,0.4-1,1-1s1,0.4,1,1v2C13,23.6,12.6,24,12,24z"></path><path d="M5.6,6.6c-0.3,0-0.5-0.1-0.7-0.3L3.5,4.9c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C6.2,6.5,5.9,6.6,5.6,6.6z"></path><path d="M19.8,20.8c-0.3,0-0.5-0.1-0.7-0.3l-1.4-1.4c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l1.4,1.4c0.4,0.4,0.4,1,0,1.4C20.3,20.7,20,20.8,19.8,20.8z"></path><path d="M3,13H1c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S3.6,13,3,13z"></path><path d="M23,13h-2c-0.6,0-1-0.4-1-1s0.4-1,1-1h2c0.6,0,1,0.4,1,1S23.6,13,23,13z"></path><path d="M4.2,20.8c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C4.7,20.7,4.5,20.8,4.2,20.8z"></path><path d="M18.4,6.6c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l1.4-1.4c0.4-0.4,1-0.4,1.4,0s0.4,1,0,1.4l-1.4,1.4C18.9,6.5,18.6,6.6,18.4,6.6z"></path>',9),ls=[cs];function is(e,t){return a(),i("svg",as,ls)}const rs=p(os,[["render",is]]),us={},ds={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},_s=r("path",{d:"M12.1,22c-0.3,0-0.6,0-0.9,0c-5.5-0.5-9.5-5.4-9-10.9c0.4-4.8,4.2-8.6,9-9c0.4,0,0.8,0.2,1,0.5c0.2,0.3,0.2,0.8-0.1,1.1c-2,2.7-1.4,6.4,1.3,8.4c2.1,1.6,5,1.6,7.1,0c0.3-0.2,0.7-0.3,1.1-0.1c0.3,0.2,0.5,0.6,0.5,1c-0.2,2.7-1.5,5.1-3.6,6.8C16.6,21.2,14.4,22,12.1,22zM9.3,4.4c-2.9,1-5,3.6-5.2,6.8c-0.4,4.4,2.8,8.3,7.2,8.7c2.1,0.2,4.2-0.4,5.8-1.8c1.1-0.9,1.9-2.1,2.4-3.4c-2.5,0.9-5.3,0.5-7.5-1.1C9.2,11.4,8.1,7.7,9.3,4.4z"},null,-1),vs=[_s];function hs(e,t){return a(),i("svg",ds,vs)}const fs=p(us,[["render",hs]]),ps={title:"toggle dark mode"},ms=g({__name:"VPSwitchAppearance",setup(e){const{site:t,isDark:n}=V(),s=x(!1),o=typeof localStorage<"u"?l():()=>{};F(()=>{s.value=document.documentElement.classList.contains("dark")});function l(){const d=window.matchMedia("(prefers-color-scheme: dark)"),f=document.documentElement.classList;let _=localStorage.getItem(Be),b=t.value.appearance==="dark"&&_==null||(_==="auto"||_==null?d.matches:_==="dark");d.onchange=w=>{_==="auto"&&C(b=w.matches)};function L(){C(b=!b),_=b?d.matches?"auto":"dark":d.matches?"light":"auto",localStorage.setItem(Be,_)}function C(w){const $=document.createElement("style");$.type="text/css",$.appendChild(document.createTextNode(`:not(.VPSwitchAppearance):not(.VPSwitchAppearance *) {
  -webkit-transition: none !important;
  -moz-transition: none !important;
  -o-transition: none !important;
  -ms-transition: none !important;
  transition: none !important;
}`)),document.head.appendChild($),s.value=w,f[w?"add":"remove"]("dark"),window.getComputedStyle($).opacity,document.head.removeChild($)}return L}return q(s,d=>{n.value=d}),(d,f)=>(a(),i("label",ps,[h(ss,{class:"VPSwitchAppearance","aria-checked":s.value,onClick:c(o)},{default:v(()=>[h(rs,{class:"sun"}),h(fs,{class:"moon"})]),_:1},8,["aria-checked","onClick"])]))}});const Pe=p(ms,[["__scopeId","data-v-f8d4c7de"]]),gs={key:0,class:"VPNavBarAppearance"},ys=g({__name:"VPNavBarAppearance",setup(e){const{site:t}=V();return(n,s)=>c(t).appearance?(a(),i("div",gs,[h(Pe)])):m("",!0)}});const bs=p(ys,[["__scopeId","data-v-8cc85833"]]),ks={discord:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Discord</title><path d="M20.317 4.3698a19.7913 19.7913 0 00-4.8851-1.5152.0741.0741 0 00-.0785.0371c-.211.3753-.4447.8648-.6083 1.2495-1.8447-.2762-3.68-.2762-5.4868 0-.1636-.3933-.4058-.8742-.6177-1.2495a.077.077 0 00-.0785-.037 19.7363 19.7363 0 00-4.8852 1.515.0699.0699 0 00-.0321.0277C.5334 9.0458-.319 13.5799.0992 18.0578a.0824.0824 0 00.0312.0561c2.0528 1.5076 4.0413 2.4228 5.9929 3.0294a.0777.0777 0 00.0842-.0276c.4616-.6304.8731-1.2952 1.226-1.9942a.076.076 0 00-.0416-.1057c-.6528-.2476-1.2743-.5495-1.8722-.8923a.077.077 0 01-.0076-.1277c.1258-.0943.2517-.1923.3718-.2914a.0743.0743 0 01.0776-.0105c3.9278 1.7933 8.18 1.7933 12.0614 0a.0739.0739 0 01.0785.0095c.1202.099.246.1981.3728.2924a.077.077 0 01-.0066.1276 12.2986 12.2986 0 01-1.873.8914.0766.0766 0 00-.0407.1067c.3604.698.7719 1.3628 1.225 1.9932a.076.076 0 00.0842.0286c1.961-.6067 3.9495-1.5219 6.0023-3.0294a.077.077 0 00.0313-.0552c.5004-5.177-.8382-9.6739-3.5485-13.6604a.061.061 0 00-.0312-.0286zM8.02 15.3312c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9555-2.4189 2.157-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.9555 2.4189-2.1569 2.4189zm7.9748 0c-1.1825 0-2.1569-1.0857-2.1569-2.419 0-1.3332.9554-2.4189 2.1569-2.4189 1.2108 0 2.1757 1.0952 2.1568 2.419 0 1.3332-.946 2.4189-2.1568 2.4189Z"/></svg>',facebook:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Facebook</title><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>',github:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>GitHub</title><path d="M12 .297c-6.63 0-12 5.373-12 12 0 5.303 3.438 9.8 8.205 11.385.6.113.82-.258.82-.577 0-.285-.01-1.04-.015-2.04-3.338.724-4.042-1.61-4.042-1.61C4.422 18.07 3.633 17.7 3.633 17.7c-1.087-.744.084-.729.084-.729 1.205.084 1.838 1.236 1.838 1.236 1.07 1.835 2.809 1.305 3.495.998.108-.776.417-1.305.76-1.605-2.665-.3-5.466-1.332-5.466-5.93 0-1.31.465-2.38 1.235-3.22-.135-.303-.54-1.523.105-3.176 0 0 1.005-.322 3.3 1.23.96-.267 1.98-.399 3-.405 1.02.006 2.04.138 3 .405 2.28-1.552 3.285-1.23 3.285-1.23.645 1.653.24 2.873.12 3.176.765.84 1.23 1.91 1.23 3.22 0 4.61-2.805 5.625-5.475 5.92.42.36.81 1.096.81 2.22 0 1.606-.015 2.896-.015 3.286 0 .315.21.69.825.57C20.565 22.092 24 17.592 24 12.297c0-6.627-5.373-12-12-12"/></svg>',instagram:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Instagram</title><path d="M12 0C8.74 0 8.333.015 7.053.072 5.775.132 4.905.333 4.14.63c-.789.306-1.459.717-2.126 1.384S.935 3.35.63 4.14C.333 4.905.131 5.775.072 7.053.012 8.333 0 8.74 0 12s.015 3.667.072 4.947c.06 1.277.261 2.148.558 2.913.306.788.717 1.459 1.384 2.126.667.666 1.336 1.079 2.126 1.384.766.296 1.636.499 2.913.558C8.333 23.988 8.74 24 12 24s3.667-.015 4.947-.072c1.277-.06 2.148-.262 2.913-.558.788-.306 1.459-.718 2.126-1.384.666-.667 1.079-1.335 1.384-2.126.296-.765.499-1.636.558-2.913.06-1.28.072-1.687.072-4.947s-.015-3.667-.072-4.947c-.06-1.277-.262-2.149-.558-2.913-.306-.789-.718-1.459-1.384-2.126C21.319 1.347 20.651.935 19.86.63c-.765-.297-1.636-.499-2.913-.558C15.667.012 15.26 0 12 0zm0 2.16c3.203 0 3.585.016 4.85.071 1.17.055 1.805.249 2.227.415.562.217.96.477 1.382.896.419.42.679.819.896 1.381.164.422.36 1.057.413 2.227.057 1.266.07 1.646.07 4.85s-.015 3.585-.074 4.85c-.061 1.17-.256 1.805-.421 2.227-.224.562-.479.96-.899 1.382-.419.419-.824.679-1.38.896-.42.164-1.065.36-2.235.413-1.274.057-1.649.07-4.859.07-3.211 0-3.586-.015-4.859-.074-1.171-.061-1.816-.256-2.236-.421-.569-.224-.96-.479-1.379-.899-.421-.419-.69-.824-.9-1.38-.165-.42-.359-1.065-.42-2.235-.045-1.26-.061-1.649-.061-4.844 0-3.196.016-3.586.061-4.861.061-1.17.255-1.814.42-2.234.21-.57.479-.96.9-1.381.419-.419.81-.689 1.379-.898.42-.166 1.051-.361 2.221-.421 1.275-.045 1.65-.06 4.859-.06l.045.03zm0 3.678c-3.405 0-6.162 2.76-6.162 6.162 0 3.405 2.76 6.162 6.162 6.162 3.405 0 6.162-2.76 6.162-6.162 0-3.405-2.76-6.162-6.162-6.162zM12 16c-2.21 0-4-1.79-4-4s1.79-4 4-4 4 1.79 4 4-1.79 4-4 4zm7.846-10.405c0 .795-.646 1.44-1.44 1.44-.795 0-1.44-.646-1.44-1.44 0-.794.646-1.439 1.44-1.439.793-.001 1.44.645 1.44 1.439z"/></svg>',linkedin:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>LinkedIn</title><path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/></svg>',mastodon:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Mastodon</title><path d="M23.268 5.313c-.35-2.578-2.617-4.61-5.304-5.004C17.51.242 15.792 0 11.813 0h-.03c-3.98 0-4.835.242-5.288.309C3.882.692 1.496 2.518.917 5.127.64 6.412.61 7.837.661 9.143c.074 1.874.088 3.745.26 5.611.118 1.24.325 2.47.62 3.68.55 2.237 2.777 4.098 4.96 4.857 2.336.792 4.849.923 7.256.38.265-.061.527-.132.786-.213.585-.184 1.27-.39 1.774-.753a.057.057 0 0 0 .023-.043v-1.809a.052.052 0 0 0-.02-.041.053.053 0 0 0-.046-.01 20.282 20.282 0 0 1-4.709.545c-2.73 0-3.463-1.284-3.674-1.818a5.593 5.593 0 0 1-.319-1.433.053.053 0 0 1 .066-.054c1.517.363 3.072.546 4.632.546.376 0 .75 0 1.125-.01 1.57-.044 3.224-.124 4.768-.422.038-.008.077-.015.11-.024 2.435-.464 4.753-1.92 4.989-5.604.008-.145.03-1.52.03-1.67.002-.512.167-3.63-.024-5.545zm-3.748 9.195h-2.561V8.29c0-1.309-.55-1.976-1.67-1.976-1.23 0-1.846.79-1.846 2.35v3.403h-2.546V8.663c0-1.56-.617-2.35-1.848-2.35-1.112 0-1.668.668-1.67 1.977v6.218H4.822V8.102c0-1.31.337-2.35 1.011-3.12.696-.77 1.608-1.164 2.74-1.164 1.311 0 2.302.5 2.962 1.498l.638 1.06.638-1.06c.66-.999 1.65-1.498 2.96-1.498 1.13 0 2.043.395 2.74 1.164.675.77 1.012 1.81 1.012 3.12z"/></svg>',slack:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Slack</title><path d="M5.042 15.165a2.528 2.528 0 0 1-2.52 2.523A2.528 2.528 0 0 1 0 15.165a2.527 2.527 0 0 1 2.522-2.52h2.52v2.52zM6.313 15.165a2.527 2.527 0 0 1 2.521-2.52 2.527 2.527 0 0 1 2.521 2.52v6.313A2.528 2.528 0 0 1 8.834 24a2.528 2.528 0 0 1-2.521-2.522v-6.313zM8.834 5.042a2.528 2.528 0 0 1-2.521-2.52A2.528 2.528 0 0 1 8.834 0a2.528 2.528 0 0 1 2.521 2.522v2.52H8.834zM8.834 6.313a2.528 2.528 0 0 1 2.521 2.521 2.528 2.528 0 0 1-2.521 2.521H2.522A2.528 2.528 0 0 1 0 8.834a2.528 2.528 0 0 1 2.522-2.521h6.312zM18.956 8.834a2.528 2.528 0 0 1 2.522-2.521A2.528 2.528 0 0 1 24 8.834a2.528 2.528 0 0 1-2.522 2.521h-2.522V8.834zM17.688 8.834a2.528 2.528 0 0 1-2.523 2.521 2.527 2.527 0 0 1-2.52-2.521V2.522A2.527 2.527 0 0 1 15.165 0a2.528 2.528 0 0 1 2.523 2.522v6.312zM15.165 18.956a2.528 2.528 0 0 1 2.523 2.522A2.528 2.528 0 0 1 15.165 24a2.527 2.527 0 0 1-2.52-2.522v-2.522h2.52zM15.165 17.688a2.527 2.527 0 0 1-2.52-2.523 2.526 2.526 0 0 1 2.52-2.52h6.313A2.527 2.527 0 0 1 24 15.165a2.528 2.528 0 0 1-2.522 2.523h-6.313z"/></svg>',twitter:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>Twitter</title><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>',youtube:'<svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><title>YouTube</title><path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/></svg>'},$s=["href","aria-label","innerHTML"],Ps=g({__name:"VPSocialLink",props:{icon:null,link:null},setup(e){const t=e,n=k(()=>typeof t.icon=="object"?t.icon.svg:ks[t.icon]);return(s,o)=>(a(),i("a",{class:"VPSocialLink",href:e.link,"aria-label":typeof e.icon=="string"?e.icon:"",target:"_blank",rel:"noopener",innerHTML:c(n)},null,8,$s))}});const Vs=p(Ps,[["__scopeId","data-v-d7042ecc"]]),ws={class:"VPSocialLinks"},xs=g({__name:"VPSocialLinks",props:{links:null},setup(e){return(t,n)=>(a(),i("div",ws,[(a(!0),i(S,null,N(e.links,({link:s,icon:o})=>(a(),y(Vs,{key:s,icon:o,link:s},null,8,["icon","link"]))),128))]))}});const Ve=p(xs,[["__scopeId","data-v-3b7cc406"]]),Ss=g({__name:"VPNavBarSocialLinks",setup(e){const{theme:t}=V();return(n,s)=>c(t).socialLinks?(a(),y(Ve,{key:0,class:"VPNavBarSocialLinks",links:c(t).socialLinks},null,8,["links"])):m("",!0)}});const Ls=p(Ss,[["__scopeId","data-v-e020e990"]]),Ms={key:0,class:"group translations"},Cs={class:"trans-title"},Bs={key:1,class:"group"},Is={class:"item appearance"},Ns={class:"label"},Ts={class:"appearance-action"},As={key:2,class:"group"},Hs={class:"item social-links"},Es=g({__name:"VPNavBarExtra",setup(e){const{site:t,theme:n}=V(),{localeLinks:s,currentLang:o}=te({correspondingLink:!0}),l=k(()=>s.value.length&&o.value.label||t.value.appearance||n.value.socialLinks);return(d,f)=>c(l)?(a(),y($e,{key:0,class:"VPNavBarExtra",label:"extra navigation"},{default:v(()=>[c(s).length&&c(o).label?(a(),i("div",Ms,[r("p",Cs,P(c(o).label),1),(a(!0),i(S,null,N(c(s),_=>(a(),y(ie,{key:_.link,item:_},null,8,["item"]))),128))])):m("",!0),c(t).appearance?(a(),i("div",Bs,[r("div",Is,[r("p",Ns,P(c(n).darkModeSwitchLabel||"Appearance"),1),r("div",Ts,[h(Pe)])])])):m("",!0),c(n).socialLinks?(a(),i("div",As,[r("div",Hs,[h(Ve,{class:"social-links-list",links:c(n).socialLinks},null,8,["links"])])])):m("",!0)]),_:1})):m("",!0)}});const zs=p(Es,[["__scopeId","data-v-17654806"]]),Ds=e=>(H("data-v-db3267d6"),e=e(),E(),e),Fs=["aria-expanded"],Os=Ds(()=>r("span",{class:"container"},[r("span",{class:"top"}),r("span",{class:"middle"}),r("span",{class:"bottom"})],-1)),Gs=[Os],Rs=g({__name:"VPNavBarHamburger",props:{active:{type:Boolean}},emits:["click"],setup(e){return(t,n)=>(a(),i("button",{type:"button",class:M(["VPNavBarHamburger",{active:e.active}]),"aria-label":"mobile navigation","aria-expanded":e.active,"aria-controls":"VPNavScreen",onClick:n[0]||(n[0]=s=>t.$emit("click"))},Gs,10,Fs))}});const Us=p(Rs,[["__scopeId","data-v-db3267d6"]]),js=e=>(H("data-v-f3dacc09"),e=e(),E(),e),qs={class:"container"},Ks={class:"title"},Ws={class:"content"},Ys=js(()=>r("div",{class:"curtain"},null,-1)),Xs={class:"content-body"},Qs=g({__name:"VPNavBar",props:{isScreenOpen:{type:Boolean}},emits:["toggle-screen"],setup(e){const{y:t}=$t(),{hasSidebar:n}=z(),s=k(()=>({"has-sidebar":n.value,fill:t.value>0}));return(o,l)=>(a(),i("div",{class:M(["VPNavBar",c(s)])},[r("div",qs,[r("div",Ks,[h(Ft,null,{"nav-bar-title-before":v(()=>[u(o.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":v(()=>[u(o.$slots,"nav-bar-title-after",{},void 0,!0)]),_:3})]),r("div",Ws,[Ys,r("div",Xs,[u(o.$slots,"nav-bar-content-before",{},void 0,!0),h(Wt,{class:"search"}),h(On,{class:"menu"}),h(Qn,{class:"translations"}),h(bs,{class:"appearance"}),h(Ls,{class:"social-links"}),h(zs,{class:"extra"}),u(o.$slots,"nav-bar-content-after",{},void 0,!0),h(Us,{class:"hamburger",active:e.isScreenOpen,onClick:l[0]||(l[0]=d=>o.$emit("toggle-screen"))},null,8,["active"])])])])],2))}});const Js=p(Qs,[["__scopeId","data-v-f3dacc09"]]);function Zs(e){if(Array.isArray(e)){for(var t=0,n=Array(e.length);t<e.length;t++)n[t]=e[t];return n}else return Array.from(e)}var we=!1;if(typeof window<"u"){var Ne={get passive(){we=!0}};window.addEventListener("testPassive",null,Ne),window.removeEventListener("testPassive",null,Ne)}var ae=typeof window<"u"&&window.navigator&&window.navigator.platform&&(/iP(ad|hone|od)/.test(window.navigator.platform)||window.navigator.platform==="MacIntel"&&window.navigator.maxTouchPoints>1),j=[],ce=!1,xe=-1,Y=void 0,G=void 0,X=void 0,qe=function(t){return j.some(function(n){return!!(n.options.allowTouchMove&&n.options.allowTouchMove(t))})},le=function(t){var n=t||window.event;return qe(n.target)||n.touches.length>1?!0:(n.preventDefault&&n.preventDefault(),!1)},eo=function(t){if(X===void 0){var n=!!t&&t.reserveScrollBarGap===!0,s=window.innerWidth-document.documentElement.clientWidth;if(n&&s>0){var o=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right"),10);X=document.body.style.paddingRight,document.body.style.paddingRight=o+s+"px"}}Y===void 0&&(Y=document.body.style.overflow,document.body.style.overflow="hidden")},to=function(){X!==void 0&&(document.body.style.paddingRight=X,X=void 0),Y!==void 0&&(document.body.style.overflow=Y,Y=void 0)},no=function(){return window.requestAnimationFrame(function(){if(G===void 0){G={position:document.body.style.position,top:document.body.style.top,left:document.body.style.left};var t=window,n=t.scrollY,s=t.scrollX,o=t.innerHeight;document.body.style.position="fixed",document.body.style.top=-n,document.body.style.left=-s,setTimeout(function(){return window.requestAnimationFrame(function(){var l=o-window.innerHeight;l&&n>=o&&(document.body.style.top=-(n+l))})},300)}})},so=function(){if(G!==void 0){var t=-parseInt(document.body.style.top,10),n=-parseInt(document.body.style.left,10);document.body.style.position=G.position,document.body.style.top=G.top,document.body.style.left=G.left,window.scrollTo(n,t),G=void 0}},oo=function(t){return t?t.scrollHeight-t.scrollTop<=t.clientHeight:!1},ao=function(t,n){var s=t.targetTouches[0].clientY-xe;return qe(t.target)?!1:n&&n.scrollTop===0&&s>0||oo(n)&&s<0?le(t):(t.stopPropagation(),!0)},Ke=function(t,n){if(!t){console.error("disableBodyScroll unsuccessful - targetElement must be provided when calling disableBodyScroll on IOS devices.");return}if(!j.some(function(o){return o.targetElement===t})){var s={targetElement:t,options:n||{}};j=[].concat(Zs(j),[s]),ae?no():eo(n),ae&&(t.ontouchstart=function(o){o.targetTouches.length===1&&(xe=o.targetTouches[0].clientY)},t.ontouchmove=function(o){o.targetTouches.length===1&&ao(o,t)},ce||(document.addEventListener("touchmove",le,we?{passive:!1}:void 0),ce=!0))}},We=function(){ae&&(j.forEach(function(t){t.targetElement.ontouchstart=null,t.targetElement.ontouchmove=null}),ce&&(document.removeEventListener("touchmove",le,we?{passive:!1}:void 0),ce=!1),xe=-1),ae?so():to(),j=[]};const co=g({__name:"VPNavScreenMenuLink",props:{text:null,link:null},setup(e){const t=me("close-screen");return(n,s)=>(a(),y(O,{class:"VPNavScreenMenuLink",href:e.link,onClick:c(t)},{default:v(()=>[I(P(e.text),1)]),_:1},8,["href","onClick"]))}});const lo=p(co,[["__scopeId","data-v-e7cd1339"]]),io={},ro={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},uo=r("path",{d:"M18.9,10.9h-6v-6c0-0.6-0.4-1-1-1s-1,0.4-1,1v6h-6c-0.6,0-1,0.4-1,1s0.4,1,1,1h6v6c0,0.6,0.4,1,1,1s1-0.4,1-1v-6h6c0.6,0,1-0.4,1-1S19.5,10.9,18.9,10.9z"},null,-1),_o=[uo];function vo(e,t){return a(),i("svg",ro,_o)}const ho=p(io,[["render",vo]]),fo=g({__name:"VPNavScreenMenuGroupLink",props:{text:null,link:null},setup(e){const t=me("close-screen");return(n,s)=>(a(),y(O,{class:"VPNavScreenMenuGroupLink",href:e.link,onClick:c(t)},{default:v(()=>[I(P(e.text),1)]),_:1},8,["href","onClick"]))}});const Ye=p(fo,[["__scopeId","data-v-d590058b"]]),po={class:"VPNavScreenMenuGroupSection"},mo={key:0,class:"title"},go=g({__name:"VPNavScreenMenuGroupSection",props:{text:null,items:null},setup(e){return(t,n)=>(a(),i("div",po,[e.text?(a(),i("p",mo,P(e.text),1)):m("",!0),(a(!0),i(S,null,N(e.items,s=>(a(),y(Ye,{key:s.text,text:s.text,link:s.link},null,8,["text","link"]))),128))]))}});const yo=p(go,[["__scopeId","data-v-f0f12ef6"]]),bo=["aria-controls","aria-expanded"],ko={class:"button-text"},$o=["id"],Po={key:1,class:"group"},Vo=g({__name:"VPNavScreenMenuGroup",props:{text:null,items:null},setup(e){const t=e,n=x(!1),s=k(()=>`NavScreenGroup-${t.text.replace(" ","-").toLowerCase()}`);function o(){n.value=!n.value}return(l,d)=>(a(),i("div",{class:M(["VPNavScreenMenuGroup",{open:n.value}])},[r("button",{class:"button","aria-controls":c(s),"aria-expanded":n.value,onClick:o},[r("span",ko,P(e.text),1),h(ho,{class:"button-icon"})],8,bo),r("div",{id:c(s),class:"items"},[(a(!0),i(S,null,N(e.items,f=>(a(),i(S,{key:f.text},["link"in f?(a(),i("div",{key:f.text,class:"item"},[h(Ye,{text:f.text,link:f.link},null,8,["text","link"])])):(a(),i("div",Po,[h(yo,{text:f.text,items:f.items},null,8,["text","items"])]))],64))),128))],8,$o)],2))}});const wo=p(Vo,[["__scopeId","data-v-62e508bf"]]),xo={key:0,class:"VPNavScreenMenu"},So=g({__name:"VPNavScreenMenu",setup(e){const{theme:t}=V();return(n,s)=>c(t).nav?(a(),i("nav",xo,[(a(!0),i(S,null,N(c(t).nav,o=>(a(),i(S,{key:o.text},["link"in o?(a(),y(lo,{key:0,text:o.text,link:o.link},null,8,["text","link"])):(a(),y(wo,{key:1,text:o.text||"",items:o.items},null,8,["text","items"]))],64))),128))])):m("",!0)}}),Lo={key:0,class:"VPNavScreenAppearance"},Mo={class:"text"},Co=g({__name:"VPNavScreenAppearance",setup(e){const{site:t,theme:n}=V();return(s,o)=>c(t).appearance?(a(),i("div",Lo,[r("p",Mo,P(c(n).darkModeSwitchLabel||"Appearance"),1),h(Pe)])):m("",!0)}});const Bo=p(Co,[["__scopeId","data-v-6b98f783"]]),Io={class:"list"},No=g({__name:"VPNavScreenTranslations",setup(e){const{localeLinks:t,currentLang:n}=te({correspondingLink:!0}),s=x(!1);function o(){s.value=!s.value}return(l,d)=>c(t).length&&c(n).label?(a(),i("div",{key:0,class:M(["VPNavScreenTranslations",{open:s.value}])},[r("button",{class:"title",onClick:o},[h(je,{class:"icon lang"}),I(" "+P(c(n).label)+" ",1),h(Ue,{class:"icon chevron"})]),r("ul",Io,[(a(!0),i(S,null,N(c(t),f=>(a(),i("li",{key:f.link,class:"item"},[h(O,{class:"link",href:f.link},{default:v(()=>[I(P(f.text),1)]),_:2},1032,["href"])]))),128))])],2)):m("",!0)}});const To=p(No,[["__scopeId","data-v-393a5f09"]]),Ao=g({__name:"VPNavScreenSocialLinks",setup(e){const{theme:t}=V();return(n,s)=>c(t).socialLinks?(a(),y(Ve,{key:0,class:"VPNavScreenSocialLinks",links:c(t).socialLinks},null,8,["links"])):m("",!0)}}),Ho={class:"container"},Eo=g({__name:"VPNavScreen",props:{open:{type:Boolean}},setup(e){const t=x(null);function n(){Ke(t.value,{reserveScrollBarGap:!0})}function s(){We()}return(o,l)=>(a(),y(pe,{name:"fade",onEnter:n,onAfterLeave:s},{default:v(()=>[e.open?(a(),i("div",{key:0,class:"VPNavScreen",ref_key:"screen",ref:t},[r("div",Ho,[u(o.$slots,"nav-screen-content-before",{},void 0,!0),h(So,{class:"menu"}),h(To,{class:"translations"}),h(Bo,{class:"appearance"}),h(Ao,{class:"social-links"}),u(o.$slots,"nav-screen-content-after",{},void 0,!0)])],512)):m("",!0)]),_:3}))}});const zo=p(Eo,[["__scopeId","data-v-90abbaee"]]),Do={class:"VPNav"},Fo=g({__name:"VPNav",setup(e){const{isScreenOpen:t,closeScreen:n,toggleScreen:s}=Nt();return oe("close-screen",n),(o,l)=>(a(),i("header",Do,[h(Js,{"is-screen-open":c(t),onToggleScreen:c(s)},{"nav-bar-title-before":v(()=>[u(o.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":v(()=>[u(o.$slots,"nav-bar-title-after",{},void 0,!0)]),"nav-bar-content-before":v(()=>[u(o.$slots,"nav-bar-content-before",{},void 0,!0)]),"nav-bar-content-after":v(()=>[u(o.$slots,"nav-bar-content-after",{},void 0,!0)]),_:3},8,["is-screen-open","onToggleScreen"]),h(zo,{open:c(t)},{"nav-screen-content-before":v(()=>[u(o.$slots,"nav-screen-content-before",{},void 0,!0)]),"nav-screen-content-after":v(()=>[u(o.$slots,"nav-screen-content-after",{},void 0,!0)]),_:3},8,["open"])]))}});const Oo=p(Fo,[["__scopeId","data-v-52d944d6"]]),Go={},Ro={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},Uo=r("path",{d:"M17,11H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,11,17,11z"},null,-1),jo=r("path",{d:"M21,7H3C2.4,7,2,6.6,2,6s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,7,21,7z"},null,-1),qo=r("path",{d:"M21,15H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h18c0.6,0,1,0.4,1,1S21.6,15,21,15z"},null,-1),Ko=r("path",{d:"M17,19H3c-0.6,0-1-0.4-1-1s0.4-1,1-1h14c0.6,0,1,0.4,1,1S17.6,19,17,19z"},null,-1),Wo=[Uo,jo,qo,Ko];function Yo(e,t){return a(),i("svg",Ro,Wo)}const Xo=p(Go,[["render",Yo]]);function Qo(){const{hasSidebar:e}=z(),t=de("(min-width: 960px)"),n=de("(min-width: 1280px)");return{isAsideEnabled:k(()=>!n.value&&!t.value?!1:e.value?n.value:t.value)}}const Jo=71;function Se(e){return typeof e.outline=="object"&&!Array.isArray(e.outline)&&e.outline.label||e.outlineTitle||"On this page"}function Le(e){const t=[...document.querySelectorAll(".VPDoc h2,h3,h4,h5,h6")].filter(n=>n.id&&n.hasChildNodes()).map(n=>{const s=Number(n.tagName[1]);return{title:Zo(n),link:"#"+n.id,level:s}});return ea(t,e)}function Zo(e){let t="";for(const n of e.childNodes)if(n.nodeType===1){if(n.classList.contains("VPBadge")||n.classList.contains("header-anchor"))continue;t+=n.textContent}else n.nodeType===3&&(t+=n.textContent);return t.trim()}function ea(e,t){if(t===!1)return[];const n=(typeof t=="object"&&!Array.isArray(t)?t.level:t)||2,[s,o]=typeof n=="number"?[n,n]:n==="deep"?[2,6]:n;e=e.filter(d=>d.level>=s&&d.level<=o);const l=[];e:for(let d=0;d<e.length;d++){const f=e[d];if(d===0)l.push(f);else{for(let _=d-1;_>=0;_--){const b=e[_];if(b.level<f.level){(b.children||(b.children=[])).push(f);continue e}}l.push(f)}}return l}function ta(e,t){const{isAsideEnabled:n}=Qo(),s=Pt(l,100);let o=null;F(()=>{requestAnimationFrame(l),window.addEventListener("scroll",s)}),lt(()=>{d(location.hash)}),fe(()=>{window.removeEventListener("scroll",s)});function l(){if(!n.value)return;const f=[].slice.call(e.value.querySelectorAll(".outline-link")),_=[].slice.call(document.querySelectorAll(".content .header-anchor")).filter($=>f.some(B=>B.hash===$.hash&&$.offsetParent!==null)),b=window.scrollY,L=window.innerHeight,C=document.body.offsetHeight,w=Math.abs(b+L-C)<1;if(_.length&&w){d(_[_.length-1].hash);return}for(let $=0;$<_.length;$++){const B=_[$],T=_[$+1],[A,D]=na($,B,T);if(A){d(D);return}}}function d(f){o&&o.classList.remove("active"),f!==null&&(o=e.value.querySelector(`a[href="${decodeURIComponent(f)}"]`));const _=o;_?(_.classList.add("active"),t.value.style.top=_.offsetTop+33+"px",t.value.style.opacity="1"):(t.value.style.top="33px",t.value.style.opacity="0")}}function Te(e){return e.parentElement.offsetTop-Jo}function na(e,t,n){const s=window.scrollY;return e===0&&s===0?[!0,null]:s<Te(t)?[!1,null]:!n||s<Te(n)?[!0,t.hash]:[!1,null]}const sa=["href","title"],oa=g({__name:"VPDocOutlineItem",props:{headers:null,root:{type:Boolean}},setup(e){function t({target:n}){const s="#"+n.href.split("#")[1],o=document.querySelector(decodeURIComponent(s));o==null||o.focus()}return(n,s)=>{const o=R("VPDocOutlineItem",!0);return a(),i("ul",{class:M(e.root?"root":"nested")},[(a(!0),i(S,null,N(e.headers,({children:l,link:d,title:f})=>(a(),i("li",null,[r("a",{class:"outline-link",href:d,onClick:t,title:f},P(f),9,sa),l!=null&&l.length?(a(),y(o,{key:0,headers:l},null,8,["headers"])):m("",!0)]))),256))],2)}}});const Me=p(oa,[["__scopeId","data-v-51078a54"]]),aa={},ca={xmlns:"http://www.w3.org/2000/svg","aria-hidden":"true",focusable:"false",viewBox:"0 0 24 24"},la=r("path",{d:"M9,19c-0.3,0-0.5-0.1-0.7-0.3c-0.4-0.4-0.4-1,0-1.4l5.3-5.3L8.3,6.7c-0.4-0.4-0.4-1,0-1.4s1-0.4,1.4,0l6,6c0.4,0.4,0.4,1,0,1.4l-6,6C9.5,18.9,9.3,19,9,19z"},null,-1),ia=[la];function ra(e,t){return a(),i("svg",ca,ia)}const Ce=p(aa,[["render",ra]]),ua=g({__name:"VPLocalNavOutlineDropdown",setup(e){const{frontmatter:t,theme:n}=V(),s=x(!1),o=x(0),l=x();J(()=>{s.value=!1});function d(){s.value=!s.value,o.value=window.innerHeight+Math.min(window.scrollY-64,0)}function f(L){L.target.classList.contains("outline-link")&&(l.value&&(l.value.style.transition="none"),it(()=>{s.value=!1}))}function _(){s.value=!1,window.scrollTo({top:0,left:0,behavior:"smooth"})}const b=ge([]);return J(()=>{b.value=Le(t.value.outline??n.value.outline)}),(L,C)=>(a(),i("div",{class:"VPLocalNavOutlineDropdown",style:He({"--vp-vh":o.value+"px"})},[c(b).length>0?(a(),i("button",{key:0,onClick:d,class:M({open:s.value})},[I(P(c(Se)(c(n)))+" ",1),h(Ce,{class:"icon"})],2)):(a(),i("button",{key:1,onClick:_},P(c(n).returnToTopLabel||"Return to top"),1)),h(pe,{name:"flyout"},{default:v(()=>[s.value?(a(),i("div",{key:0,ref_key:"items",ref:l,class:"items",onClick:f},[r("a",{class:"top-link",href:"#",onClick:_},P(c(n).returnToTopLabel||"Return to top"),1),h(Me,{headers:c(b)},null,8,["headers"])],512)):m("",!0)]),_:1})],4))}});const da=p(ua,[["__scopeId","data-v-539129c1"]]),_a={key:0,class:"VPLocalNav"},va=["aria-expanded"],ha={class:"menu-text"},fa=g({__name:"VPLocalNav",props:{open:{type:Boolean}},emits:["open-menu"],setup(e){const{theme:t}=V(),{hasSidebar:n}=z();return(s,o)=>c(n)?(a(),i("div",_a,[r("button",{class:"menu","aria-expanded":e.open,"aria-controls":"VPSidebarNav",onClick:o[0]||(o[0]=l=>s.$emit("open-menu"))},[h(Xo,{class:"menu-icon"}),r("span",ha,P(c(t).sidebarMenuLabel||"Menu"),1)],8,va),h(da)])):m("",!0)}});const pa=p(fa,[["__scopeId","data-v-b003dbae"]]),ma=e=>(H("data-v-93d36c98"),e=e(),E(),e),ga=["role","tabindex"],ya=ma(()=>r("div",{class:"indicator"},null,-1)),ba=["onKeydown"],ka={key:1,class:"items"},$a=g({__name:"VPSidebarItem",props:{item:null,depth:null},setup(e){const t=e,{collapsed:n,collapsible:s,isLink:o,isActiveLink:l,hasActiveLink:d,hasChildren:f,toggle:_}=St(k(()=>t.item)),b=k(()=>f.value?"section":"div"),L=k(()=>o.value?"a":"div"),C=k(()=>f.value?t.depth+2===7?"p":`h${t.depth+2}`:"p"),w=k(()=>o.value?void 0:"button"),$=k(()=>[[`level-${t.depth}`],{collapsible:s.value},{collapsed:n.value},{"is-link":o.value},{"is-active":l.value},{"has-active":d.value}]);function B(A){"key"in A&&A.key!=="Enter"||!t.item.link&&_()}function T(){t.item.link&&_()}return(A,D)=>{const ne=R("VPSidebarItem",!0);return a(),y(U(c(b)),{class:M(["VPSidebarItem",c($)])},{default:v(()=>[e.item.text?(a(),i("div",se({key:0,class:"item",role:c(w)},ut(e.item.items?{click:B,keydown:B}:{},!0),{tabindex:e.item.items&&0}),[ya,e.item.link?(a(),y(O,{key:0,tag:c(L),class:"link",href:e.item.link},{default:v(()=>[(a(),y(U(c(C)),{class:"text",innerHTML:e.item.text},null,8,["innerHTML"]))]),_:1},8,["tag","href"])):(a(),y(U(c(C)),{key:1,class:"text",innerHTML:e.item.text},null,8,["innerHTML"])),e.item.collapsed!=null?(a(),i("div",{key:2,class:"caret",role:"button","aria-label":"toggle section",onClick:T,onKeydown:rt(T,["enter"]),tabindex:"0"},[h(Ce,{class:"caret-icon"})],40,ba)):m("",!0)],16,ga)):m("",!0),e.item.items&&e.item.items.length?(a(),i("div",ka,[e.depth<5?(a(!0),i(S,{key:0},N(e.item.items,W=>(a(),y(ne,{key:W.text,item:W,depth:e.depth+1},null,8,["item","depth"]))),128)):m("",!0)])):m("",!0)]),_:1},8,["class"])}}});const Pa=p($a,[["__scopeId","data-v-93d36c98"]]),Xe=e=>(H("data-v-dc12acb6"),e=e(),E(),e),Va=Xe(()=>r("div",{class:"curtain"},null,-1)),wa={class:"nav",id:"VPSidebarNav","aria-labelledby":"sidebar-aria-label",tabindex:"-1"},xa=Xe(()=>r("span",{class:"visually-hidden",id:"sidebar-aria-label"}," Sidebar Navigation ",-1)),Sa=g({__name:"VPSidebar",props:{open:{type:Boolean}},setup(e){const t=e,{sidebarGroups:n,hasSidebar:s}=z();let o=x(null);function l(){Ke(o.value,{reserveScrollBarGap:!0})}function d(){We()}return dt(async()=>{var f;t.open?(l(),(f=o.value)==null||f.focus()):d()}),(f,_)=>c(s)?(a(),i("aside",{key:0,class:M(["VPSidebar",{open:e.open}]),ref_key:"navEl",ref:o,onClick:_[0]||(_[0]=_t(()=>{},["stop"]))},[Va,r("nav",wa,[xa,u(f.$slots,"sidebar-nav-before",{},void 0,!0),(a(!0),i(S,null,N(c(n),b=>(a(),i("div",{key:b.text,class:"group"},[h(Pa,{item:b,depth:0},null,8,["item"])]))),128)),u(f.$slots,"sidebar-nav-after",{},void 0,!0)])],2)):m("",!0)}});const La=p(Sa,[["__scopeId","data-v-dc12acb6"]]),Ma={},Ca={class:"VPPage"};function Ba(e,t){const n=R("Content");return a(),i("div",Ca,[u(e.$slots,"page-top"),h(n),u(e.$slots,"page-bottom")])}const Ia=p(Ma,[["render",Ba]]),Na=g({__name:"VPButton",props:{tag:null,size:null,theme:null,text:null,href:null},setup(e){const t=e,n=k(()=>[t.size??"medium",t.theme??"brand"]),s=k(()=>t.href&&Ee.test(t.href)),o=k(()=>t.tag?t.tag:t.href?"a":"button");return(l,d)=>(a(),y(U(c(o)),{class:M(["VPButton",c(n)]),href:e.href?c(Z)(e.href):void 0,target:c(s)?"_blank":void 0,rel:c(s)?"noreferrer":void 0},{default:v(()=>[I(P(e.text),1)]),_:1},8,["class","href","target","rel"]))}});const Ta=p(Na,[["__scopeId","data-v-81daf437"]]),Aa=e=>(H("data-v-fb8f862d"),e=e(),E(),e),Ha={class:"container"},Ea={class:"main"},za={key:0,class:"name"},Da={class:"clip"},Fa={key:1,class:"text"},Oa={key:2,class:"tagline"},Ga={key:0,class:"actions"},Ra={key:0,class:"image"},Ua={class:"image-container"},ja=Aa(()=>r("div",{class:"image-bg"},null,-1)),qa=g({__name:"VPHero",props:{name:null,text:null,tagline:null,image:null,actions:null},setup(e){const t=me("hero-image-slot-exists");return(n,s)=>(a(),i("div",{class:M(["VPHero",{"has-image":e.image||c(t)}])},[r("div",Ha,[r("div",Ea,[u(n.$slots,"home-hero-info",{},()=>[e.name?(a(),i("h1",za,[r("span",Da,P(e.name),1)])):m("",!0),e.text?(a(),i("p",Fa,P(e.text),1)):m("",!0),e.tagline?(a(),i("p",Oa,P(e.tagline),1)):m("",!0)],!0),e.actions?(a(),i("div",Ga,[(a(!0),i(S,null,N(e.actions,o=>(a(),i("div",{key:o.link,class:"action"},[h(Ta,{tag:"a",size:"medium",theme:o.theme,text:o.text,href:o.link},null,8,["theme","text","href"])]))),128))])):m("",!0)]),e.image||c(t)?(a(),i("div",Ra,[r("div",Ua,[ja,u(n.$slots,"home-hero-image",{},()=>[e.image?(a(),y(be,{key:0,class:"image-src",image:e.image},null,8,["image"])):m("",!0)],!0)])])):m("",!0)])],2))}});const Ka=p(qa,[["__scopeId","data-v-fb8f862d"]]),Wa=g({__name:"VPHomeHero",setup(e){const{frontmatter:t}=V();return(n,s)=>c(t).hero?(a(),y(Ka,{key:0,class:"VPHomeHero",name:c(t).hero.name,text:c(t).hero.text,tagline:c(t).hero.tagline,image:c(t).hero.image,actions:c(t).hero.actions},{"home-hero-info":v(()=>[u(n.$slots,"home-hero-info")]),"home-hero-image":v(()=>[u(n.$slots,"home-hero-image")]),_:3},8,["name","text","tagline","image","actions"])):m("",!0)}}),Ya={},Xa={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Qa=r("path",{d:"M19.9,12.4c0.1-0.2,0.1-0.5,0-0.8c-0.1-0.1-0.1-0.2-0.2-0.3l-7-7c-0.4-0.4-1-0.4-1.4,0s-0.4,1,0,1.4l5.3,5.3H5c-0.6,0-1,0.4-1,1s0.4,1,1,1h11.6l-5.3,5.3c-0.4,0.4-0.4,1,0,1.4c0.2,0.2,0.5,0.3,0.7,0.3s0.5-0.1,0.7-0.3l7-7C19.8,12.6,19.9,12.5,19.9,12.4z"},null,-1),Ja=[Qa];function Za(e,t){return a(),i("svg",Xa,Ja)}const ec=p(Ya,[["render",Za]]),tc={class:"box"},nc=["innerHTML"],sc=["innerHTML"],oc=["innerHTML"],ac={key:3,class:"link-text"},cc={class:"link-text-value"},lc=g({__name:"VPFeature",props:{icon:null,title:null,details:null,link:null,linkText:null},setup(e){return(t,n)=>(a(),y(O,{class:"VPFeature",href:e.link,"no-icon":!0},{default:v(()=>[r("article",tc,[typeof e.icon=="object"?(a(),y(be,{key:0,image:e.icon,alt:e.icon.alt,height:e.icon.height,width:e.icon.width},null,8,["image","alt","height","width"])):e.icon?(a(),i("div",{key:1,class:"icon",innerHTML:e.icon},null,8,nc)):m("",!0),r("h2",{class:"title",innerHTML:e.title},null,8,sc),e.details?(a(),i("p",{key:2,class:"details",innerHTML:e.details},null,8,oc)):m("",!0),e.linkText?(a(),i("div",ac,[r("p",cc,[I(P(e.linkText)+" ",1),h(ec,{class:"link-text-icon"})])])):m("",!0)])]),_:1},8,["href"]))}});const ic=p(lc,[["__scopeId","data-v-e75de1fb"]]),rc={key:0,class:"VPFeatures"},uc={class:"container"},dc={class:"items"},_c=g({__name:"VPFeatures",props:{features:null},setup(e){const t=e,n=k(()=>{const s=t.features.length;if(s){if(s===2)return"grid-2";if(s===3)return"grid-3";if(s%3===0)return"grid-6";if(s%2===0)return"grid-4"}else return});return(s,o)=>e.features?(a(),i("div",rc,[r("div",uc,[r("div",dc,[(a(!0),i(S,null,N(e.features,l=>(a(),i("div",{key:l.title,class:M(["item",[c(n)]])},[h(ic,{icon:l.icon,title:l.title,details:l.details,link:l.link,"link-text":l.linkText},null,8,["icon","title","details","link","link-text"])],2))),128))])])])):m("",!0)}});const vc=p(_c,[["__scopeId","data-v-531162e3"]]),hc=g({__name:"VPHomeFeatures",setup(e){const{frontmatter:t}=V();return(n,s)=>c(t).features?(a(),y(vc,{key:0,class:"VPHomeFeatures",features:c(t).features},null,8,["features"])):m("",!0)}}),fc={class:"VPHome"},pc=g({__name:"VPHome",setup(e){return(t,n)=>{const s=R("Content");return a(),i("div",fc,[u(t.$slots,"home-hero-before",{},void 0,!0),h(Wa,null,{"home-hero-info":v(()=>[u(t.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-image":v(()=>[u(t.$slots,"home-hero-image",{},void 0,!0)]),_:3}),u(t.$slots,"home-hero-after",{},void 0,!0),u(t.$slots,"home-features-before",{},void 0,!0),h(hc),u(t.$slots,"home-features-after",{},void 0,!0),h(s)])}}});const mc=p(pc,[["__scopeId","data-v-afb11246"]]),gc=e=>(H("data-v-4c887018"),e=e(),E(),e),yc={class:"content"},bc={class:"outline-title"},kc={"aria-labelledby":"doc-outline-aria-label"},$c=gc(()=>r("span",{class:"visually-hidden",id:"doc-outline-aria-label"}," Table of Contents for current page ",-1)),Pc=g({__name:"VPDocAsideOutline",setup(e){const{frontmatter:t,theme:n}=V(),s=ge([]);J(()=>{s.value=Le(t.value.outline??n.value.outline)});const o=x(),l=x();return ta(o,l),(d,f)=>(a(),i("div",{class:M(["VPDocAsideOutline",{"has-outline":c(s).length>0}]),ref_key:"container",ref:o},[r("div",yc,[r("div",{class:"outline-marker",ref_key:"marker",ref:l},null,512),r("div",bc,P(c(Se)(c(n))),1),r("nav",kc,[$c,h(Me,{headers:c(s),root:!0},null,8,["headers"])])])],2))}});const Vc=p(Pc,[["__scopeId","data-v-4c887018"]]),wc={class:"VPDocAsideCarbonAds"},xc=g({__name:"VPDocAsideCarbonAds",props:{carbonAds:null},setup(e){const t=()=>null;return(n,s)=>(a(),i("div",wc,[h(c(t),{"carbon-ads":e.carbonAds},null,8,["carbon-ads"])]))}}),Sc=e=>(H("data-v-70f1dff2"),e=e(),E(),e),Lc={class:"VPDocAside"},Mc=Sc(()=>r("div",{class:"spacer"},null,-1)),Cc=g({__name:"VPDocAside",setup(e){const{theme:t}=V();return(n,s)=>(a(),i("div",Lc,[u(n.$slots,"aside-top",{},void 0,!0),u(n.$slots,"aside-outline-before",{},void 0,!0),h(Vc),u(n.$slots,"aside-outline-after",{},void 0,!0),Mc,u(n.$slots,"aside-ads-before",{},void 0,!0),c(t).carbonAds?(a(),y(xc,{key:0,"carbon-ads":c(t).carbonAds},null,8,["carbon-ads"])):m("",!0),u(n.$slots,"aside-ads-after",{},void 0,!0),u(n.$slots,"aside-bottom",{},void 0,!0)]))}});const Bc=p(Cc,[["__scopeId","data-v-70f1dff2"]]);function Ic(){const{theme:e,page:t}=V();return k(()=>{const{text:n="Edit this page",pattern:s=""}=e.value.editLink||{},{relativePath:o}=t.value;let l;return typeof s=="function"?l=s({relativePath:o}):l=s.replace(/:path/g,o),{url:l,text:n}})}function Nc(){const{page:e,theme:t,frontmatter:n}=V();return k(()=>{var d,f,_,b;const s=Oe(t.value.sidebar,e.value.relativePath),o=wt(s),l=o.findIndex(L=>K(e.value.relativePath,L.link));return{prev:n.value.prev===!1?void 0:{text:(typeof n.value.prev=="string"?n.value.prev:typeof n.value.prev=="object"?n.value.prev.text:void 0)??((d=o[l-1])==null?void 0:d.text),link:(typeof n.value.prev=="object"?n.value.prev.link:void 0)??((f=o[l-1])==null?void 0:f.link)},next:n.value.next===!1?void 0:{text:(typeof n.value.next=="string"?n.value.next:typeof n.value.next=="object"?n.value.next.text:void 0)??((_=o[l+1])==null?void 0:_.text),link:(typeof n.value.next=="object"?n.value.next.link:void 0)??((b=o[l+1])==null?void 0:b.link)}}})}const Tc={},Ac={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24"},Hc=r("path",{d:"M18,23H4c-1.7,0-3-1.3-3-3V6c0-1.7,1.3-3,3-3h7c0.6,0,1,0.4,1,1s-0.4,1-1,1H4C3.4,5,3,5.4,3,6v14c0,0.6,0.4,1,1,1h14c0.6,0,1-0.4,1-1v-7c0-0.6,0.4-1,1-1s1,0.4,1,1v7C21,21.7,19.7,23,18,23z"},null,-1),Ec=r("path",{d:"M8,17c-0.3,0-0.5-0.1-0.7-0.3C7,16.5,6.9,16.1,7,15.8l1-4c0-0.2,0.1-0.3,0.3-0.5l9.5-9.5c1.2-1.2,3.2-1.2,4.4,0c1.2,1.2,1.2,3.2,0,4.4l-9.5,9.5c-0.1,0.1-0.3,0.2-0.5,0.3l-4,1C8.2,17,8.1,17,8,17zM9.9,12.5l-0.5,2.1l2.1-0.5l9.3-9.3c0.4-0.4,0.4-1.1,0-1.6c-0.4-0.4-1.2-0.4-1.6,0l0,0L9.9,12.5z M18.5,2.5L18.5,2.5L18.5,2.5z"},null,-1),zc=[Hc,Ec];function Dc(e,t){return a(),i("svg",Ac,zc)}const Fc=p(Tc,[["render",Dc]]),Oc={class:"VPLastUpdated"},Gc=["datetime"],Rc=g({__name:"VPDocFooterLastUpdated",setup(e){const{theme:t,page:n,lang:s}=V(),o=k(()=>new Date(n.value.lastUpdated)),l=k(()=>o.value.toISOString()),d=x("");return F(()=>{Q(()=>{d.value=o.value.toLocaleString(s.value)})}),(f,_)=>(a(),i("p",Oc,[I(P(c(t).lastUpdatedText||"Last updated")+": ",1),r("time",{datetime:c(l)},P(d.value),9,Gc)]))}});const Uc=p(Rc,[["__scopeId","data-v-3bb9eadc"]]),jc={key:0,class:"VPDocFooter"},qc={key:0,class:"edit-info"},Kc={key:0,class:"edit-link"},Wc={key:1,class:"last-updated"},Yc={key:1,class:"prev-next"},Xc={class:"pager"},Qc=["href"],Jc=["innerHTML"],Zc=["innerHTML"],el=["href"],tl=["innerHTML"],nl=["innerHTML"],sl=g({__name:"VPDocFooter",setup(e){const{theme:t,page:n,frontmatter:s}=V(),o=Ic(),l=Nc(),d=k(()=>t.value.editLink&&s.value.editLink!==!1),f=k(()=>n.value.lastUpdated&&s.value.lastUpdated!==!1),_=k(()=>d.value||f.value||l.value.prev||l.value.next);return(b,L)=>{var C,w,$,B,T,A,D;return c(_)?(a(),i("footer",jc,[u(b.$slots,"doc-footer-before",{},void 0,!0),c(d)||c(f)?(a(),i("div",qc,[c(d)?(a(),i("div",Kc,[h(O,{class:"edit-link-button",href:c(o).url,"no-icon":!0},{default:v(()=>[h(Fc,{class:"edit-link-icon","aria-label":"edit icon"}),I(" "+P(c(o).text),1)]),_:1},8,["href"])])):m("",!0),c(f)?(a(),i("div",Wc,[h(Uc)])):m("",!0)])):m("",!0),(C=c(l).prev)!=null&&C.link||(w=c(l).next)!=null&&w.link?(a(),i("div",Yc,[r("div",Xc,[($=c(l).prev)!=null&&$.link?(a(),i("a",{key:0,class:"pager-link prev",href:c(Z)(c(l).prev.link)},[r("span",{class:"desc",innerHTML:((B=c(t).docFooter)==null?void 0:B.prev)||"Previous page"},null,8,Jc),r("span",{class:"title",innerHTML:c(l).prev.text},null,8,Zc)],8,Qc)):m("",!0)]),r("div",{class:M(["pager",{"has-prev":(T=c(l).prev)==null?void 0:T.link}])},[(A=c(l).next)!=null&&A.link?(a(),i("a",{key:0,class:"pager-link next",href:c(Z)(c(l).next.link)},[r("span",{class:"desc",innerHTML:((D=c(t).docFooter)==null?void 0:D.next)||"Next page"},null,8,tl),r("span",{class:"title",innerHTML:c(l).next.text},null,8,nl)],8,el)):m("",!0)],2)])):m("",!0)])):m("",!0)}}});const ol=p(sl,[["__scopeId","data-v-6701008d"]]),al={key:0,class:"VPDocOutlineDropdown"},cl={key:0,class:"items"},ll=g({__name:"VPDocOutlineDropdown",setup(e){const{frontmatter:t,theme:n}=V(),s=x(!1);J(()=>{s.value=!1});const o=ge([]);return J(()=>{o.value=Le(t.value.outline??n.value.outline)}),(l,d)=>c(o).length>0?(a(),i("div",al,[r("button",{onClick:d[0]||(d[0]=f=>s.value=!s.value),class:M({open:s.value})},[I(P(c(Se)(c(n)))+" ",1),h(Ce,{class:"icon"})],2),s.value?(a(),i("div",cl,[h(Me,{headers:c(o)},null,8,["headers"])])):m("",!0)])):m("",!0)}});const il=p(ll,[["__scopeId","data-v-a099069c"]]),rl=e=>(H("data-v-aa7e4f8a"),e=e(),E(),e),ul={class:"container"},dl=rl(()=>r("div",{class:"aside-curtain"},null,-1)),_l={class:"aside-container"},vl={class:"aside-content"},hl={class:"content"},fl={class:"content-container"},pl={class:"main"},ml=g({__name:"VPDoc",setup(e){const t=ee(),{hasSidebar:n,hasAside:s,leftAside:o}=z(),l=k(()=>t.path.replace(/[./]+/g,"_").replace(/_html$/,""));return(d,f)=>{const _=R("Content");return a(),i("div",{class:M(["VPDoc",{"has-sidebar":c(n),"has-aside":c(s)}])},[u(d.$slots,"doc-top",{},void 0,!0),r("div",ul,[c(s)?(a(),i("div",{key:0,class:M(["aside",{"left-aside":c(o)}])},[dl,r("div",_l,[r("div",vl,[h(Bc,null,{"aside-top":v(()=>[u(d.$slots,"aside-top",{},void 0,!0)]),"aside-bottom":v(()=>[u(d.$slots,"aside-bottom",{},void 0,!0)]),"aside-outline-before":v(()=>[u(d.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":v(()=>[u(d.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":v(()=>[u(d.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":v(()=>[u(d.$slots,"aside-ads-after",{},void 0,!0)]),_:3})])])],2)):m("",!0),r("div",hl,[r("div",fl,[u(d.$slots,"doc-before",{},void 0,!0),h(il),r("main",pl,[h(_,{class:M(["vp-doc",c(l)])},null,8,["class"])]),h(ol,null,{"doc-footer-before":v(()=>[u(d.$slots,"doc-footer-before",{},void 0,!0)]),_:3}),u(d.$slots,"doc-after",{},void 0,!0)])])]),u(d.$slots,"doc-bottom",{},void 0,!0)],2)}}});const gl=p(ml,[["__scopeId","data-v-aa7e4f8a"]]),re=e=>(H("data-v-f8e47611"),e=e(),E(),e),yl={class:"NotFound"},bl=re(()=>r("p",{class:"code"},"404",-1)),kl=re(()=>r("h1",{class:"title"},"PAGE NOT FOUND",-1)),$l=re(()=>r("div",{class:"divider"},null,-1)),Pl=re(()=>r("blockquote",{class:"quote"}," But if you don't change your direction, and if you keep looking, you may end up where you are heading. ",-1)),Vl={class:"action"},wl=["href"],xl=g({__name:"NotFound",setup(e){const{site:t}=V(),{localeLinks:n}=te({removeCurrent:!1}),s=x("/");return F(()=>{var l;const o=window.location.pathname.replace(t.value.base,"").replace(/(^.*?\/).*$/,"/$1");n.value.length&&(s.value=((l=n.value.find(({link:d})=>d.startsWith(o)))==null?void 0:l.link)||n.value[0].link)}),(o,l)=>(a(),i("div",yl,[bl,kl,$l,Pl,r("div",Vl,[r("a",{class:"link",href:c(he)(s.value),"aria-label":"go to home"}," Take me home ",8,wl)])]))}});const Sl=p(xl,[["__scopeId","data-v-f8e47611"]]),Ll=g({__name:"VPContent",setup(e){const{page:t,frontmatter:n}=V(),{hasSidebar:s}=z();return(o,l)=>(a(),i("div",{class:M(["VPContent",{"has-sidebar":c(s),"is-home":c(n).layout==="home"}]),id:"VPContent"},[c(t).isNotFound?u(o.$slots,"not-found",{key:0},()=>[h(Sl)],!0):c(n).layout==="page"?(a(),y(Ia,{key:1},{"page-top":v(()=>[u(o.$slots,"page-top",{},void 0,!0)]),"page-bottom":v(()=>[u(o.$slots,"page-bottom",{},void 0,!0)]),_:3})):c(n).layout==="home"?(a(),y(mc,{key:2},{"home-hero-before":v(()=>[u(o.$slots,"home-hero-before",{},void 0,!0)]),"home-hero-info":v(()=>[u(o.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-image":v(()=>[u(o.$slots,"home-hero-image",{},void 0,!0)]),"home-hero-after":v(()=>[u(o.$slots,"home-hero-after",{},void 0,!0)]),"home-features-before":v(()=>[u(o.$slots,"home-features-before",{},void 0,!0)]),"home-features-after":v(()=>[u(o.$slots,"home-features-after",{},void 0,!0)]),_:3})):(a(),y(gl,{key:3},{"doc-top":v(()=>[u(o.$slots,"doc-top",{},void 0,!0)]),"doc-bottom":v(()=>[u(o.$slots,"doc-bottom",{},void 0,!0)]),"doc-footer-before":v(()=>[u(o.$slots,"doc-footer-before",{},void 0,!0)]),"doc-before":v(()=>[u(o.$slots,"doc-before",{},void 0,!0)]),"doc-after":v(()=>[u(o.$slots,"doc-after",{},void 0,!0)]),"aside-top":v(()=>[u(o.$slots,"aside-top",{},void 0,!0)]),"aside-outline-before":v(()=>[u(o.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":v(()=>[u(o.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":v(()=>[u(o.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":v(()=>[u(o.$slots,"aside-ads-after",{},void 0,!0)]),"aside-bottom":v(()=>[u(o.$slots,"aside-bottom",{},void 0,!0)]),_:3}))],2))}});const Ml=p(Ll,[["__scopeId","data-v-8fb2403f"]]),Cl={class:"container"},Bl=["innerHTML"],Il=["innerHTML"],Nl=g({__name:"VPFooter",setup(e){const{theme:t}=V(),{hasSidebar:n}=z();return(s,o)=>c(t).footer?(a(),i("footer",{key:0,class:M(["VPFooter",{"has-sidebar":c(n)}])},[r("div",Cl,[c(t).footer.message?(a(),i("p",{key:0,class:"message",innerHTML:c(t).footer.message},null,8,Bl)):m("",!0),c(t).footer.copyright?(a(),i("p",{key:1,class:"copyright",innerHTML:c(t).footer.copyright},null,8,Il)):m("",!0)])],2)):m("",!0)}});const Tl=p(Nl,[["__scopeId","data-v-f446d9d9"]]),Al={key:0,class:"Layout"},Hl=g({__name:"Layout",setup(e){const{isOpen:t,open:n,close:s}=z(),o=ee();q(()=>o.path,s),xt(t,s),oe("close-sidebar",s),oe("is-sidebar-open",t);const{frontmatter:l}=V(),d=vt(),f=k(()=>!!d["home-hero-image"]);return oe("hero-image-slot-exists",f),(_,b)=>{const L=R("Content");return c(l).layout!==!1?(a(),i("div",Al,[u(_.$slots,"layout-top",{},void 0,!0),h(Mt),h(It,{class:"backdrop",show:c(t),onClick:c(s)},null,8,["show","onClick"]),h(Oo,null,{"nav-bar-title-before":v(()=>[u(_.$slots,"nav-bar-title-before",{},void 0,!0)]),"nav-bar-title-after":v(()=>[u(_.$slots,"nav-bar-title-after",{},void 0,!0)]),"nav-bar-content-before":v(()=>[u(_.$slots,"nav-bar-content-before",{},void 0,!0)]),"nav-bar-content-after":v(()=>[u(_.$slots,"nav-bar-content-after",{},void 0,!0)]),"nav-screen-content-before":v(()=>[u(_.$slots,"nav-screen-content-before",{},void 0,!0)]),"nav-screen-content-after":v(()=>[u(_.$slots,"nav-screen-content-after",{},void 0,!0)]),_:3}),h(pa,{open:c(t),onOpenMenu:c(n)},null,8,["open","onOpenMenu"]),h(La,{open:c(t)},{"sidebar-nav-before":v(()=>[u(_.$slots,"sidebar-nav-before",{},void 0,!0)]),"sidebar-nav-after":v(()=>[u(_.$slots,"sidebar-nav-after",{},void 0,!0)]),_:3},8,["open"]),h(Ml,null,{"page-top":v(()=>[u(_.$slots,"page-top",{},void 0,!0)]),"page-bottom":v(()=>[u(_.$slots,"page-bottom",{},void 0,!0)]),"not-found":v(()=>[u(_.$slots,"not-found",{},void 0,!0)]),"home-hero-before":v(()=>[u(_.$slots,"home-hero-before",{},void 0,!0)]),"home-hero-info":v(()=>[u(_.$slots,"home-hero-info",{},void 0,!0)]),"home-hero-image":v(()=>[u(_.$slots,"home-hero-image",{},void 0,!0)]),"home-hero-after":v(()=>[u(_.$slots,"home-hero-after",{},void 0,!0)]),"home-features-before":v(()=>[u(_.$slots,"home-features-before",{},void 0,!0)]),"home-features-after":v(()=>[u(_.$slots,"home-features-after",{},void 0,!0)]),"doc-footer-before":v(()=>[u(_.$slots,"doc-footer-before",{},void 0,!0)]),"doc-before":v(()=>[u(_.$slots,"doc-before",{},void 0,!0)]),"doc-after":v(()=>[u(_.$slots,"doc-after",{},void 0,!0)]),"doc-top":v(()=>[u(_.$slots,"doc-top",{},void 0,!0)]),"doc-bottom":v(()=>[u(_.$slots,"doc-bottom",{},void 0,!0)]),"aside-top":v(()=>[u(_.$slots,"aside-top",{},void 0,!0)]),"aside-bottom":v(()=>[u(_.$slots,"aside-bottom",{},void 0,!0)]),"aside-outline-before":v(()=>[u(_.$slots,"aside-outline-before",{},void 0,!0)]),"aside-outline-after":v(()=>[u(_.$slots,"aside-outline-after",{},void 0,!0)]),"aside-ads-before":v(()=>[u(_.$slots,"aside-ads-before",{},void 0,!0)]),"aside-ads-after":v(()=>[u(_.$slots,"aside-ads-after",{},void 0,!0)]),_:3}),h(Tl),u(_.$slots,"layout-bottom",{},void 0,!0)])):(a(),y(L,{key:1}))}}});const El=p(Hl,[["__scopeId","data-v-a484e24a"]]);const Dl={Layout:El,enhanceApp:({app:e})=>{e.component("Badge",ft)}};export{Dl as t};
