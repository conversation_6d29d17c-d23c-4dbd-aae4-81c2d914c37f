function Kn(e,t){const n=Object.create(null),s=e.split(",");for(let r=0;r<s.length;r++)n[s[r]]=!0;return t?r=>!!n[r.toLowerCase()]:r=>!!n[r]}function kn(e){if(N(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],r=re(s)?Ci(s):kn(s);if(r)for(const i in r)t[i]=r[i]}return t}else{if(re(e))return e;if(ee(e))return e}}const _i=/;(?![^(]*\))/g,bi=/:([^]+)/,yi=/\/\*.*?\*\//gs;function Ci(e){const t={};return e.replace(yi,"").split(_i).forEach(n=>{if(n){const s=n.split(bi);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function Wn(e){let t="";if(re(e))t=e;else if(N(e))for(let n=0;n<e.length;n++){const s=Wn(e[n]);s&&(t+=s+" ")}else if(ee(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const wi="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",xi=Kn(wi);function rr(e){return!!e||e===""}const bc=e=>re(e)?e:e==null?"":N(e)||ee(e)&&(e.toString===cr||!j(e.toString))?JSON.stringify(e,ir,2):String(e),ir=(e,t)=>t&&t.__v_isRef?ir(e,t.value):ut(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,r])=>(n[`${s} =>`]=r,n),{})}:or(t)?{[`Set(${t.size})`]:[...t.values()]}:ee(t)&&!N(t)&&!fr(t)?String(t):t,te={},ft=[],Ie=()=>{},Ei=()=>!1,vi=/^on[^a-z]/,Lt=e=>vi.test(e),qn=e=>e.startsWith("onUpdate:"),ce=Object.assign,Vn=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},Ti=Object.prototype.hasOwnProperty,V=(e,t)=>Ti.call(e,t),N=Array.isArray,ut=e=>sn(e)==="[object Map]",or=e=>sn(e)==="[object Set]",j=e=>typeof e=="function",re=e=>typeof e=="string",zn=e=>typeof e=="symbol",ee=e=>e!==null&&typeof e=="object",lr=e=>ee(e)&&j(e.then)&&j(e.catch),cr=Object.prototype.toString,sn=e=>cr.call(e),Ai=e=>sn(e).slice(8,-1),fr=e=>sn(e)==="[object Object]",Yn=e=>re(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Et=Kn(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),rn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},Ri=/-(\w)/g,Se=rn(e=>e.replace(Ri,(t,n)=>n?n.toUpperCase():"")),Ii=/\B([A-Z])/g,it=rn(e=>e.replace(Ii,"-$1").toLowerCase()),on=rn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Vt=rn(e=>e?`on${on(e)}`:""),It=(e,t)=>!Object.is(e,t),Cn=(e,t)=>{for(let n=0;n<e.length;n++)e[n](t)},Xt=(e,t,n)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,value:n})},Jn=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let _s;const Oi=()=>_s||(_s=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});let me;class Fi{constructor(t=!1){this.detached=t,this.active=!0,this.effects=[],this.cleanups=[],this.parent=me,!t&&me&&(this.index=(me.scopes||(me.scopes=[])).push(this)-1)}run(t){if(this.active){const n=me;try{return me=this,t()}finally{me=n}}}on(){me=this}off(){me=this.parent}stop(t){if(this.active){let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.scopes)for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0,this.active=!1}}}function Pi(e,t=me){t&&t.active&&t.effects.push(e)}function yc(){return me}function Cc(e){me&&me.cleanups.push(e)}const Xn=e=>{const t=new Set(e);return t.w=0,t.n=0,t},ur=e=>(e.w&qe)>0,ar=e=>(e.n&qe)>0,Mi=({deps:e})=>{if(e.length)for(let t=0;t<e.length;t++)e[t].w|=qe},Si=e=>{const{deps:t}=e;if(t.length){let n=0;for(let s=0;s<t.length;s++){const r=t[s];ur(r)&&!ar(r)?r.delete(e):t[n++]=r,r.w&=~qe,r.n&=~qe}t.length=n}},On=new WeakMap;let xt=0,qe=1;const Fn=30;let Ae;const st=Symbol(""),Pn=Symbol("");class Zn{constructor(t,n=null,s){this.fn=t,this.scheduler=n,this.active=!0,this.deps=[],this.parent=void 0,Pi(this,s)}run(){if(!this.active)return this.fn();let t=Ae,n=ke;for(;t;){if(t===this)return;t=t.parent}try{return this.parent=Ae,Ae=this,ke=!0,qe=1<<++xt,xt<=Fn?Mi(this):bs(this),this.fn()}finally{xt<=Fn&&Si(this),qe=1<<--xt,Ae=this.parent,ke=n,this.parent=void 0,this.deferStop&&this.stop()}}stop(){Ae===this?this.deferStop=!0:this.active&&(bs(this),this.onStop&&this.onStop(),this.active=!1)}}function bs(e){const{deps:t}=e;if(t.length){for(let n=0;n<t.length;n++)t[n].delete(e);t.length=0}}let ke=!0;const dr=[];function bt(){dr.push(ke),ke=!1}function yt(){const e=dr.pop();ke=e===void 0?!0:e}function be(e,t,n){if(ke&&Ae){let s=On.get(e);s||On.set(e,s=new Map);let r=s.get(n);r||s.set(n,r=Xn()),hr(r)}}function hr(e,t){let n=!1;xt<=Fn?ar(e)||(e.n|=qe,n=!ur(e)):n=!e.has(Ae),n&&(e.add(Ae),Ae.deps.push(e))}function He(e,t,n,s,r,i){const o=On.get(e);if(!o)return;let l=[];if(t==="clear")l=[...o.values()];else if(n==="length"&&N(e)){const f=Jn(s);o.forEach((a,d)=>{(d==="length"||d>=f)&&l.push(a)})}else switch(n!==void 0&&l.push(o.get(n)),t){case"add":N(e)?Yn(n)&&l.push(o.get("length")):(l.push(o.get(st)),ut(e)&&l.push(o.get(Pn)));break;case"delete":N(e)||(l.push(o.get(st)),ut(e)&&l.push(o.get(Pn)));break;case"set":ut(e)&&l.push(o.get(st));break}if(l.length===1)l[0]&&Mn(l[0]);else{const f=[];for(const a of l)a&&f.push(...a);Mn(Xn(f))}}function Mn(e,t){const n=N(e)?e:[...e];for(const s of n)s.computed&&ys(s);for(const s of n)s.computed||ys(s)}function ys(e,t){(e!==Ae||e.allowRecurse)&&(e.scheduler?e.scheduler():e.run())}const Li=Kn("__proto__,__v_isRef,__isVue"),pr=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(zn)),Ni=Qn(),Hi=Qn(!1,!0),$i=Qn(!0),Cs=Ui();function Ui(){const e={};return["includes","indexOf","lastIndexOf"].forEach(t=>{e[t]=function(...n){const s=Y(this);for(let i=0,o=this.length;i<o;i++)be(s,"get",i+"");const r=s[t](...n);return r===-1||r===!1?s[t](...n.map(Y)):r}}),["push","pop","shift","unshift","splice"].forEach(t=>{e[t]=function(...n){bt();const s=Y(this)[t].apply(this,n);return yt(),s}}),e}function Qn(e=!1,t=!1){return function(s,r,i){if(r==="__v_isReactive")return!e;if(r==="__v_isReadonly")return e;if(r==="__v_isShallow")return t;if(r==="__v_raw"&&i===(e?t?eo:yr:t?br:_r).get(s))return s;const o=N(s);if(!e&&o&&V(Cs,r))return Reflect.get(Cs,r,i);const l=Reflect.get(s,r,i);return(zn(r)?pr.has(r):Li(r))||(e||be(s,"get",r),t)?l:oe(l)?o&&Yn(r)?l:l.value:ee(l)?e?Cr(l):cn(l):l}}const ji=gr(),Bi=gr(!0);function gr(e=!1){return function(n,s,r,i){let o=n[s];if(gt(o)&&oe(o)&&!oe(r))return!1;if(!e&&(!Zt(r)&&!gt(r)&&(o=Y(o),r=Y(r)),!N(n)&&oe(o)&&!oe(r)))return o.value=r,!0;const l=N(n)&&Yn(s)?Number(s)<n.length:V(n,s),f=Reflect.set(n,s,r,i);return n===Y(i)&&(l?It(r,o)&&He(n,"set",s,r):He(n,"add",s,r)),f}}function Di(e,t){const n=V(e,t);e[t];const s=Reflect.deleteProperty(e,t);return s&&n&&He(e,"delete",t,void 0),s}function Ki(e,t){const n=Reflect.has(e,t);return(!zn(t)||!pr.has(t))&&be(e,"has",t),n}function ki(e){return be(e,"iterate",N(e)?"length":st),Reflect.ownKeys(e)}const mr={get:Ni,set:ji,deleteProperty:Di,has:Ki,ownKeys:ki},Wi={get:$i,set(e,t){return!0},deleteProperty(e,t){return!0}},qi=ce({},mr,{get:Hi,set:Bi}),Gn=e=>e,ln=e=>Reflect.getPrototypeOf(e);function $t(e,t,n=!1,s=!1){e=e.__v_raw;const r=Y(e),i=Y(t);n||(t!==i&&be(r,"get",t),be(r,"get",i));const{has:o}=ln(r),l=s?Gn:n?ns:Ot;if(o.call(r,t))return l(e.get(t));if(o.call(r,i))return l(e.get(i));e!==r&&e.get(t)}function Ut(e,t=!1){const n=this.__v_raw,s=Y(n),r=Y(e);return t||(e!==r&&be(s,"has",e),be(s,"has",r)),e===r?n.has(e):n.has(e)||n.has(r)}function jt(e,t=!1){return e=e.__v_raw,!t&&be(Y(e),"iterate",st),Reflect.get(e,"size",e)}function ws(e){e=Y(e);const t=Y(this);return ln(t).has.call(t,e)||(t.add(e),He(t,"add",e,e)),this}function xs(e,t){t=Y(t);const n=Y(this),{has:s,get:r}=ln(n);let i=s.call(n,e);i||(e=Y(e),i=s.call(n,e));const o=r.call(n,e);return n.set(e,t),i?It(t,o)&&He(n,"set",e,t):He(n,"add",e,t),this}function Es(e){const t=Y(this),{has:n,get:s}=ln(t);let r=n.call(t,e);r||(e=Y(e),r=n.call(t,e)),s&&s.call(t,e);const i=t.delete(e);return r&&He(t,"delete",e,void 0),i}function vs(){const e=Y(this),t=e.size!==0,n=e.clear();return t&&He(e,"clear",void 0,void 0),n}function Bt(e,t){return function(s,r){const i=this,o=i.__v_raw,l=Y(o),f=t?Gn:e?ns:Ot;return!e&&be(l,"iterate",st),o.forEach((a,d)=>s.call(r,f(a),f(d),i))}}function Dt(e,t,n){return function(...s){const r=this.__v_raw,i=Y(r),o=ut(i),l=e==="entries"||e===Symbol.iterator&&o,f=e==="keys"&&o,a=r[e](...s),d=n?Gn:t?ns:Ot;return!t&&be(i,"iterate",f?Pn:st),{next(){const{value:p,done:y}=a.next();return y?{value:p,done:y}:{value:l?[d(p[0]),d(p[1])]:d(p),done:y}},[Symbol.iterator](){return this}}}}function Ue(e){return function(...t){return e==="delete"?!1:this}}function Vi(){const e={get(i){return $t(this,i)},get size(){return jt(this)},has:Ut,add:ws,set:xs,delete:Es,clear:vs,forEach:Bt(!1,!1)},t={get(i){return $t(this,i,!1,!0)},get size(){return jt(this)},has:Ut,add:ws,set:xs,delete:Es,clear:vs,forEach:Bt(!1,!0)},n={get(i){return $t(this,i,!0)},get size(){return jt(this,!0)},has(i){return Ut.call(this,i,!0)},add:Ue("add"),set:Ue("set"),delete:Ue("delete"),clear:Ue("clear"),forEach:Bt(!0,!1)},s={get(i){return $t(this,i,!0,!0)},get size(){return jt(this,!0)},has(i){return Ut.call(this,i,!0)},add:Ue("add"),set:Ue("set"),delete:Ue("delete"),clear:Ue("clear"),forEach:Bt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(i=>{e[i]=Dt(i,!1,!1),n[i]=Dt(i,!0,!1),t[i]=Dt(i,!1,!0),s[i]=Dt(i,!0,!0)}),[e,n,t,s]}const[zi,Yi,Ji,Xi]=Vi();function es(e,t){const n=t?e?Xi:Ji:e?Yi:zi;return(s,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?s:Reflect.get(V(n,r)&&r in s?n:s,r,i)}const Zi={get:es(!1,!1)},Qi={get:es(!1,!0)},Gi={get:es(!0,!1)},_r=new WeakMap,br=new WeakMap,yr=new WeakMap,eo=new WeakMap;function to(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function no(e){return e.__v_skip||!Object.isExtensible(e)?0:to(Ai(e))}function cn(e){return gt(e)?e:ts(e,!1,mr,Zi,_r)}function so(e){return ts(e,!1,qi,Qi,br)}function Cr(e){return ts(e,!0,Wi,Gi,yr)}function ts(e,t,n,s,r){if(!ee(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=r.get(e);if(i)return i;const o=no(e);if(o===0)return e;const l=new Proxy(e,o===2?s:n);return r.set(e,l),l}function at(e){return gt(e)?at(e.__v_raw):!!(e&&e.__v_isReactive)}function gt(e){return!!(e&&e.__v_isReadonly)}function Zt(e){return!!(e&&e.__v_isShallow)}function wr(e){return at(e)||gt(e)}function Y(e){const t=e&&e.__v_raw;return t?Y(t):e}function vt(e){return Xt(e,"__v_skip",!0),e}const Ot=e=>ee(e)?cn(e):e,ns=e=>ee(e)?Cr(e):e;function ss(e){ke&&Ae&&(e=Y(e),hr(e.dep||(e.dep=Xn())))}function rs(e,t){e=Y(e),e.dep&&Mn(e.dep)}function oe(e){return!!(e&&e.__v_isRef===!0)}function xr(e){return Er(e,!1)}function ro(e){return Er(e,!0)}function Er(e,t){return oe(e)?e:new io(e,t)}class io{constructor(t,n){this.__v_isShallow=n,this.dep=void 0,this.__v_isRef=!0,this._rawValue=n?t:Y(t),this._value=n?t:Ot(t)}get value(){return ss(this),this._value}set value(t){const n=this.__v_isShallow||Zt(t)||gt(t);t=n?t:Y(t),It(t,this._rawValue)&&(this._rawValue=t,this._value=n?t:Ot(t),rs(this))}}function oo(e){return oe(e)?e.value:e}const lo={get:(e,t,n)=>oo(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const r=e[t];return oe(r)&&!oe(n)?(r.value=n,!0):Reflect.set(e,t,n,s)}};function vr(e){return at(e)?e:new Proxy(e,lo)}class co{constructor(t){this.dep=void 0,this.__v_isRef=!0;const{get:n,set:s}=t(()=>ss(this),()=>rs(this));this._get=n,this._set=s}get value(){return this._get()}set value(t){this._set(t)}}function wc(e){return new co(e)}class fo{constructor(t,n,s){this._object=t,this._key=n,this._defaultValue=s,this.__v_isRef=!0}get value(){const t=this._object[this._key];return t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}}function xc(e,t,n){const s=e[t];return oe(s)?s:new fo(e,t,n)}var Tr;class uo{constructor(t,n,s,r){this._setter=n,this.dep=void 0,this.__v_isRef=!0,this[Tr]=!1,this._dirty=!0,this.effect=new Zn(t,()=>{this._dirty||(this._dirty=!0,rs(this))}),this.effect.computed=this,this.effect.active=this._cacheable=!r,this.__v_isReadonly=s}get value(){const t=Y(this);return ss(t),(t._dirty||!t._cacheable)&&(t._dirty=!1,t._value=t.effect.run()),t._value}set value(t){this._setter(t)}}Tr="__v_isReadonly";function ao(e,t,n=!1){let s,r;const i=j(e);return i?(s=e,r=Ie):(s=e.get,r=e.set),new uo(s,r,i||!r,n)}function We(e,t,n,s){let r;try{r=s?e(...s):e()}catch(i){fn(i,t,n)}return r}function xe(e,t,n,s){if(j(e)){const i=We(e,t,n,s);return i&&lr(i)&&i.catch(o=>{fn(o,t,n)}),i}const r=[];for(let i=0;i<e.length;i++)r.push(xe(e[i],t,n,s));return r}function fn(e,t,n,s=!0){const r=t?t.vnode:null;if(t){let i=t.parent;const o=t.proxy,l=n;for(;i;){const a=i.ec;if(a){for(let d=0;d<a.length;d++)if(a[d](e,o,l)===!1)return}i=i.parent}const f=t.appContext.config.errorHandler;if(f){We(f,null,10,[e,o,l]);return}}ho(e,n,r,s)}function ho(e,t,n,s=!0){console.error(e)}let Ft=!1,Sn=!1;const fe=[];let Me=0;const dt=[];let Ne=null,Ge=0;const Ar=Promise.resolve();let is=null;function Rr(e){const t=is||Ar;return e?t.then(this?e.bind(this):e):t}function po(e){let t=Me+1,n=fe.length;for(;t<n;){const s=t+n>>>1;Pt(fe[s])<e?t=s+1:n=s}return t}function os(e){(!fe.length||!fe.includes(e,Ft&&e.allowRecurse?Me+1:Me))&&(e.id==null?fe.push(e):fe.splice(po(e.id),0,e),Ir())}function Ir(){!Ft&&!Sn&&(Sn=!0,is=Ar.then(Or))}function go(e){const t=fe.indexOf(e);t>Me&&fe.splice(t,1)}function mo(e){N(e)?dt.push(...e):(!Ne||!Ne.includes(e,e.allowRecurse?Ge+1:Ge))&&dt.push(e),Ir()}function Ts(e,t=Ft?Me+1:0){for(;t<fe.length;t++){const n=fe[t];n&&n.pre&&(fe.splice(t,1),t--,n())}}function Qt(e){if(dt.length){const t=[...new Set(dt)];if(dt.length=0,Ne){Ne.push(...t);return}for(Ne=t,Ne.sort((n,s)=>Pt(n)-Pt(s)),Ge=0;Ge<Ne.length;Ge++)Ne[Ge]();Ne=null,Ge=0}}const Pt=e=>e.id==null?1/0:e.id,_o=(e,t)=>{const n=Pt(e)-Pt(t);if(n===0){if(e.pre&&!t.pre)return-1;if(t.pre&&!e.pre)return 1}return n};function Or(e){Sn=!1,Ft=!0,fe.sort(_o);const t=Ie;try{for(Me=0;Me<fe.length;Me++){const n=fe[Me];n&&n.active!==!1&&We(n,null,14)}}finally{Me=0,fe.length=0,Qt(),Ft=!1,is=null,(fe.length||dt.length)&&Or()}}function bo(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||te;let r=n;const i=t.startsWith("update:"),o=i&&t.slice(7);if(o&&o in s){const d=`${o==="modelValue"?"model":o}Modifiers`,{number:p,trim:y}=s[d]||te;y&&(r=n.map(R=>re(R)?R.trim():R)),p&&(r=n.map(Jn))}let l,f=s[l=Vt(t)]||s[l=Vt(Se(t))];!f&&i&&(f=s[l=Vt(it(t))]),f&&xe(f,e,6,r);const a=s[l+"Once"];if(a){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,xe(a,e,6,r)}}function Fr(e,t,n=!1){const s=t.emitsCache,r=s.get(e);if(r!==void 0)return r;const i=e.emits;let o={},l=!1;if(!j(e)){const f=a=>{const d=Fr(a,t,!0);d&&(l=!0,ce(o,d))};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}return!i&&!l?(ee(e)&&s.set(e,null),null):(N(i)?i.forEach(f=>o[f]=null):ce(o,i),ee(e)&&s.set(e,o),o)}function un(e,t){return!e||!Lt(t)?!1:(t=t.slice(2).replace(/Once$/,""),V(e,t[0].toLowerCase()+t.slice(1))||V(e,it(t))||V(e,t))}let ue=null,an=null;function Gt(e){const t=ue;return ue=e,an=e&&e.type.__scopeId||null,t}function Ec(e){an=e}function vc(){an=null}function yo(e,t=ue,n){if(!t||e._n)return e;const s=(...r)=>{s._d&&Ns(-1);const i=Gt(t);let o;try{o=e(...r)}finally{Gt(i),s._d&&Ns(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function wn(e){const{type:t,vnode:n,proxy:s,withProxy:r,props:i,propsOptions:[o],slots:l,attrs:f,emit:a,render:d,renderCache:p,data:y,setupState:R,ctx:L,inheritAttrs:F}=e;let W,_;const E=Gt(e);try{if(n.shapeFlag&4){const $=r||s;W=Te(d.call($,$,p,i,R,y,L)),_=f}else{const $=t;W=Te($.length>1?$(i,{attrs:f,slots:l,emit:a}):$(i,null)),_=t.props?f:Co(f)}}catch($){Rt.length=0,fn($,e,1),W=le(_e)}let A=W;if(_&&F!==!1){const $=Object.keys(_),{shapeFlag:D}=A;$.length&&D&7&&(o&&$.some(qn)&&(_=wo(_,o)),A=Ve(A,_))}return n.dirs&&(A=Ve(A),A.dirs=A.dirs?A.dirs.concat(n.dirs):n.dirs),n.transition&&(A.transition=n.transition),W=A,Gt(E),W}const Co=e=>{let t;for(const n in e)(n==="class"||n==="style"||Lt(n))&&((t||(t={}))[n]=e[n]);return t},wo=(e,t)=>{const n={};for(const s in e)(!qn(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function xo(e,t,n){const{props:s,children:r,component:i}=e,{props:o,children:l,patchFlag:f}=t,a=i.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&f>=0){if(f&1024)return!0;if(f&16)return s?As(s,o,a):!!o;if(f&8){const d=t.dynamicProps;for(let p=0;p<d.length;p++){const y=d[p];if(o[y]!==s[y]&&!un(a,y))return!0}}}else return(r||l)&&(!l||!l.$stable)?!0:s===o?!1:s?o?As(s,o,a):!0:!!o;return!1}function As(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let r=0;r<s.length;r++){const i=s[r];if(t[i]!==e[i]&&!un(n,i))return!0}return!1}function Eo({vnode:e,parent:t},n){for(;t&&t.subTree===e;)(e=t.vnode).el=n,t=t.parent}const vo=e=>e.__isSuspense;function Pr(e,t){t&&t.pendingBranch?N(e)?t.effects.push(...e):t.effects.push(e):mo(e)}function To(e,t){if(ie){let n=ie.provides;const s=ie.parent&&ie.parent.provides;s===n&&(n=ie.provides=Object.create(s)),n[e]=t}}function ht(e,t,n=!1){const s=ie||ue;if(s){const r=s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides;if(r&&e in r)return r[e];if(arguments.length>1)return n&&j(t)?t.call(s.proxy):t}}function Ao(e,t){return dn(e,null,t)}function Tc(e,t){return dn(e,null,{flush:"post"})}const Kt={};function zt(e,t,n){return dn(e,t,n)}function dn(e,t,{immediate:n,deep:s,flush:r,onTrack:i,onTrigger:o}=te){const l=ie;let f,a=!1,d=!1;if(oe(e)?(f=()=>e.value,a=Zt(e)):at(e)?(f=()=>e,s=!0):N(e)?(d=!0,a=e.some(A=>at(A)||Zt(A)),f=()=>e.map(A=>{if(oe(A))return A.value;if(at(A))return ct(A);if(j(A))return We(A,l,2)})):j(e)?t?f=()=>We(e,l,2):f=()=>{if(!(l&&l.isUnmounted))return p&&p(),xe(e,l,3,[y])}:f=Ie,t&&s){const A=f;f=()=>ct(A())}let p,y=A=>{p=_.onStop=()=>{We(A,l,4)}},R;if(St)if(y=Ie,t?n&&xe(t,l,3,[f(),d?[]:void 0,y]):f(),r==="sync"){const A=bl();R=A.__watcherHandles||(A.__watcherHandles=[])}else return Ie;let L=d?new Array(e.length).fill(Kt):Kt;const F=()=>{if(_.active)if(t){const A=_.run();(s||a||(d?A.some(($,D)=>It($,L[D])):It(A,L)))&&(p&&p(),xe(t,l,3,[A,L===Kt?void 0:d&&L[0]===Kt?[]:L,y]),L=A)}else _.run()};F.allowRecurse=!!t;let W;r==="sync"?W=F:r==="post"?W=()=>de(F,l&&l.suspense):(F.pre=!0,l&&(F.id=l.uid),W=()=>os(F));const _=new Zn(f,W);t?n?F():L=_.run():r==="post"?de(_.run.bind(_),l&&l.suspense):_.run();const E=()=>{_.stop(),l&&l.scope&&Vn(l.scope.effects,_)};return R&&R.push(E),E}function Ro(e,t,n){const s=this.proxy,r=re(e)?e.includes(".")?Mr(s,e):()=>s[e]:e.bind(s,s);let i;j(t)?i=t:(i=t.handler,n=t);const o=ie;_t(this);const l=dn(r,i.bind(s),n);return o?_t(o):rt(),l}function Mr(e,t){const n=t.split(".");return()=>{let s=e;for(let r=0;r<n.length&&s;r++)s=s[n[r]];return s}}function ct(e,t){if(!ee(e)||e.__v_skip||(t=t||new Set,t.has(e)))return e;if(t.add(e),oe(e))ct(e.value,t);else if(N(e))for(let n=0;n<e.length;n++)ct(e[n],t);else if(or(e)||ut(e))e.forEach(n=>{ct(n,t)});else if(fr(e))for(const n in e)ct(e[n],t);return e}function Io(){const e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return gn(()=>{e.isMounted=!0}),Ur(()=>{e.isUnmounting=!0}),e}const ye=[Function,Array],Oo={name:"BaseTransition",props:{mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:ye,onEnter:ye,onAfterEnter:ye,onEnterCancelled:ye,onBeforeLeave:ye,onLeave:ye,onAfterLeave:ye,onLeaveCancelled:ye,onBeforeAppear:ye,onAppear:ye,onAfterAppear:ye,onAppearCancelled:ye},setup(e,{slots:t}){const n=ni(),s=Io();let r;return()=>{const i=t.default&&Nr(t.default(),!0);if(!i||!i.length)return;let o=i[0];if(i.length>1){for(const F of i)if(F.type!==_e){o=F;break}}const l=Y(e),{mode:f}=l;if(s.isLeaving)return xn(o);const a=Rs(o);if(!a)return xn(o);const d=Ln(a,l,s,n);Nn(a,d);const p=n.subTree,y=p&&Rs(p);let R=!1;const{getTransitionKey:L}=a.type;if(L){const F=L();r===void 0?r=F:F!==r&&(r=F,R=!0)}if(y&&y.type!==_e&&(!et(a,y)||R)){const F=Ln(y,l,s,n);if(Nn(y,F),f==="out-in")return s.isLeaving=!0,F.afterLeave=()=>{s.isLeaving=!1,n.update.active!==!1&&n.update()},xn(o);f==="in-out"&&a.type!==_e&&(F.delayLeave=(W,_,E)=>{const A=Lr(s,y);A[String(y.key)]=y,W._leaveCb=()=>{_(),W._leaveCb=void 0,delete d.delayedLeave},d.delayedLeave=E})}return o}}},Sr=Oo;function Lr(e,t){const{leavingVNodes:n}=e;let s=n.get(t.type);return s||(s=Object.create(null),n.set(t.type,s)),s}function Ln(e,t,n,s){const{appear:r,mode:i,persisted:o=!1,onBeforeEnter:l,onEnter:f,onAfterEnter:a,onEnterCancelled:d,onBeforeLeave:p,onLeave:y,onAfterLeave:R,onLeaveCancelled:L,onBeforeAppear:F,onAppear:W,onAfterAppear:_,onAppearCancelled:E}=t,A=String(e.key),$=Lr(n,e),D=(T,B)=>{T&&xe(T,s,9,B)},Z=(T,B)=>{const U=B[1];D(T,B),N(T)?T.every(z=>z.length<=1)&&U():T.length<=1&&U()},q={mode:i,persisted:o,beforeEnter(T){let B=l;if(!n.isMounted)if(r)B=F||l;else return;T._leaveCb&&T._leaveCb(!0);const U=$[A];U&&et(e,U)&&U.el._leaveCb&&U.el._leaveCb(),D(B,[T])},enter(T){let B=f,U=a,z=d;if(!n.isMounted)if(r)B=W||f,U=_||a,z=E||d;else return;let I=!1;const K=T._enterCb=P=>{I||(I=!0,P?D(z,[T]):D(U,[T]),q.delayedLeave&&q.delayedLeave(),T._enterCb=void 0)};B?Z(B,[T,K]):K()},leave(T,B){const U=String(e.key);if(T._enterCb&&T._enterCb(!0),n.isUnmounting)return B();D(p,[T]);let z=!1;const I=T._leaveCb=K=>{z||(z=!0,B(),K?D(L,[T]):D(R,[T]),T._leaveCb=void 0,$[U]===e&&delete $[U])};$[U]=e,y?Z(y,[T,I]):I()},clone(T){return Ln(T,t,n,s)}};return q}function xn(e){if(hn(e))return e=Ve(e),e.children=null,e}function Rs(e){return hn(e)?e.children?e.children[0]:void 0:e}function Nn(e,t){e.shapeFlag&6&&e.component?Nn(e.component.subTree,t):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function Nr(e,t=!1,n){let s=[],r=0;for(let i=0;i<e.length;i++){let o=e[i];const l=n==null?o.key:String(n)+String(o.key!=null?o.key:i);o.type===he?(o.patchFlag&128&&r++,s=s.concat(Nr(o.children,t,l))):(t||o.type!==_e)&&s.push(l!=null?Ve(o,{key:l}):o)}if(r>1)for(let i=0;i<s.length;i++)s[i].patchFlag=-2;return s}function Hr(e){return j(e)?{setup:e,name:e.name}:e}const pt=e=>!!e.type.__asyncLoader,hn=e=>e.type.__isKeepAlive;function Fo(e,t){$r(e,"a",t)}function Po(e,t){$r(e,"da",t)}function $r(e,t,n=ie){const s=e.__wdc||(e.__wdc=()=>{let r=n;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(pn(t,s,n),n){let r=n.parent;for(;r&&r.parent;)hn(r.parent.vnode)&&Mo(s,t,n,r),r=r.parent}}function Mo(e,t,n,s){const r=pn(t,e,s,!0);mn(()=>{Vn(s[t],r)},n)}function pn(e,t,n=ie,s=!1){if(n){const r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{if(n.isUnmounted)return;bt(),_t(n);const l=xe(t,n,e,o);return rt(),yt(),l});return s?r.unshift(i):r.push(i),i}}const $e=e=>(t,n=ie)=>(!St||e==="sp")&&pn(e,(...s)=>t(...s),n),So=$e("bm"),gn=$e("m"),Lo=$e("bu"),No=$e("u"),Ur=$e("bum"),mn=$e("um"),Ho=$e("sp"),$o=$e("rtg"),Uo=$e("rtc");function jo(e,t=ie){pn("ec",e,t)}function Pe(e,t,n,s){const r=e.dirs,i=t&&t.dirs;for(let o=0;o<r.length;o++){const l=r[o];i&&(l.oldValue=i[o].value);let f=l.dir[s];f&&(bt(),xe(f,n,8,[e.el,l,e,t]),yt())}}const ls="components";function Ac(e,t){return Br(ls,e,!0,t)||e}const jr=Symbol();function Rc(e){return re(e)?Br(ls,e,!1)||e:e||jr}function Br(e,t,n=!0,s=!1){const r=ue||ie;if(r){const i=r.type;if(e===ls){const l=pl(i,!1);if(l&&(l===t||l===Se(t)||l===on(Se(t))))return i}const o=Is(r[e]||i[e],t)||Is(r.appContext[e],t);return!o&&s?i:o}}function Is(e,t){return e&&(e[t]||e[Se(t)]||e[on(Se(t))])}function Ic(e,t,n,s){let r;const i=n&&n[s];if(N(e)||re(e)){r=new Array(e.length);for(let o=0,l=e.length;o<l;o++)r[o]=t(e[o],o,void 0,i&&i[o])}else if(typeof e=="number"){r=new Array(e);for(let o=0;o<e;o++)r[o]=t(o+1,o,void 0,i&&i[o])}else if(ee(e))if(e[Symbol.iterator])r=Array.from(e,(o,l)=>t(o,l,void 0,i&&i[l]));else{const o=Object.keys(e);r=new Array(o.length);for(let l=0,f=o.length;l<f;l++){const a=o[l];r[l]=t(e[a],a,l,i&&i[l])}}else r=[];return n&&(n[s]=r),r}function Oc(e,t,n={},s,r){if(ue.isCE||ue.parent&&pt(ue.parent)&&ue.parent.isCE)return t!=="default"&&(n.name=t),le("slot",n,s&&s());let i=e[t];i&&i._c&&(i._d=!1),Xr();const o=i&&Dr(i(n)),l=Qr(he,{key:n.key||o&&o.key||`_${t}`},o||(s?s():[]),o&&e._===1?64:-2);return!r&&l.scopeId&&(l.slotScopeIds=[l.scopeId+"-s"]),i&&i._c&&(i._d=!0),l}function Dr(e){return e.some(t=>nn(t)?!(t.type===_e||t.type===he&&!Dr(t.children)):!0)?e:null}function Fc(e,t){const n={};for(const s in e)n[t&&/[A-Z]/.test(s)?`on:${s}`:Vt(s)]=e[s];return n}const Hn=e=>e?si(e)?as(e)||e.proxy:Hn(e.parent):null,Tt=ce(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Hn(e.parent),$root:e=>Hn(e.root),$emit:e=>e.emit,$options:e=>cs(e),$forceUpdate:e=>e.f||(e.f=()=>os(e.update)),$nextTick:e=>e.n||(e.n=Rr.bind(e.proxy)),$watch:e=>Ro.bind(e)}),En=(e,t)=>e!==te&&!e.__isScriptSetup&&V(e,t),Bo={get({_:e},t){const{ctx:n,setupState:s,data:r,props:i,accessCache:o,type:l,appContext:f}=e;let a;if(t[0]!=="$"){const R=o[t];if(R!==void 0)switch(R){case 1:return s[t];case 2:return r[t];case 4:return n[t];case 3:return i[t]}else{if(En(s,t))return o[t]=1,s[t];if(r!==te&&V(r,t))return o[t]=2,r[t];if((a=e.propsOptions[0])&&V(a,t))return o[t]=3,i[t];if(n!==te&&V(n,t))return o[t]=4,n[t];$n&&(o[t]=0)}}const d=Tt[t];let p,y;if(d)return t==="$attrs"&&be(e,"get",t),d(e);if((p=l.__cssModules)&&(p=p[t]))return p;if(n!==te&&V(n,t))return o[t]=4,n[t];if(y=f.config.globalProperties,V(y,t))return y[t]},set({_:e},t,n){const{data:s,setupState:r,ctx:i}=e;return En(r,t)?(r[t]=n,!0):s!==te&&V(s,t)?(s[t]=n,!0):V(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:r,propsOptions:i}},o){let l;return!!n[o]||e!==te&&V(e,o)||En(t,o)||(l=i[0])&&V(l,o)||V(s,o)||V(Tt,o)||V(r.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:V(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};let $n=!0;function Do(e){const t=cs(e),n=e.proxy,s=e.ctx;$n=!1,t.beforeCreate&&Os(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:o,watch:l,provide:f,inject:a,created:d,beforeMount:p,mounted:y,beforeUpdate:R,updated:L,activated:F,deactivated:W,beforeDestroy:_,beforeUnmount:E,destroyed:A,unmounted:$,render:D,renderTracked:Z,renderTriggered:q,errorCaptured:T,serverPrefetch:B,expose:U,inheritAttrs:z,components:I,directives:K,filters:P}=t;if(a&&Ko(a,s,null,e.appContext.config.unwrapInjectedRef),o)for(const ne in o){const Q=o[ne];j(Q)&&(s[ne]=Q.bind(n))}if(r){const ne=r.call(n,n);ee(ne)&&(e.data=cn(ne))}if($n=!0,i)for(const ne in i){const Q=i[ne],ze=j(Q)?Q.bind(n,n):j(Q.get)?Q.get.bind(n,n):Ie,Nt=!j(Q)&&j(Q.set)?Q.set.bind(n):Ie,Ye=ve({get:ze,set:Nt});Object.defineProperty(s,ne,{enumerable:!0,configurable:!0,get:()=>Ye.value,set:Oe=>Ye.value=Oe})}if(l)for(const ne in l)Kr(l[ne],s,n,ne);if(f){const ne=j(f)?f.call(n):f;Reflect.ownKeys(ne).forEach(Q=>{To(Q,ne[Q])})}d&&Os(d,e,"c");function J(ne,Q){N(Q)?Q.forEach(ze=>ne(ze.bind(n))):Q&&ne(Q.bind(n))}if(J(So,p),J(gn,y),J(Lo,R),J(No,L),J(Fo,F),J(Po,W),J(jo,T),J(Uo,Z),J($o,q),J(Ur,E),J(mn,$),J(Ho,B),N(U))if(U.length){const ne=e.exposed||(e.exposed={});U.forEach(Q=>{Object.defineProperty(ne,Q,{get:()=>n[Q],set:ze=>n[Q]=ze})})}else e.exposed||(e.exposed={});D&&e.render===Ie&&(e.render=D),z!=null&&(e.inheritAttrs=z),I&&(e.components=I),K&&(e.directives=K)}function Ko(e,t,n=Ie,s=!1){N(e)&&(e=Un(e));for(const r in e){const i=e[r];let o;ee(i)?"default"in i?o=ht(i.from||r,i.default,!0):o=ht(i.from||r):o=ht(i),oe(o)&&s?Object.defineProperty(t,r,{enumerable:!0,configurable:!0,get:()=>o.value,set:l=>o.value=l}):t[r]=o}}function Os(e,t,n){xe(N(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function Kr(e,t,n,s){const r=s.includes(".")?Mr(n,s):()=>n[s];if(re(e)){const i=t[e];j(i)&&zt(r,i)}else if(j(e))zt(r,e.bind(n));else if(ee(e))if(N(e))e.forEach(i=>Kr(i,t,n,s));else{const i=j(e.handler)?e.handler.bind(n):t[e.handler];j(i)&&zt(r,i,e)}}function cs(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:o}}=e.appContext,l=i.get(t);let f;return l?f=l:!r.length&&!n&&!s?f=t:(f={},r.length&&r.forEach(a=>en(f,a,o,!0)),en(f,t,o)),ee(t)&&i.set(t,f),f}function en(e,t,n,s=!1){const{mixins:r,extends:i}=t;i&&en(e,i,n,!0),r&&r.forEach(o=>en(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const l=ko[o]||n&&n[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const ko={data:Fs,props:Qe,emits:Qe,methods:Qe,computed:Qe,beforeCreate:ae,created:ae,beforeMount:ae,mounted:ae,beforeUpdate:ae,updated:ae,beforeDestroy:ae,beforeUnmount:ae,destroyed:ae,unmounted:ae,activated:ae,deactivated:ae,errorCaptured:ae,serverPrefetch:ae,components:Qe,directives:Qe,watch:qo,provide:Fs,inject:Wo};function Fs(e,t){return t?e?function(){return ce(j(e)?e.call(this,this):e,j(t)?t.call(this,this):t)}:t:e}function Wo(e,t){return Qe(Un(e),Un(t))}function Un(e){if(N(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function ae(e,t){return e?[...new Set([].concat(e,t))]:t}function Qe(e,t){return e?ce(ce(Object.create(null),e),t):t}function qo(e,t){if(!e)return t;if(!t)return e;const n=ce(Object.create(null),e);for(const s in t)n[s]=ae(e[s],t[s]);return n}function Vo(e,t,n,s=!1){const r={},i={};Xt(i,_n,1),e.propsDefaults=Object.create(null),kr(e,t,r,i);for(const o in e.propsOptions[0])o in r||(r[o]=void 0);n?e.props=s?r:so(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function zo(e,t,n,s){const{props:r,attrs:i,vnode:{patchFlag:o}}=e,l=Y(r),[f]=e.propsOptions;let a=!1;if((s||o>0)&&!(o&16)){if(o&8){const d=e.vnode.dynamicProps;for(let p=0;p<d.length;p++){let y=d[p];if(un(e.emitsOptions,y))continue;const R=t[y];if(f)if(V(i,y))R!==i[y]&&(i[y]=R,a=!0);else{const L=Se(y);r[L]=jn(f,l,L,R,e,!1)}else R!==i[y]&&(i[y]=R,a=!0)}}}else{kr(e,t,r,i)&&(a=!0);let d;for(const p in l)(!t||!V(t,p)&&((d=it(p))===p||!V(t,d)))&&(f?n&&(n[p]!==void 0||n[d]!==void 0)&&(r[p]=jn(f,l,p,void 0,e,!0)):delete r[p]);if(i!==l)for(const p in i)(!t||!V(t,p))&&(delete i[p],a=!0)}a&&He(e,"set","$attrs")}function kr(e,t,n,s){const[r,i]=e.propsOptions;let o=!1,l;if(t)for(let f in t){if(Et(f))continue;const a=t[f];let d;r&&V(r,d=Se(f))?!i||!i.includes(d)?n[d]=a:(l||(l={}))[d]=a:un(e.emitsOptions,f)||(!(f in s)||a!==s[f])&&(s[f]=a,o=!0)}if(i){const f=Y(n),a=l||te;for(let d=0;d<i.length;d++){const p=i[d];n[p]=jn(r,f,p,a[p],e,!V(a,p))}}return o}function jn(e,t,n,s,r,i){const o=e[n];if(o!=null){const l=V(o,"default");if(l&&s===void 0){const f=o.default;if(o.type!==Function&&j(f)){const{propsDefaults:a}=r;n in a?s=a[n]:(_t(r),s=a[n]=f.call(null,t),rt())}else s=f}o[0]&&(i&&!l?s=!1:o[1]&&(s===""||s===it(n))&&(s=!0))}return s}function Wr(e,t,n=!1){const s=t.propsCache,r=s.get(e);if(r)return r;const i=e.props,o={},l=[];let f=!1;if(!j(e)){const d=p=>{f=!0;const[y,R]=Wr(p,t,!0);ce(o,y),R&&l.push(...R)};!n&&t.mixins.length&&t.mixins.forEach(d),e.extends&&d(e.extends),e.mixins&&e.mixins.forEach(d)}if(!i&&!f)return ee(e)&&s.set(e,ft),ft;if(N(i))for(let d=0;d<i.length;d++){const p=Se(i[d]);Ps(p)&&(o[p]=te)}else if(i)for(const d in i){const p=Se(d);if(Ps(p)){const y=i[d],R=o[p]=N(y)||j(y)?{type:y}:Object.assign({},y);if(R){const L=Ls(Boolean,R.type),F=Ls(String,R.type);R[0]=L>-1,R[1]=F<0||L<F,(L>-1||V(R,"default"))&&l.push(p)}}}const a=[o,l];return ee(e)&&s.set(e,a),a}function Ps(e){return e[0]!=="$"}function Ms(e){const t=e&&e.toString().match(/^\s*function (\w+)/);return t?t[1]:e===null?"null":""}function Ss(e,t){return Ms(e)===Ms(t)}function Ls(e,t){return N(t)?t.findIndex(n=>Ss(n,e)):j(t)&&Ss(t,e)?0:-1}const qr=e=>e[0]==="_"||e==="$stable",fs=e=>N(e)?e.map(Te):[Te(e)],Yo=(e,t,n)=>{if(t._n)return t;const s=yo((...r)=>fs(t(...r)),n);return s._c=!1,s},Vr=(e,t,n)=>{const s=e._ctx;for(const r in e){if(qr(r))continue;const i=e[r];if(j(i))t[r]=Yo(r,i,s);else if(i!=null){const o=fs(i);t[r]=()=>o}}},zr=(e,t)=>{const n=fs(t);e.slots.default=()=>n},Jo=(e,t)=>{if(e.vnode.shapeFlag&32){const n=t._;n?(e.slots=Y(t),Xt(t,"_",n)):Vr(t,e.slots={})}else e.slots={},t&&zr(e,t);Xt(e.slots,_n,1)},Xo=(e,t,n)=>{const{vnode:s,slots:r}=e;let i=!0,o=te;if(s.shapeFlag&32){const l=t._;l?n&&l===1?i=!1:(ce(r,t),!n&&l===1&&delete r._):(i=!t.$stable,Vr(t,r)),o=t}else t&&(zr(e,t),o={default:1});if(i)for(const l in r)!qr(l)&&!(l in o)&&delete r[l]};function Yr(){return{app:null,config:{isNativeTag:Ei,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Zo=0;function Qo(e,t){return function(s,r=null){j(s)||(s=Object.assign({},s)),r!=null&&!ee(r)&&(r=null);const i=Yr(),o=new Set;let l=!1;const f=i.app={_uid:Zo++,_component:s,_props:r,_container:null,_context:i,_instance:null,version:yl,get config(){return i.config},set config(a){},use(a,...d){return o.has(a)||(a&&j(a.install)?(o.add(a),a.install(f,...d)):j(a)&&(o.add(a),a(f,...d))),f},mixin(a){return i.mixins.includes(a)||i.mixins.push(a),f},component(a,d){return d?(i.components[a]=d,f):i.components[a]},directive(a,d){return d?(i.directives[a]=d,f):i.directives[a]},mount(a,d,p){if(!l){const y=le(s,r);return y.appContext=i,d&&t?t(y,a):e(y,a,p),l=!0,f._container=a,a.__vue_app__=f,as(y.component)||y.component.proxy}},unmount(){l&&(e(null,f._container),delete f._container.__vue_app__)},provide(a,d){return i.provides[a]=d,f}};return f}}function tn(e,t,n,s,r=!1){if(N(e)){e.forEach((y,R)=>tn(y,t&&(N(t)?t[R]:t),n,s,r));return}if(pt(s)&&!r)return;const i=s.shapeFlag&4?as(s.component)||s.component.proxy:s.el,o=r?null:i,{i:l,r:f}=e,a=t&&t.r,d=l.refs===te?l.refs={}:l.refs,p=l.setupState;if(a!=null&&a!==f&&(re(a)?(d[a]=null,V(p,a)&&(p[a]=null)):oe(a)&&(a.value=null)),j(f))We(f,l,12,[o,d]);else{const y=re(f),R=oe(f);if(y||R){const L=()=>{if(e.f){const F=y?V(p,f)?p[f]:d[f]:f.value;r?N(F)&&Vn(F,i):N(F)?F.includes(i)||F.push(i):y?(d[f]=[i],V(p,f)&&(p[f]=d[f])):(f.value=[i],e.k&&(d[e.k]=f.value))}else y?(d[f]=o,V(p,f)&&(p[f]=o)):R&&(f.value=o,e.k&&(d[e.k]=o))};o?(L.id=-1,de(L,n)):L()}}}let je=!1;const kt=e=>/svg/.test(e.namespaceURI)&&e.tagName!=="foreignObject",Wt=e=>e.nodeType===8;function Go(e){const{mt:t,p:n,o:{patchProp:s,createText:r,nextSibling:i,parentNode:o,remove:l,insert:f,createComment:a}}=e,d=(_,E)=>{if(!E.hasChildNodes()){n(null,_,E),Qt(),E._vnode=_;return}je=!1,p(E.firstChild,_,null,null,null),Qt(),E._vnode=_,je&&console.error("Hydration completed but contains mismatches.")},p=(_,E,A,$,D,Z=!1)=>{const q=Wt(_)&&_.data==="[",T=()=>F(_,E,A,$,D,q),{type:B,ref:U,shapeFlag:z,patchFlag:I}=E;let K=_.nodeType;E.el=_,I===-2&&(Z=!1,E.dynamicChildren=null);let P=null;switch(B){case mt:K!==3?E.children===""?(f(E.el=r(""),o(_),_),P=_):P=T():(_.data!==E.children&&(je=!0,_.data=E.children),P=i(_));break;case _e:K!==8||q?P=T():P=i(_);break;case At:if(q&&(_=i(_),K=_.nodeType),K===1||K===3){P=_;const pe=!E.children.length;for(let J=0;J<E.staticCount;J++)pe&&(E.children+=P.nodeType===1?P.outerHTML:P.data),J===E.staticCount-1&&(E.anchor=P),P=i(P);return q?i(P):P}else T();break;case he:q?P=L(_,E,A,$,D,Z):P=T();break;default:if(z&1)K!==1||E.type.toLowerCase()!==_.tagName.toLowerCase()?P=T():P=y(_,E,A,$,D,Z);else if(z&6){E.slotScopeIds=D;const pe=o(_);if(t(E,pe,null,A,$,kt(pe),Z),P=q?W(_):i(_),P&&Wt(P)&&P.data==="teleport end"&&(P=i(P)),pt(E)){let J;q?(J=le(he),J.anchor=P?P.previousSibling:pe.lastChild):J=_.nodeType===3?ti(""):le("div"),J.el=_,E.component.subTree=J}}else z&64?K!==8?P=T():P=E.type.hydrate(_,E,A,$,D,Z,e,R):z&128&&(P=E.type.hydrate(_,E,A,$,kt(o(_)),D,Z,e,p))}return U!=null&&tn(U,null,$,E),P},y=(_,E,A,$,D,Z)=>{Z=Z||!!E.dynamicChildren;const{type:q,props:T,patchFlag:B,shapeFlag:U,dirs:z}=E,I=q==="input"&&z||q==="option";if(I||B!==-1){if(z&&Pe(E,null,A,"created"),T)if(I||!Z||B&48)for(const P in T)(I&&P.endsWith("value")||Lt(P)&&!Et(P))&&s(_,P,null,T[P],!1,void 0,A);else T.onClick&&s(_,"onClick",null,T.onClick,!1,void 0,A);let K;if((K=T&&T.onVnodeBeforeMount)&&Ce(K,A,E),z&&Pe(E,null,A,"beforeMount"),((K=T&&T.onVnodeMounted)||z)&&Pr(()=>{K&&Ce(K,A,E),z&&Pe(E,null,A,"mounted")},$),U&16&&!(T&&(T.innerHTML||T.textContent))){let P=R(_.firstChild,E,_,A,$,D,Z);for(;P;){je=!0;const pe=P;P=P.nextSibling,l(pe)}}else U&8&&_.textContent!==E.children&&(je=!0,_.textContent=E.children)}return _.nextSibling},R=(_,E,A,$,D,Z,q)=>{q=q||!!E.dynamicChildren;const T=E.children,B=T.length;for(let U=0;U<B;U++){const z=q?T[U]:T[U]=Te(T[U]);if(_)_=p(_,z,$,D,Z,q);else{if(z.type===mt&&!z.children)continue;je=!0,n(null,z,A,null,$,D,kt(A),Z)}}return _},L=(_,E,A,$,D,Z)=>{const{slotScopeIds:q}=E;q&&(D=D?D.concat(q):q);const T=o(_),B=R(i(_),E,T,A,$,D,Z);return B&&Wt(B)&&B.data==="]"?i(E.anchor=B):(je=!0,f(E.anchor=a("]"),T,B),B)},F=(_,E,A,$,D,Z)=>{if(je=!0,E.el=null,Z){const B=W(_);for(;;){const U=i(_);if(U&&U!==B)l(U);else break}}const q=i(_),T=o(_);return l(_),n(null,E,T,q,A,$,kt(T),D),q},W=_=>{let E=0;for(;_;)if(_=i(_),_&&Wt(_)&&(_.data==="["&&E++,_.data==="]")){if(E===0)return i(_);E--}return _};return[d,p]}const de=Pr;function el(e){return tl(e,Go)}function tl(e,t){const n=Oi();n.__VUE__=!0;const{insert:s,remove:r,patchProp:i,createElement:o,createText:l,createComment:f,setText:a,setElementText:d,parentNode:p,nextSibling:y,setScopeId:R=Ie,insertStaticContent:L}=e,F=(c,u,h,m=null,g=null,w=null,v=!1,C=null,x=!!u.dynamicChildren)=>{if(c===u)return;c&&!et(c,u)&&(m=Ht(c),Oe(c,g,w,!0),c=null),u.patchFlag===-2&&(x=!1,u.dynamicChildren=null);const{type:b,ref:M,shapeFlag:O}=u;switch(b){case mt:W(c,u,h,m);break;case _e:_(c,u,h,m);break;case At:c==null&&E(u,h,m,v);break;case he:I(c,u,h,m,g,w,v,C,x);break;default:O&1?D(c,u,h,m,g,w,v,C,x):O&6?K(c,u,h,m,g,w,v,C,x):(O&64||O&128)&&b.process(c,u,h,m,g,w,v,C,x,ot)}M!=null&&g&&tn(M,c&&c.ref,w,u||c,!u)},W=(c,u,h,m)=>{if(c==null)s(u.el=l(u.children),h,m);else{const g=u.el=c.el;u.children!==c.children&&a(g,u.children)}},_=(c,u,h,m)=>{c==null?s(u.el=f(u.children||""),h,m):u.el=c.el},E=(c,u,h,m)=>{[c.el,c.anchor]=L(c.children,u,h,m,c.el,c.anchor)},A=({el:c,anchor:u},h,m)=>{let g;for(;c&&c!==u;)g=y(c),s(c,h,m),c=g;s(u,h,m)},$=({el:c,anchor:u})=>{let h;for(;c&&c!==u;)h=y(c),r(c),c=h;r(u)},D=(c,u,h,m,g,w,v,C,x)=>{v=v||u.type==="svg",c==null?Z(u,h,m,g,w,v,C,x):B(c,u,g,w,v,C,x)},Z=(c,u,h,m,g,w,v,C)=>{let x,b;const{type:M,props:O,shapeFlag:S,transition:H,dirs:k}=c;if(x=c.el=o(c.type,w,O&&O.is,O),S&8?d(x,c.children):S&16&&T(c.children,x,null,m,g,w&&M!=="foreignObject",v,C),k&&Pe(c,null,m,"created"),O){for(const X in O)X!=="value"&&!Et(X)&&i(x,X,null,O[X],w,c.children,m,g,Le);"value"in O&&i(x,"value",null,O.value),(b=O.onVnodeBeforeMount)&&Ce(b,m,c)}q(x,c,c.scopeId,v,m),k&&Pe(c,null,m,"beforeMount");const G=(!g||g&&!g.pendingBranch)&&H&&!H.persisted;G&&H.beforeEnter(x),s(x,u,h),((b=O&&O.onVnodeMounted)||G||k)&&de(()=>{b&&Ce(b,m,c),G&&H.enter(x),k&&Pe(c,null,m,"mounted")},g)},q=(c,u,h,m,g)=>{if(h&&R(c,h),m)for(let w=0;w<m.length;w++)R(c,m[w]);if(g){let w=g.subTree;if(u===w){const v=g.vnode;q(c,v,v.scopeId,v.slotScopeIds,g.parent)}}},T=(c,u,h,m,g,w,v,C,x=0)=>{for(let b=x;b<c.length;b++){const M=c[b]=C?Ke(c[b]):Te(c[b]);F(null,M,u,h,m,g,w,v,C)}},B=(c,u,h,m,g,w,v)=>{const C=u.el=c.el;let{patchFlag:x,dynamicChildren:b,dirs:M}=u;x|=c.patchFlag&16;const O=c.props||te,S=u.props||te;let H;h&&Je(h,!1),(H=S.onVnodeBeforeUpdate)&&Ce(H,h,u,c),M&&Pe(u,c,h,"beforeUpdate"),h&&Je(h,!0);const k=g&&u.type!=="foreignObject";if(b?U(c.dynamicChildren,b,C,h,m,k,w):v||Q(c,u,C,null,h,m,k,w,!1),x>0){if(x&16)z(C,u,O,S,h,m,g);else if(x&2&&O.class!==S.class&&i(C,"class",null,S.class,g),x&4&&i(C,"style",O.style,S.style,g),x&8){const G=u.dynamicProps;for(let X=0;X<G.length;X++){const se=G[X],Ee=O[se],lt=S[se];(lt!==Ee||se==="value")&&i(C,se,Ee,lt,g,c.children,h,m,Le)}}x&1&&c.children!==u.children&&d(C,u.children)}else!v&&b==null&&z(C,u,O,S,h,m,g);((H=S.onVnodeUpdated)||M)&&de(()=>{H&&Ce(H,h,u,c),M&&Pe(u,c,h,"updated")},m)},U=(c,u,h,m,g,w,v)=>{for(let C=0;C<u.length;C++){const x=c[C],b=u[C],M=x.el&&(x.type===he||!et(x,b)||x.shapeFlag&70)?p(x.el):h;F(x,b,M,null,m,g,w,v,!0)}},z=(c,u,h,m,g,w,v)=>{if(h!==m){if(h!==te)for(const C in h)!Et(C)&&!(C in m)&&i(c,C,h[C],null,v,u.children,g,w,Le);for(const C in m){if(Et(C))continue;const x=m[C],b=h[C];x!==b&&C!=="value"&&i(c,C,b,x,v,u.children,g,w,Le)}"value"in m&&i(c,"value",h.value,m.value)}},I=(c,u,h,m,g,w,v,C,x)=>{const b=u.el=c?c.el:l(""),M=u.anchor=c?c.anchor:l("");let{patchFlag:O,dynamicChildren:S,slotScopeIds:H}=u;H&&(C=C?C.concat(H):H),c==null?(s(b,h,m),s(M,h,m),T(u.children,h,M,g,w,v,C,x)):O>0&&O&64&&S&&c.dynamicChildren?(U(c.dynamicChildren,S,h,g,w,v,C),(u.key!=null||g&&u===g.subTree)&&Jr(c,u,!0)):Q(c,u,h,M,g,w,v,C,x)},K=(c,u,h,m,g,w,v,C,x)=>{u.slotScopeIds=C,c==null?u.shapeFlag&512?g.ctx.activate(u,h,m,v,x):P(u,h,m,g,w,v,x):pe(c,u,x)},P=(c,u,h,m,g,w,v)=>{const C=c.component=ul(c,m,g);if(hn(c)&&(C.ctx.renderer=ot),al(C),C.asyncDep){if(g&&g.registerDep(C,J),!c.el){const x=C.subTree=le(_e);_(null,x,u,h)}return}J(C,c,u,h,g,w,v)},pe=(c,u,h)=>{const m=u.component=c.component;if(xo(c,u,h))if(m.asyncDep&&!m.asyncResolved){ne(m,u,h);return}else m.next=u,go(m.update),m.update();else u.el=c.el,m.vnode=u},J=(c,u,h,m,g,w,v)=>{const C=()=>{if(c.isMounted){let{next:M,bu:O,u:S,parent:H,vnode:k}=c,G=M,X;Je(c,!1),M?(M.el=k.el,ne(c,M,v)):M=k,O&&Cn(O),(X=M.props&&M.props.onVnodeBeforeUpdate)&&Ce(X,H,M,k),Je(c,!0);const se=wn(c),Ee=c.subTree;c.subTree=se,F(Ee,se,p(Ee.el),Ht(Ee),c,g,w),M.el=se.el,G===null&&Eo(c,se.el),S&&de(S,g),(X=M.props&&M.props.onVnodeUpdated)&&de(()=>Ce(X,H,M,k),g)}else{let M;const{el:O,props:S}=u,{bm:H,m:k,parent:G}=c,X=pt(u);if(Je(c,!1),H&&Cn(H),!X&&(M=S&&S.onVnodeBeforeMount)&&Ce(M,G,u),Je(c,!0),O&&yn){const se=()=>{c.subTree=wn(c),yn(O,c.subTree,c,g,null)};X?u.type.__asyncLoader().then(()=>!c.isUnmounted&&se()):se()}else{const se=c.subTree=wn(c);F(null,se,h,m,c,g,w),u.el=se.el}if(k&&de(k,g),!X&&(M=S&&S.onVnodeMounted)){const se=u;de(()=>Ce(M,G,se),g)}(u.shapeFlag&256||G&&pt(G.vnode)&&G.vnode.shapeFlag&256)&&c.a&&de(c.a,g),c.isMounted=!0,u=h=m=null}},x=c.effect=new Zn(C,()=>os(b),c.scope),b=c.update=()=>x.run();b.id=c.uid,Je(c,!0),b()},ne=(c,u,h)=>{u.component=c;const m=c.vnode.props;c.vnode=u,c.next=null,zo(c,u.props,m,h),Xo(c,u.children,h),bt(),Ts(),yt()},Q=(c,u,h,m,g,w,v,C,x=!1)=>{const b=c&&c.children,M=c?c.shapeFlag:0,O=u.children,{patchFlag:S,shapeFlag:H}=u;if(S>0){if(S&128){Nt(b,O,h,m,g,w,v,C,x);return}else if(S&256){ze(b,O,h,m,g,w,v,C,x);return}}H&8?(M&16&&Le(b,g,w),O!==b&&d(h,O)):M&16?H&16?Nt(b,O,h,m,g,w,v,C,x):Le(b,g,w,!0):(M&8&&d(h,""),H&16&&T(O,h,m,g,w,v,C,x))},ze=(c,u,h,m,g,w,v,C,x)=>{c=c||ft,u=u||ft;const b=c.length,M=u.length,O=Math.min(b,M);let S;for(S=0;S<O;S++){const H=u[S]=x?Ke(u[S]):Te(u[S]);F(c[S],H,h,null,g,w,v,C,x)}b>M?Le(c,g,w,!0,!1,O):T(u,h,m,g,w,v,C,x,O)},Nt=(c,u,h,m,g,w,v,C,x)=>{let b=0;const M=u.length;let O=c.length-1,S=M-1;for(;b<=O&&b<=S;){const H=c[b],k=u[b]=x?Ke(u[b]):Te(u[b]);if(et(H,k))F(H,k,h,null,g,w,v,C,x);else break;b++}for(;b<=O&&b<=S;){const H=c[O],k=u[S]=x?Ke(u[S]):Te(u[S]);if(et(H,k))F(H,k,h,null,g,w,v,C,x);else break;O--,S--}if(b>O){if(b<=S){const H=S+1,k=H<M?u[H].el:m;for(;b<=S;)F(null,u[b]=x?Ke(u[b]):Te(u[b]),h,k,g,w,v,C,x),b++}}else if(b>S)for(;b<=O;)Oe(c[b],g,w,!0),b++;else{const H=b,k=b,G=new Map;for(b=k;b<=S;b++){const ge=u[b]=x?Ke(u[b]):Te(u[b]);ge.key!=null&&G.set(ge.key,b)}let X,se=0;const Ee=S-k+1;let lt=!1,ps=0;const Ct=new Array(Ee);for(b=0;b<Ee;b++)Ct[b]=0;for(b=H;b<=O;b++){const ge=c[b];if(se>=Ee){Oe(ge,g,w,!0);continue}let Fe;if(ge.key!=null)Fe=G.get(ge.key);else for(X=k;X<=S;X++)if(Ct[X-k]===0&&et(ge,u[X])){Fe=X;break}Fe===void 0?Oe(ge,g,w,!0):(Ct[Fe-k]=b+1,Fe>=ps?ps=Fe:lt=!0,F(ge,u[Fe],h,null,g,w,v,C,x),se++)}const gs=lt?nl(Ct):ft;for(X=gs.length-1,b=Ee-1;b>=0;b--){const ge=k+b,Fe=u[ge],ms=ge+1<M?u[ge+1].el:m;Ct[b]===0?F(null,Fe,h,ms,g,w,v,C,x):lt&&(X<0||b!==gs[X]?Ye(Fe,h,ms,2):X--)}}},Ye=(c,u,h,m,g=null)=>{const{el:w,type:v,transition:C,children:x,shapeFlag:b}=c;if(b&6){Ye(c.component.subTree,u,h,m);return}if(b&128){c.suspense.move(u,h,m);return}if(b&64){v.move(c,u,h,ot);return}if(v===he){s(w,u,h);for(let O=0;O<x.length;O++)Ye(x[O],u,h,m);s(c.anchor,u,h);return}if(v===At){A(c,u,h);return}if(m!==2&&b&1&&C)if(m===0)C.beforeEnter(w),s(w,u,h),de(()=>C.enter(w),g);else{const{leave:O,delayLeave:S,afterLeave:H}=C,k=()=>s(w,u,h),G=()=>{O(w,()=>{k(),H&&H()})};S?S(w,k,G):G()}else s(w,u,h)},Oe=(c,u,h,m=!1,g=!1)=>{const{type:w,props:v,ref:C,children:x,dynamicChildren:b,shapeFlag:M,patchFlag:O,dirs:S}=c;if(C!=null&&tn(C,null,h,c,!0),M&256){u.ctx.deactivate(c);return}const H=M&1&&S,k=!pt(c);let G;if(k&&(G=v&&v.onVnodeBeforeUnmount)&&Ce(G,u,c),M&6)mi(c.component,h,m);else{if(M&128){c.suspense.unmount(h,m);return}H&&Pe(c,null,u,"beforeUnmount"),M&64?c.type.remove(c,u,h,g,ot,m):b&&(w!==he||O>0&&O&64)?Le(b,u,h,!1,!0):(w===he&&O&384||!g&&M&16)&&Le(x,u,h),m&&ds(c)}(k&&(G=v&&v.onVnodeUnmounted)||H)&&de(()=>{G&&Ce(G,u,c),H&&Pe(c,null,u,"unmounted")},h)},ds=c=>{const{type:u,el:h,anchor:m,transition:g}=c;if(u===he){gi(h,m);return}if(u===At){$(c);return}const w=()=>{r(h),g&&!g.persisted&&g.afterLeave&&g.afterLeave()};if(c.shapeFlag&1&&g&&!g.persisted){const{leave:v,delayLeave:C}=g,x=()=>v(h,w);C?C(c.el,w,x):x()}else w()},gi=(c,u)=>{let h;for(;c!==u;)h=y(c),r(c),c=h;r(u)},mi=(c,u,h)=>{const{bum:m,scope:g,update:w,subTree:v,um:C}=c;m&&Cn(m),g.stop(),w&&(w.active=!1,Oe(v,c,u,h)),C&&de(C,u),de(()=>{c.isUnmounted=!0},u),u&&u.pendingBranch&&!u.isUnmounted&&c.asyncDep&&!c.asyncResolved&&c.suspenseId===u.pendingId&&(u.deps--,u.deps===0&&u.resolve())},Le=(c,u,h,m=!1,g=!1,w=0)=>{for(let v=w;v<c.length;v++)Oe(c[v],u,h,m,g)},Ht=c=>c.shapeFlag&6?Ht(c.component.subTree):c.shapeFlag&128?c.suspense.next():y(c.anchor||c.el),hs=(c,u,h)=>{c==null?u._vnode&&Oe(u._vnode,null,null,!0):F(u._vnode||null,c,u,null,null,null,h),Ts(),Qt(),u._vnode=c},ot={p:F,um:Oe,m:Ye,r:ds,mt:P,mc:T,pc:Q,pbc:U,n:Ht,o:e};let bn,yn;return t&&([bn,yn]=t(ot)),{render:hs,hydrate:bn,createApp:Qo(hs,bn)}}function Je({effect:e,update:t},n){e.allowRecurse=t.allowRecurse=n}function Jr(e,t,n=!1){const s=e.children,r=t.children;if(N(s)&&N(r))for(let i=0;i<s.length;i++){const o=s[i];let l=r[i];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=r[i]=Ke(r[i]),l.el=o.el),n||Jr(o,l)),l.type===mt&&(l.el=o.el)}}function nl(e){const t=e.slice(),n=[0];let s,r,i,o,l;const f=e.length;for(s=0;s<f;s++){const a=e[s];if(a!==0){if(r=n[n.length-1],e[r]<a){t[s]=r,n.push(s);continue}for(i=0,o=n.length-1;i<o;)l=i+o>>1,e[n[l]]<a?i=l+1:o=l;a<e[n[i]]&&(i>0&&(t[s]=n[i-1]),n[i]=s)}}for(i=n.length,o=n[i-1];i-- >0;)n[i]=o,o=t[o];return n}const sl=e=>e.__isTeleport,he=Symbol(void 0),mt=Symbol(void 0),_e=Symbol(void 0),At=Symbol(void 0),Rt=[];let Re=null;function Xr(e=!1){Rt.push(Re=e?null:[])}function rl(){Rt.pop(),Re=Rt[Rt.length-1]||null}let Mt=1;function Ns(e){Mt+=e}function Zr(e){return e.dynamicChildren=Mt>0?Re||ft:null,rl(),Mt>0&&Re&&Re.push(e),e}function Pc(e,t,n,s,r,i){return Zr(ei(e,t,n,s,r,i,!0))}function Qr(e,t,n,s,r){return Zr(le(e,t,n,s,r,!0))}function nn(e){return e?e.__v_isVNode===!0:!1}function et(e,t){return e.type===t.type&&e.key===t.key}const _n="__vInternal",Gr=({key:e})=>e??null,Yt=({ref:e,ref_key:t,ref_for:n})=>e!=null?re(e)||oe(e)||j(e)?{i:ue,r:e,k:t,f:!!n}:e:null;function ei(e,t=null,n=null,s=0,r=null,i=e===he?0:1,o=!1,l=!1){const f={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Gr(t),ref:t&&Yt(t),scopeId:an,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:s,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:ue};return l?(us(f,n),i&128&&e.normalize(f)):n&&(f.shapeFlag|=re(n)?8:16),Mt>0&&!o&&Re&&(f.patchFlag>0||i&6)&&f.patchFlag!==32&&Re.push(f),f}const le=il;function il(e,t=null,n=null,s=0,r=null,i=!1){if((!e||e===jr)&&(e=_e),nn(e)){const l=Ve(e,t,!0);return n&&us(l,n),Mt>0&&!i&&Re&&(l.shapeFlag&6?Re[Re.indexOf(e)]=l:Re.push(l)),l.patchFlag|=-2,l}if(gl(e)&&(e=e.__vccOpts),t){t=ol(t);let{class:l,style:f}=t;l&&!re(l)&&(t.class=Wn(l)),ee(f)&&(wr(f)&&!N(f)&&(f=ce({},f)),t.style=kn(f))}const o=re(e)?1:vo(e)?128:sl(e)?64:ee(e)?4:j(e)?2:0;return ei(e,t,n,s,r,o,i,!0)}function ol(e){return e?wr(e)||_n in e?ce({},e):e:null}function Ve(e,t,n=!1){const{props:s,ref:r,patchFlag:i,children:o}=e,l=t?ll(s||{},t):s;return{__v_isVNode:!0,__v_skip:!0,type:e.type,props:l,key:l&&Gr(l),ref:t&&t.ref?n&&r?N(r)?r.concat(Yt(t)):[r,Yt(t)]:Yt(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:o,target:e.target,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==he?i===-1?16:i|16:i,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:e.transition,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Ve(e.ssContent),ssFallback:e.ssFallback&&Ve(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx}}function ti(e=" ",t=0){return le(mt,null,e,t)}function Mc(e,t){const n=le(At,null,e);return n.staticCount=t,n}function Sc(e="",t=!1){return t?(Xr(),Qr(_e,null,e)):le(_e,null,e)}function Te(e){return e==null||typeof e=="boolean"?le(_e):N(e)?le(he,null,e.slice()):typeof e=="object"?Ke(e):le(mt,null,String(e))}function Ke(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Ve(e)}function us(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if(N(t))n=16;else if(typeof t=="object")if(s&65){const r=t.default;r&&(r._c&&(r._d=!1),us(e,r()),r._c&&(r._d=!0));return}else{n=32;const r=t._;!r&&!(_n in t)?t._ctx=ue:r===3&&ue&&(ue.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else j(t)?(t={default:t,_ctx:ue},n=32):(t=String(t),s&64?(n=16,t=[ti(t)]):n=8);e.children=t,e.shapeFlag|=n}function ll(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const r in s)if(r==="class")t.class!==s.class&&(t.class=Wn([t.class,s.class]));else if(r==="style")t.style=kn([t.style,s.style]);else if(Lt(r)){const i=t[r],o=s[r];o&&i!==o&&!(N(i)&&i.includes(o))&&(t[r]=i?[].concat(i,o):o)}else r!==""&&(t[r]=s[r])}return t}function Ce(e,t,n,s=null){xe(e,t,7,[n,s])}const cl=Yr();let fl=0;function ul(e,t,n){const s=e.type,r=(t?t.appContext:e.appContext)||cl,i={uid:fl++,vnode:e,type:s,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,scope:new Fi(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Wr(s,r),emitsOptions:Fr(s,r),emit:null,emitted:null,propsDefaults:te,inheritAttrs:s.inheritAttrs,ctx:te,data:te,props:te,attrs:te,slots:te,refs:te,setupState:te,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=bo.bind(null,i),e.ce&&e.ce(i),i}let ie=null;const ni=()=>ie||ue,_t=e=>{ie=e,e.scope.on()},rt=()=>{ie&&ie.scope.off(),ie=null};function si(e){return e.vnode.shapeFlag&4}let St=!1;function al(e,t=!1){St=t;const{props:n,children:s}=e.vnode,r=si(e);Vo(e,n,r,t),Jo(e,s);const i=r?dl(e,t):void 0;return St=!1,i}function dl(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=vt(new Proxy(e.ctx,Bo));const{setup:s}=n;if(s){const r=e.setupContext=s.length>1?ii(e):null;_t(e),bt();const i=We(s,e,0,[e.props,r]);if(yt(),rt(),lr(i)){if(i.then(rt,rt),t)return i.then(o=>{Hs(e,o,t)}).catch(o=>{fn(o,e,0)});e.asyncDep=i}else Hs(e,i,t)}else ri(e,t)}function Hs(e,t,n){j(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ee(t)&&(e.setupState=vr(t)),ri(e,n)}let $s;function ri(e,t,n){const s=e.type;if(!e.render){if(!t&&$s&&!s.render){const r=s.template||cs(e).template;if(r){const{isCustomElement:i,compilerOptions:o}=e.appContext.config,{delimiters:l,compilerOptions:f}=s,a=ce(ce({isCustomElement:i,delimiters:l},o),f);s.render=$s(r,a)}}e.render=s.render||Ie}_t(e),bt(),Do(e),yt(),rt()}function hl(e){return new Proxy(e.attrs,{get(t,n){return be(e,"get","$attrs"),t[n]}})}function ii(e){const t=s=>{e.exposed=s||{}};let n;return{get attrs(){return n||(n=hl(e))},slots:e.slots,emit:e.emit,expose:t}}function as(e){if(e.exposed)return e.exposeProxy||(e.exposeProxy=new Proxy(vr(vt(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Tt)return Tt[n](e)},has(t,n){return n in t||n in Tt}}))}function pl(e,t=!0){return j(e)?e.displayName||e.name:e.name||t&&e.__name}function gl(e){return j(e)&&"__vccOpts"in e}const ve=(e,t)=>ao(e,t,St);function Lc(){return ml().slots}function ml(){const e=ni();return e.setupContext||(e.setupContext=ii(e))}function Bn(e,t,n){const s=arguments.length;return s===2?ee(t)&&!N(t)?nn(t)?le(e,null,[t]):le(e,t):le(e,null,t):(s>3?n=Array.prototype.slice.call(arguments,2):s===3&&nn(n)&&(n=[n]),le(e,t,n))}const _l=Symbol(""),bl=()=>ht(_l),yl="3.2.45",Cl="http://www.w3.org/2000/svg",tt=typeof document<"u"?document:null,Us=tt&&tt.createElement("template"),wl={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const r=t?tt.createElementNS(Cl,e):tt.createElement(e,n?{is:n}:void 0);return e==="select"&&s&&s.multiple!=null&&r.setAttribute("multiple",s.multiple),r},createText:e=>tt.createTextNode(e),createComment:e=>tt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>tt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,r,i){const o=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),!(r===i||!(r=r.nextSibling)););else{Us.innerHTML=s?`<svg>${e}</svg>`:e;const l=Us.content;if(s){const f=l.firstChild;for(;f.firstChild;)l.appendChild(f.firstChild);l.removeChild(f)}t.insertBefore(l,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}};function xl(e,t,n){const s=e._vtc;s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}function El(e,t,n){const s=e.style,r=re(n);if(n&&!r){for(const i in n)Dn(s,i,n[i]);if(t&&!re(t))for(const i in t)n[i]==null&&Dn(s,i,"")}else{const i=s.display;r?t!==n&&(s.cssText=n):t&&e.removeAttribute("style"),"_vod"in e&&(s.display=i)}}const js=/\s*!important$/;function Dn(e,t,n){if(N(n))n.forEach(s=>Dn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=vl(e,t);js.test(n)?e.setProperty(it(s),n.replace(js,""),"important"):e[s]=n}}const Bs=["Webkit","Moz","ms"],vn={};function vl(e,t){const n=vn[t];if(n)return n;let s=Se(t);if(s!=="filter"&&s in e)return vn[t]=s;s=on(s);for(let r=0;r<Bs.length;r++){const i=Bs[r]+s;if(i in e)return vn[t]=i}return t}const Ds="http://www.w3.org/1999/xlink";function Tl(e,t,n,s,r){if(s&&t.startsWith("xlink:"))n==null?e.removeAttributeNS(Ds,t.slice(6,t.length)):e.setAttributeNS(Ds,t,n);else{const i=xi(t);n==null||i&&!rr(n)?e.removeAttribute(t):e.setAttribute(t,i?"":n)}}function Al(e,t,n,s,r,i,o){if(t==="innerHTML"||t==="textContent"){s&&o(s,r,i),e[t]=n??"";return}if(t==="value"&&e.tagName!=="PROGRESS"&&!e.tagName.includes("-")){e._value=n;const f=n??"";(e.value!==f||e.tagName==="OPTION")&&(e.value=f),n==null&&e.removeAttribute(t);return}let l=!1;if(n===""||n==null){const f=typeof e[t];f==="boolean"?n=rr(n):n==null&&f==="string"?(n="",l=!0):f==="number"&&(n=0,l=!0)}try{e[t]=n}catch{}l&&e.removeAttribute(t)}function Rl(e,t,n,s){e.addEventListener(t,n,s)}function Il(e,t,n,s){e.removeEventListener(t,n,s)}function Ol(e,t,n,s,r=null){const i=e._vei||(e._vei={}),o=i[t];if(s&&o)o.value=s;else{const[l,f]=Fl(t);if(s){const a=i[t]=Sl(s,r);Rl(e,l,a,f)}else o&&(Il(e,l,o,f),i[t]=void 0)}}const Ks=/(?:Once|Passive|Capture)$/;function Fl(e){let t;if(Ks.test(e)){t={};let s;for(;s=e.match(Ks);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):it(e.slice(2)),t]}let Tn=0;const Pl=Promise.resolve(),Ml=()=>Tn||(Pl.then(()=>Tn=0),Tn=Date.now());function Sl(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;xe(Ll(s,n.value),t,5,[s])};return n.value=e,n.attached=Ml(),n}function Ll(e,t){if(N(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>r=>!r._stopped&&s&&s(r))}else return t}const ks=/^on[a-z]/,Nl=(e,t,n,s,r=!1,i,o,l,f)=>{t==="class"?xl(e,s,r):t==="style"?El(e,n,s):Lt(t)?qn(t)||Ol(e,t,n,s,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Hl(e,t,s,r))?Al(e,t,s,i,o,l,f):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),Tl(e,t,s,r))};function Hl(e,t,n,s){return s?!!(t==="innerHTML"||t==="textContent"||t in e&&ks.test(t)&&j(n)):t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA"||ks.test(t)&&re(n)?!1:t in e}const Be="transition",wt="animation",oi=(e,{slots:t})=>Bn(Sr,$l(e),t);oi.displayName="Transition";const li={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String};oi.props=ce({},Sr.props,li);const Xe=(e,t=[])=>{N(e)?e.forEach(n=>n(...t)):e&&e(...t)},Ws=e=>e?N(e)?e.some(t=>t.length>1):e.length>1:!1;function $l(e){const t={};for(const I in e)I in li||(t[I]=e[I]);if(e.css===!1)return t;const{name:n="v",type:s,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:o=`${n}-enter-active`,enterToClass:l=`${n}-enter-to`,appearFromClass:f=i,appearActiveClass:a=o,appearToClass:d=l,leaveFromClass:p=`${n}-leave-from`,leaveActiveClass:y=`${n}-leave-active`,leaveToClass:R=`${n}-leave-to`}=e,L=Ul(r),F=L&&L[0],W=L&&L[1],{onBeforeEnter:_,onEnter:E,onEnterCancelled:A,onLeave:$,onLeaveCancelled:D,onBeforeAppear:Z=_,onAppear:q=E,onAppearCancelled:T=A}=t,B=(I,K,P)=>{Ze(I,K?d:l),Ze(I,K?a:o),P&&P()},U=(I,K)=>{I._isLeaving=!1,Ze(I,p),Ze(I,R),Ze(I,y),K&&K()},z=I=>(K,P)=>{const pe=I?q:E,J=()=>B(K,I,P);Xe(pe,[K,J]),qs(()=>{Ze(K,I?f:i),De(K,I?d:l),Ws(pe)||Vs(K,s,F,J)})};return ce(t,{onBeforeEnter(I){Xe(_,[I]),De(I,i),De(I,o)},onBeforeAppear(I){Xe(Z,[I]),De(I,f),De(I,a)},onEnter:z(!1),onAppear:z(!0),onLeave(I,K){I._isLeaving=!0;const P=()=>U(I,K);De(I,p),Dl(),De(I,y),qs(()=>{I._isLeaving&&(Ze(I,p),De(I,R),Ws($)||Vs(I,s,W,P))}),Xe($,[I,P])},onEnterCancelled(I){B(I,!1),Xe(A,[I])},onAppearCancelled(I){B(I,!0),Xe(T,[I])},onLeaveCancelled(I){U(I),Xe(D,[I])}})}function Ul(e){if(e==null)return null;if(ee(e))return[An(e.enter),An(e.leave)];{const t=An(e);return[t,t]}}function An(e){return Jn(e)}function De(e,t){t.split(/\s+/).forEach(n=>n&&e.classList.add(n)),(e._vtc||(e._vtc=new Set)).add(t)}function Ze(e,t){t.split(/\s+/).forEach(s=>s&&e.classList.remove(s));const{_vtc:n}=e;n&&(n.delete(t),n.size||(e._vtc=void 0))}function qs(e){requestAnimationFrame(()=>{requestAnimationFrame(e)})}let jl=0;function Vs(e,t,n,s){const r=e._endId=++jl,i=()=>{r===e._endId&&s()};if(n)return setTimeout(i,n);const{type:o,timeout:l,propCount:f}=Bl(e,t);if(!o)return s();const a=o+"end";let d=0;const p=()=>{e.removeEventListener(a,y),i()},y=R=>{R.target===e&&++d>=f&&p()};setTimeout(()=>{d<f&&p()},l+1),e.addEventListener(a,y)}function Bl(e,t){const n=window.getComputedStyle(e),s=L=>(n[L]||"").split(", "),r=s(`${Be}Delay`),i=s(`${Be}Duration`),o=zs(r,i),l=s(`${wt}Delay`),f=s(`${wt}Duration`),a=zs(l,f);let d=null,p=0,y=0;t===Be?o>0&&(d=Be,p=o,y=i.length):t===wt?a>0&&(d=wt,p=a,y=f.length):(p=Math.max(o,a),d=p>0?o>a?Be:wt:null,y=d?d===Be?i.length:f.length:0);const R=d===Be&&/\b(transform|all)(,|$)/.test(s(`${Be}Property`).toString());return{type:d,timeout:p,propCount:y,hasTransform:R}}function zs(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map((n,s)=>Ys(n)+Ys(e[s])))}function Ys(e){return Number(e.slice(0,-1).replace(",","."))*1e3}function Dl(){return document.body.offsetHeight}const Kl=["ctrl","shift","alt","meta"],kl={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>Kl.some(n=>e[`${n}Key`]&&!t.includes(n))},Nc=(e,t)=>(n,...s)=>{for(let r=0;r<t.length;r++){const i=kl[t[r]];if(i&&i(n,t))return}return e(n,...s)},Wl={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},Hc=(e,t)=>n=>{if(!("key"in n))return;const s=it(n.key);if(t.some(r=>r===s||Wl[r]===s))return e(n)},ql=ce({patchProp:Nl},wl);let Rn,Js=!1;function Vl(){return Rn=Js?Rn:el(ql),Js=!0,Rn}const $c=(...e)=>{const t=Vl().createApp(...e),{mount:n}=t;return t.mount=s=>{const r=zl(s);if(r)return n(r,!0,r instanceof SVGElement)},t};function zl(e){return re(e)?document.querySelector(e):e}const Uc=(e,t)=>{const n=e.__vccOpts||e;for(const[s,r]of t)n[s]=r;return n},Yl="modulepreload",Jl=function(e){return"/vue-office/examples/docs/"+e},Xs={},jc=function(t,n,s){if(!n||n.length===0)return t();const r=document.getElementsByTagName("link");return Promise.all(n.map(i=>{if(i=Jl(i),i in Xs)return;Xs[i]=!0;const o=i.endsWith(".css"),l=o?'[rel="stylesheet"]':"";if(!!s)for(let d=r.length-1;d>=0;d--){const p=r[d];if(p.href===i&&(!o||p.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${i}"]${l}`))return;const a=document.createElement("link");if(a.rel=o?"stylesheet":Yl,o||(a.as="script",a.crossOrigin=""),a.href=i,document.head.appendChild(a),o)return new Promise((d,p)=>{a.addEventListener("load",d),a.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${i}`)))})})).then(()=>t())},Xl=window.__VP_SITE_DATA__,ci=/^[a-z]+:/i,Bc=/^pathname:\/\//,Dc="vitepress-theme-appearance",fi=/#.*$/,Zl=/(index)?\.(md|html)$/,we=typeof document<"u",ui={relativePath:"",title:"404",description:"Not Found",headers:[],frontmatter:{sidebar:!1,layout:"page"},lastUpdated:0,isNotFound:!0};function Ql(e,t,n=!1){if(t===void 0)return!1;if(e=Zs(`/${e}`),n)return new RegExp(t).test(e);if(Zs(t)!==e)return!1;const s=t.match(fi);return s?(we?location.hash:"")===s[0]:!0}function Zs(e){return decodeURI(e).replace(fi,"").replace(Zl,"")}function Gl(e){return ci.test(e)}function ec(e,t){var s,r,i,o,l,f,a;const n=Object.keys(e.locales).find(d=>d!=="root"&&!Gl(d)&&Ql(t,`/${d}/`,!0))||"root";return Object.assign({},e,{localeIndex:n,lang:((s=e.locales[n])==null?void 0:s.lang)??e.lang,dir:((r=e.locales[n])==null?void 0:r.dir)??e.dir,title:((i=e.locales[n])==null?void 0:i.title)??e.title,titleTemplate:((o=e.locales[n])==null?void 0:o.titleTemplate)??e.titleTemplate,description:((l=e.locales[n])==null?void 0:l.description)??e.description,head:di(e.head,((f=e.locales[n])==null?void 0:f.head)??[]),themeConfig:{...e.themeConfig,...(a=e.locales[n])==null?void 0:a.themeConfig}})}function ai(e,t){const n=t.title||e.title,s=t.titleTemplate??e.titleTemplate;if(typeof s=="string"&&s.includes(":title"))return s.replace(/:title/g,n);const r=tc(e.title,s);return`${n}${r}`}function tc(e,t){return t===!1?"":t===!0||t===void 0?` | ${e}`:e===t?"":` | ${t}`}function nc(e,t){const[n,s]=t;if(n!=="meta")return!1;const r=Object.entries(s)[0];return r==null?!1:e.some(([i,o])=>i===n&&o[r[0]]===r[1])}function di(e,t){return[...e.filter(n=>!nc(t,n)),...t]}const sc=/[\u0000-\u001F"#$&*+,:;<=>?[\]^`{|}\u007F]/g,rc=/^[a-z]:/i;function Qs(e){const t=rc.exec(e),n=t?t[0]:"";return n+e.slice(n.length).replace(sc,"_").replace(/(^|\/)_+(?=[^/]*$)/,"$1")}const ic=Symbol(),nt=ro(Xl);function Kc(e){const t=ve(()=>ec(nt.value,e.data.relativePath));return{site:t,theme:ve(()=>t.value.themeConfig),page:ve(()=>e.data),frontmatter:ve(()=>e.data.frontmatter),params:ve(()=>e.data.params),lang:ve(()=>t.value.lang),dir:ve(()=>t.value.dir),localeIndex:ve(()=>t.value.localeIndex||"root"),title:ve(()=>ai(t.value,e.data)),description:ve(()=>e.data.description||t.value.description),isDark:xr(!1)}}function kc(){const e=ht(ic);if(!e)throw new Error("vitepress data not properly injected in app");return e}function oc(e,t){return`${e}${t}`.replace(/\/+/g,"/")}function Gs(e){return ci.test(e)||e.startsWith(".")?e:oc(nt.value.base,e)}function lc(e){let t=e.replace(/\.html$/,"");if(t=decodeURIComponent(t),t=t.replace(/\/$/,"/index"),we){const n="/vue-office/examples/docs/";t=Qs(t.slice(n.length).replace(/\//g,"_")||"index")+".md";let s=__VP_HASH_MAP__[t.toLowerCase()];s||(t=t.endsWith("_index.md")?t.slice(0,-9)+".md":t.slice(0,-3)+"_index.md",s=__VP_HASH_MAP__[t.toLowerCase()]),t=`${n}assets/${t}.${s}.js`}else t=`./${Qs(t.slice(1).replace(/\//g,"_"))}.md.js`;return t}let Jt=[];function Wc(e){Jt.push(e),mn(()=>{Jt=Jt.filter(t=>t!==e)})}const cc=Symbol(),er="http://a.com",fc=()=>({path:"/",component:null,data:ui});function qc(e,t){const n=cn(fc()),s={route:n,go:r};async function r(l=we?location.href:"/"){var a,d;await((a=s.onBeforeRouteChange)==null?void 0:a.call(s,l));const f=new URL(l,er);nt.value.cleanUrls||!f.pathname.endsWith("/")&&!f.pathname.endsWith(".html")&&(f.pathname+=".html",l=f.pathname+f.search+f.hash),we&&l!==location.href&&(history.replaceState({scrollPosition:window.scrollY},document.title),history.pushState(null,"",l)),await o(l),await((d=s.onAfterRouteChanged)==null?void 0:d.call(s,l))}let i=null;async function o(l,f=0,a=!1){const d=new URL(l,er),p=i=d.pathname;try{let y=await e(p);if(i===p){i=null;const{default:R,__pageData:L}=y;if(!R)throw new Error(`Invalid route component: ${R}`);n.path=we?p:Gs(p),n.component=vt(R),n.data=vt(L),we&&Rr(()=>{let F=nt.value.base+L.relativePath.replace(/(?:(^|\/)index)?\.md$/,"$1");if(!nt.value.cleanUrls&&!F.endsWith("/")&&(F+=".html"),F!==d.pathname&&(d.pathname=F,l=F+d.search+d.hash,history.replaceState(null,"",l)),d.hash&&!f){let W=null;try{W=document.querySelector(decodeURIComponent(d.hash))}catch(_){console.warn(_)}if(W){tr(W,d.hash);return}}window.scrollTo(0,f)})}}catch(y){if(!/fetch/.test(y.message)&&!/^\/404(\.html|\/)?$/.test(l)&&console.error(y),!a)try{const R=await fetch(nt.value.base+"hashmap.json");window.__VP_HASH_MAP__=await R.json(),await o(l,f,!0);return}catch{}i===p&&(i=null,n.path=we?p:Gs(p),n.component=t?vt(t):null,n.data=ui)}}return we&&(window.addEventListener("click",l=>{if(l.target.closest("button"))return;const a=l.target.closest("a");if(a&&!a.closest(".vp-raw")&&(a instanceof SVGElement||!a.download)){const{target:d}=a,{href:p,origin:y,pathname:R,hash:L,search:F}=new URL(a.href instanceof SVGAnimatedString?a.href.animVal:a.href,a.baseURI),W=window.location,_=R.match(/\.\w+$/);!l.ctrlKey&&!l.shiftKey&&!l.altKey&&!l.metaKey&&d!=="_blank"&&y===W.origin&&!(_&&_[0]!==".html")&&(l.preventDefault(),R===W.pathname&&F===W.search?L&&L!==W.hash&&(history.pushState(null,"",L),window.dispatchEvent(new Event("hashchange")),tr(a,L,a.classList.contains("header-anchor"))):r(p))}},{capture:!0}),window.addEventListener("popstate",l=>{o(location.href,l.state&&l.state.scrollPosition||0)}),window.addEventListener("hashchange",l=>{l.preventDefault()})),s}function uc(){const e=ht(cc);if(!e)throw new Error("useRouter() is called without provider.");return e}function hi(){return uc().route}function tr(e,t,n=!1){let s=null;try{s=e.classList.contains("header-anchor")?e:document.querySelector(decodeURIComponent(t))}catch(r){console.warn(r)}if(s){const r=nt.value.scrollOffset;let i=0;if(typeof r=="number")i=r;else if(typeof r=="string")i=nr(r);else if(Array.isArray(r))for(const f of r){const a=nr(f);if(a){i=a;break}}const o=parseInt(window.getComputedStyle(s).paddingTop,10),l=window.scrollY+s.getBoundingClientRect().top-i+o;!n||Math.abs(l-window.scrollY)>window.innerHeight?window.scrollTo(0,l):window.scrollTo({left:0,top:l,behavior:"smooth"})}}function nr(e){const t=document.querySelector(e);if(!t)return 0;const n=t.getBoundingClientRect().bottom;return n<0?0:n+24}const sr=()=>Jt.forEach(e=>e()),Vc=Hr({name:"VitePressContent",props:{as:{type:[Object,String],default:"div"}},setup(e){const t=hi();return()=>Bn(e.as,{style:{position:"relative"}},[t.component?Bn(t.component,{onVnodeMounted:sr,onVnodeUpdated:sr}):"404 Page Not Found"])}});function zc(e,t){let n=[],s=!0;const r=i=>{if(s){s=!1;return}n.forEach(o=>document.head.removeChild(o)),n=[],i.forEach(o=>{const l=ac(o);document.head.appendChild(l),n.push(l)})};Ao(()=>{const i=e.data,o=t.value,l=i&&i.description,f=i&&i.frontmatter.head||[];document.title=ai(o,i),document.querySelector("meta[name=description]").setAttribute("content",l||o.description),r(di(o.head,hc(f)))})}function ac([e,t,n]){const s=document.createElement(e);for(const r in t)s.setAttribute(r,t[r]);return n&&(s.innerHTML=n),s}function dc(e){return e[0]==="meta"&&e[1]&&e[1].name==="description"}function hc(e){return e.filter(t=>!dc(t))}const In=new Set,pi=()=>document.createElement("link"),pc=e=>{const t=pi();t.rel="prefetch",t.href=e,document.head.appendChild(t)},gc=e=>{const t=new XMLHttpRequest;t.open("GET",e,t.withCredentials=!0),t.send()};let qt;const mc=we&&(qt=pi())&&qt.relList&&qt.relList.supports&&qt.relList.supports("prefetch")?pc:gc;function Yc(){if(!we||!window.IntersectionObserver)return;let e;if((e=navigator.connection)&&(e.saveData||/2g/.test(e.effectiveType)))return;const t=window.requestIdleCallback||setTimeout;let n=null;const s=()=>{n&&n.disconnect(),n=new IntersectionObserver(i=>{i.forEach(o=>{if(o.isIntersecting){const l=o.target;n.unobserve(l);const{pathname:f}=l;if(!In.has(f)){In.add(f);const a=lc(f);mc(a)}}})}),t(()=>{document.querySelectorAll("#app a").forEach(i=>{const{target:o}=i,{hostname:l,pathname:f}=new URL(i.href instanceof SVGAnimatedString?i.href.animVal:i.href,i.baseURI),a=f.match(/\.\w+$/);a&&a[0]!==".html"||o!=="_blank"&&l===location.hostname&&(f!==location.pathname?n.observe(i):In.add(f))})})};gn(s);const r=hi();zt(()=>r.path,s),mn(()=>{n&&n.disconnect()})}const Jc=Hr({setup(e,{slots:t}){const n=xr(!1);return gn(()=>{n.value=!0}),()=>n.value&&t.default?t.default():null}});function Xc(){if(we){const e=new Map;window.addEventListener("click",t=>{var s;const n=t.target;if(n.matches('div[class*="language-"] > button.copy')){const r=n.parentElement,i=(s=n.nextElementSibling)==null?void 0:s.nextElementSibling;if(!r||!i)return;const o=/language-(shellscript|shell|bash|sh|zsh)/.test(r.className);let l="";i.querySelectorAll("span.line:not(.diff.remove)").forEach(f=>l+=(f.textContent||"")+`
`),l=l.slice(0,-1),o&&(l=l.replace(/^ *(\$|>) /gm,"").trim()),_c(l).then(()=>{n.classList.add("copied"),clearTimeout(e.get(n));const f=setTimeout(()=>{n.classList.remove("copied"),n.blur(),e.delete(n)},2e3);e.set(n,f)})}})}}async function _c(e){try{return navigator.clipboard.writeText(e)}catch{const t=document.createElement("textarea"),n=document.activeElement;t.value=e,t.setAttribute("readonly",""),t.style.contain="strict",t.style.position="absolute",t.style.left="-9999px",t.style.fontSize="12pt";const s=document.getSelection(),r=s?s.rangeCount>0&&s.getRangeAt(0):null;document.body.appendChild(t),t.select(),t.selectionStart=0,t.selectionEnd=e.length,document.execCommand("copy"),document.body.removeChild(t),r&&(s.removeAllRanges(),s.addRange(r)),n&&n.focus()}}function Zc(){we&&window.addEventListener("click",e=>{var n,s;const t=e.target;if(t.matches(".vp-code-group input")){const r=(n=t.parentElement)==null?void 0:n.parentElement,i=Array.from((r==null?void 0:r.querySelectorAll("input"))||[]).indexOf(t),o=r==null?void 0:r.querySelector('div[class*="language-"].active'),l=(s=r==null?void 0:r.querySelectorAll('div[class*="language-"]:not(.language-id)'))==null?void 0:s[i];o&&l&&o!==l&&(o.classList.remove("active"),l.classList.add("active"))}})}export{Tc as $,Qr as A,yo as B,Sc as C,ll as D,le as E,he as F,Ac as G,kn as H,Rc as I,ci as J,we as K,Ic as L,Ec as M,vc as N,Mc as O,Bc as P,Dc as Q,ht as R,To as S,oi as T,No as U,Wc as V,ro as W,Rr as X,Hc as Y,Fc as Z,Uc as _,ti as a,Nc as a0,Lc as a1,zc as a2,Yc as a3,Xc as a4,Zc as a5,Bn as a6,cc as a7,Kc as a8,ic as a9,Vc as aa,Jc as ab,nt as ac,$c as ad,qc as ae,lc as af,jc as ag,oo as b,Pc as c,Hr as d,xc as e,Cr as f,wc as g,xr as h,yc as i,Cc as j,Ao as k,ve as l,ni as m,Wn as n,Xr as o,gn as p,Gl as q,Oc as r,Gs as s,bc as t,kc as u,Ql as v,zt as w,hi as x,mn as y,ei as z};
