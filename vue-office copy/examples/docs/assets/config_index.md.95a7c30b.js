import{_ as s,c as a,o as n,O as l}from"./chunks/framework.935eb42c.js";const d=JSON.parse('{"title":"属性","description":"","frontmatter":{},"headers":[],"relativePath":"config/index.md"}'),o={name:"config/index.md"},p=l(`<h1 id="属性" tabindex="-1">属性 <a class="header-anchor" href="#属性" aria-label="Permalink to &quot;属性&quot;">​</a></h1><h2 id="src" tabindex="-1">src <a class="header-anchor" href="#src" aria-label="Permalink to &quot;src&quot;">​</a></h2><ul><li>类型：String, ArrayBuffer, Blob</li></ul><p>文档地址，文件在CDN或服务器上的地址，或者是通过FileReader读取的文件ArrayBuffer或者Blob格式。</p><h2 id="request-options" tabindex="-1">request-options <a class="header-anchor" href="#request-options" aria-label="Permalink to &quot;request-options&quot;">​</a></h2><ul><li>类型：Object</li></ul><p>如果属性src是个文件地址，组件内部会通过window.fetch进行请求，对应window.fetch的请求参数，可以用来设置header等请求信息。</p><h2 id="options" tabindex="-1">options <a class="header-anchor" href="#options" aria-label="Permalink to &quot;options&quot;">​</a></h2><ul><li>类型： Object</li></ul><p>预览需要的一些特殊配置，不同预览组件可配置项各不相同。</p><ul><li>excel预览</li></ul><p>excel相关的配置，目前支持配置项很少。</p><p>minColLength: excel最少渲染多少列，如果想实现xlsx文件内容有几列，就渲染几列，可以将此值设置为0.</p><p>minRowLength: excel最少渲染多少行，如果想实现根据xlsx实际函数渲染，可以将此值设置为0.</p><p>widthOffset：在默认渲染的列表宽度上再加 Npx宽</p><p>heightOffset：在默认渲染的列表高度上再加 Npx高</p><p>beforeTransformData: 底层通过exceljs获取excel文件内容，通过该钩子函数，可以对获取的excel文件内容进行修改，比如某个单元格的数据显示不正确，可以在此自行修改每个单元格的value值。</p><p>transformData：将获取到的excel数据进行处理之后且渲染到页面之前，可通过transformData对即将渲染的数据及样式进行修改，此时每个单元格的text值就是即将渲染到页面上的内容。</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">minColLength</span><span style="color:#89DDFF;">&quot;</span><span style="color:#F07178;">: </span><span style="color:#F78C6C;">20</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">minRowLength</span><span style="color:#89DDFF;">&quot;</span><span style="color:#F07178;">: </span><span style="color:#F78C6C;">100</span><span style="color:#89DDFF;">,</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">widthOffset</span><span style="color:#89DDFF;">&quot;</span><span style="color:#F07178;">: </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//在默认渲染的列表宽度上再加10px宽</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">heightOffset</span><span style="color:#89DDFF;">&quot;</span><span style="color:#F07178;">: </span><span style="color:#F78C6C;">10</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//在默认渲染的列表高度上再加10px高</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">beforeTransformData</span><span style="color:#89DDFF;">&quot;</span><span style="color:#F07178;">: </span><span style="color:#C792EA;">function</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">(</span><span style="color:#A6ACCD;font-style:italic;">workbookData</span><span style="color:#89DDFF;">){</span></span>
<span class="line"><span style="color:#89DDFF;">        </span><span style="color:#676E95;font-style:italic;">//修改workbookData，可以打印出来看看数据格式</span></span>
<span class="line"><span style="color:#F07178;">        </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">workbookData</span><span style="color:#89DDFF;">;</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">},</span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">transformData</span><span style="color:#89DDFF;">&quot;</span><span style="color:#F07178;">: </span><span style="color:#C792EA;">function</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">(</span><span style="color:#A6ACCD;font-style:italic;">workbookData</span><span style="color:#89DDFF;">){</span></span>
<span class="line"><span style="color:#89DDFF;">      </span><span style="color:#676E95;font-style:italic;">//修改workbookData，可以打印出来看看数据格式</span></span>
<span class="line"><span style="color:#F07178;">      </span><span style="color:#89DDFF;font-style:italic;">return</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">workbookData</span><span style="color:#89DDFF;">;</span><span style="color:#F07178;">    </span></span>
<span class="line"><span style="color:#F07178;">  </span><span style="color:#89DDFF;">}</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><ul><li>docx预览</li></ul><p>docx预览组件支持的可配置项如下</p><div class="language-typescript"><button title="Copy Code" class="copy"></button><span class="lang">typescript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">className</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">string</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">&quot;</span><span style="color:#C3E88D;">docx</span><span style="color:#89DDFF;">&quot;</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//class name/prefix for default and document style classes</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">inWrapper</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">boolean</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//enables rendering of wrapper around document content</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">ignoreWidth</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">boolean</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//disables rendering width of page</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">ignoreHeight</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">boolean</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//disables rendering height of page</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">ignoreFonts</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">boolean</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//disables fonts rendering</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">breakPages</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">boolean</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//enables page breaking on page breaks</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">ignoreLastRenderedPageBreak</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">boolean</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//disables page breaking on lastRenderedPageBreak elements</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">experimental</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">boolean</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//enables experimental features (tab stops calculation)</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">trimXmlDeclaration</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">boolean</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">true</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//if true, xml declaration will be removed from xml documents before parsing</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">useBase64URL</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">boolean</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//if true, images, fonts, etc. will be converted to base 64 URL, otherwise URL.createObjectURL is used</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">useMathMLPolyfill</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">boolean</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//includes MathML polyfills for chrome, edge, etc.</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">showChanges</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//enables experimental rendering of document changes (inserions/deletions)</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#FFCB6B;">debug</span><span style="color:#89DDFF;">:</span><span style="color:#F07178;"> </span><span style="color:#A6ACCD;">boolean</span><span style="color:#F07178;"> </span><span style="color:#89DDFF;">=</span><span style="color:#F07178;"> </span><span style="color:#FF9CAC;">false</span><span style="color:#89DDFF;">,</span><span style="color:#F07178;"> </span><span style="color:#676E95;font-style:italic;">//enables additional logging</span></span>
<span class="line"><span style="color:#F07178;">    </span><span style="color:#89DDFF;">}</span></span></code></pre></div><ul><li>pdf预览</li></ul><p>pdf 预览组件支持的可配置项如下</p><div class="language-javascript"><button title="Copy Code" class="copy"></button><span class="lang">javascript</span><pre class="shiki material-theme-palenight"><code><span class="line"><span style="color:#C792EA;">const</span><span style="color:#A6ACCD;"> options </span><span style="color:#89DDFF;">=</span><span style="color:#A6ACCD;"> </span><span style="color:#89DDFF;">{</span></span>
<span class="line"><span style="color:#A6ACCD;">    </span><span style="color:#F07178;">width</span><span style="color:#89DDFF;">:</span><span style="color:#A6ACCD;"> </span><span style="color:#F78C6C;">500</span><span style="color:#89DDFF;">,</span><span style="color:#A6ACCD;"> </span><span style="color:#676E95;font-style:italic;">//number，可不传，用来控制pdf预览的宽度，默认根据文档实际宽度计算</span></span>
<span class="line"><span style="color:#A6ACCD;">    </span><span style="color:#F07178;">httpHeaders</span><span style="color:#89DDFF;">:</span><span style="color:#A6ACCD;"> </span><span style="color:#89DDFF;">{},</span><span style="color:#A6ACCD;"> </span><span style="color:#676E95;font-style:italic;">//object, Basic authentication headers</span></span>
<span class="line"><span style="color:#A6ACCD;">    </span><span style="color:#F07178;">password</span><span style="color:#89DDFF;">:</span><span style="color:#A6ACCD;"> </span><span style="color:#89DDFF;">&#39;&#39;</span><span style="color:#89DDFF;">,</span><span style="color:#A6ACCD;"> </span><span style="color:#676E95;font-style:italic;">//string, 加密pdf的密码</span></span>
<span class="line"><span style="color:#89DDFF;">    </span><span style="color:#676E95;font-style:italic;">//更多配置参见 https://mozilla.github.io/pdf.js/api/draft/module-pdfjsLib.html</span></span>
<span class="line"><span style="color:#89DDFF;">}</span></span></code></pre></div><h2 id="staticfileurl-pdf特有属性" tabindex="-1">staticFileUrl [pdf特有属性] <a class="header-anchor" href="#staticfileurl-pdf特有属性" aria-label="Permalink to &quot;staticFileUrl [pdf特有属性]&quot;">​</a></h2><ul><li>类型： String</li></ul><p>pdf渲染时可能会请求一些bcmap文件，这些文件默认从 <a href="https://unpkg.com/pdfjs-dist@3.1.81/" target="_blank" rel="noreferrer">https://unpkg.com/pdfjs-dist@3.1.81/</a> 加载，但是可能存在网络不通问题，如果遇到这种问题，可以将这些文件放到自己静态目录，然后修改该属性，告诉组件去这里请求bcmap文件。</p><p>涉及的文件存放在当前github项目中examples/public/cmaps目录下，可将cmaps目录复制到你的静态服务目录下，然后修改staticFileUrl为cmaps文件夹对应的父地址，必须已http或https开头，如 <a href="http://yourdomain/static/" target="_blank" rel="noreferrer">http://yourdomain/static/</a></p>`,29),e=[p];function t(c,r,F,y,i,D){return n(),a("div",null,e)}const f=s(o,[["render",t]]);export{d as __pageData,f as default};
