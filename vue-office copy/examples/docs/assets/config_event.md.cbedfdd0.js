import{_ as e,c as r,o as a,O as t}from"./chunks/framework.935eb42c.js";const m=JSON.parse('{"title":"事件","description":"","frontmatter":{},"headers":[],"relativePath":"config/event.md"}'),o={name:"config/event.md"},n=t('<h1 id="事件" tabindex="-1">事件 <a class="header-anchor" href="#事件" aria-label="Permalink to &quot;事件&quot;">​</a></h1><h2 id="rendered" tabindex="-1">rendered <a class="header-anchor" href="#rendered" aria-label="Permalink to &quot;rendered&quot;">​</a></h2><p>渲染完成事件，可以在该事件中处理关闭loading操作等。</p><p>首次渲染完成及每次src变化之后渲染完成都会触发该事件。</p><h2 id="error" tabindex="-1">error <a class="header-anchor" href="#error" aria-label="Permalink to &quot;error&quot;">​</a></h2><p>失败事件，各种失败都会触发该事件，包括网络请求失败，渲染失败等</p>',6),d=[n];function c(s,i,_,l,h,p){return a(),r("div",null,d)}const u=e(o,[["render",c]]);export{m as __pageData,u as default};
