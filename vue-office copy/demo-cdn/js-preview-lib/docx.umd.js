!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t(require("stream"),require("events"),require("buffer"),require("util")):"function"==typeof define&&define.amd?define(["stream","events","buffer","util"],t):(e="undefined"!=typeof globalThis?globalThis:e||self).jsPreviewDocx=t(e.require$$0,e.require$$2,e.require$$0$1,e.require$$1)}(this,(function(e,t,r,n){"use strict";void 0===window.setImmediate&&(window.setImmediate=function(e,...t){setTimeout((()=>e(t)))});var a,i,s,o,l,u="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},c={exports:{}},h={},d={},f={},p={exports:{}},m={exports:{}};function g(){if(a)return m.exports;return a=1,"undefined"==typeof process||!process.version||0===process.version.indexOf("v0.")||0===process.version.indexOf("v1.")&&0!==process.version.indexOf("v1.8.")?m.exports={nextTick:function(e,t,r,n){if("function"!=typeof e)throw new TypeError('"callback" argument must be a function');var a,i,s=arguments.length;switch(s){case 0:case 1:return process.nextTick(e);case 2:return process.nextTick((function(){e.call(null,t)}));case 3:return process.nextTick((function(){e.call(null,t,r)}));case 4:return process.nextTick((function(){e.call(null,t,r,n)}));default:for(a=new Array(s-1),i=0;i<a.length;)a[i++]=arguments[i];return process.nextTick((function(){e.apply(null,a)}))}}}:m.exports=process,m.exports}function b(){return l?o:(l=1,o=e)}var y,v={exports:{}};function w(){return y||(y=1,function(e,t){var n=r,a=n.Buffer;function i(e,t){for(var r in e)t[r]=e[r]}function s(e,t,r){return a(e,t,r)}a.from&&a.alloc&&a.allocUnsafe&&a.allocUnsafeSlow?e.exports=n:(i(n,t),t.Buffer=s),i(a,s),s.from=function(e,t,r){if("number"==typeof e)throw new TypeError("Argument must not be a number");return a(e,t,r)},s.alloc=function(e,t,r){if("number"!=typeof e)throw new TypeError("Argument must be a number");var n=a(e);return void 0!==t?"string"==typeof r?n.fill(t,r):n.fill(t):n.fill(0),n},s.allocUnsafe=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return a(e)},s.allocUnsafeSlow=function(e){if("number"!=typeof e)throw new TypeError("Argument must be a number");return n.SlowBuffer(e)}}(v,v.exports)),v.exports}var _,k={};function S(){if(_)return k;function e(e){return Object.prototype.toString.call(e)}return _=1,k.isArray=function(t){return Array.isArray?Array.isArray(t):"[object Array]"===e(t)},k.isBoolean=function(e){return"boolean"==typeof e},k.isNull=function(e){return null===e},k.isNullOrUndefined=function(e){return null==e},k.isNumber=function(e){return"number"==typeof e},k.isString=function(e){return"string"==typeof e},k.isSymbol=function(e){return"symbol"==typeof e},k.isUndefined=function(e){return void 0===e},k.isRegExp=function(t){return"[object RegExp]"===e(t)},k.isObject=function(e){return"object"==typeof e&&null!==e},k.isDate=function(t){return"[object Date]"===e(t)},k.isError=function(t){return"[object Error]"===e(t)||t instanceof Error},k.isFunction=function(e){return"function"==typeof e},k.isPrimitive=function(e){return null===e||"boolean"==typeof e||"number"==typeof e||"string"==typeof e||"symbol"==typeof e||void 0===e},k.isBuffer=r.Buffer.isBuffer,k}var x,C,A={exports:{}},P={exports:{}};function T(){if(C)return A.exports;C=1;try{var e=require("util");if("function"!=typeof e.inherits)throw"";A.exports=e.inherits}catch(e){A.exports=(x||(x=1,"function"==typeof Object.create?P.exports=function(e,t){t&&(e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}))}:P.exports=function(e,t){if(t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e}}),P.exports)}return A.exports}var E,D,N,B,M,R,O,I,z,F={exports:{}};function L(){return E||(E=1,function(e){var t=w().Buffer,r=n;e.exports=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.head=null,this.tail=null,this.length=0}return e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},e.prototype.clear=function(){this.head=this.tail=null,this.length=0},e.prototype.join=function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r},e.prototype.concat=function(e){if(0===this.length)return t.alloc(0);for(var r,n,a,i=t.allocUnsafe(e>>>0),s=this.head,o=0;s;)r=s.data,n=i,a=o,r.copy(n,a),o+=s.data.length,s=s.next;return i},e}(),r&&r.inspect&&r.inspect.custom&&(e.exports.prototype[r.inspect.custom]=function(){var e=r.inspect({length:this.length});return this.constructor.name+" "+e})}(F)),F.exports}function j(){if(N)return D;N=1;var e=g();function t(e,t){e.emit("error",t)}return D={destroy:function(r,n){var a=this,i=this._readableState&&this._readableState.destroyed,s=this._writableState&&this._writableState.destroyed;return i||s?(n?n(r):r&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,e.nextTick(t,this,r)):e.nextTick(t,this,r)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(r||null,(function(r){!n&&r?a._writableState?a._writableState.errorEmitted||(a._writableState.errorEmitted=!0,e.nextTick(t,a,r)):e.nextTick(t,a,r):n&&n(r)})),this)},undestroy:function(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}}}function U(){if(O)return R;O=1;var e=g();function t(e){var t=this;this.next=null,this.entry=null,this.finish=function(){!function(e,t,r){var n=e.entry;e.entry=null;for(;n;){var a=n.callback;t.pendingcb--,a(r),n=n.next}t.corkedRequestsFree.next=e}(t,e)}}R=m;var r,a=!process.browser&&["v0.10","v0.9."].indexOf(process.version.slice(0,5))>-1?setImmediate:e.nextTick;m.WritableState=p;var i=Object.create(S());i.inherits=T();var s={deprecate:M?B:(M=1,B=n.deprecate)},o=b(),l=w().Buffer,c=(void 0!==u?u:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var h,d=j();function f(){}function p(n,i){r=r||W(),n=n||{};var s=i instanceof r;this.objectMode=!!n.objectMode,s&&(this.objectMode=this.objectMode||!!n.writableObjectMode);var o=n.highWaterMark,l=n.writableHighWaterMark,u=this.objectMode?16:16384;this.highWaterMark=o||0===o?o:s&&(l||0===l)?l:u,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var c=!1===n.decodeStrings;this.decodeStrings=!c,this.defaultEncoding=n.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){!function(t,r){var n=t._writableState,i=n.sync,s=n.writecb;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(n),r)!function(t,r,n,a,i){--r.pendingcb,n?(e.nextTick(i,a),e.nextTick(C,t,r),t._writableState.errorEmitted=!0,t.emit("error",a)):(i(a),t._writableState.errorEmitted=!0,t.emit("error",a),C(t,r))}(t,n,i,r,s);else{var o=k(n);o||n.corked||n.bufferProcessing||!n.bufferedRequest||_(t,n),i?a(v,t,n,o,s):v(t,n,o,s)}}(i,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new t(this)}function m(e){if(r=r||W(),!(h.call(m,this)||this instanceof r))return new m(e);this._writableState=new p(e,this),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev),"function"==typeof e.destroy&&(this._destroy=e.destroy),"function"==typeof e.final&&(this._final=e.final)),o.call(this)}function y(e,t,r,n,a,i,s){t.writelen=n,t.writecb=s,t.writing=!0,t.sync=!0,r?e._writev(a,t.onwrite):e._write(a,i,t.onwrite),t.sync=!1}function v(e,t,r,n){r||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,n(),C(e,t)}function _(e,r){r.bufferProcessing=!0;var n=r.bufferedRequest;if(e._writev&&n&&n.next){var a=r.bufferedRequestCount,i=new Array(a),s=r.corkedRequestsFree;s.entry=n;for(var o=0,l=!0;n;)i[o]=n,n.isBuf||(l=!1),n=n.next,o+=1;i.allBuffers=l,y(e,r,!0,r.length,i,"",s.finish),r.pendingcb++,r.lastBufferedRequest=null,s.next?(r.corkedRequestsFree=s.next,s.next=null):r.corkedRequestsFree=new t(r),r.bufferedRequestCount=0}else{for(;n;){var u=n.chunk,c=n.encoding,h=n.callback;if(y(e,r,!1,r.objectMode?1:u.length,u,c,h),n=n.next,r.bufferedRequestCount--,r.writing)break}null===n&&(r.lastBufferedRequest=null)}r.bufferedRequest=n,r.bufferProcessing=!1}function k(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function x(e,t){e._final((function(r){t.pendingcb--,r&&e.emit("error",r),t.prefinished=!0,e.emit("prefinish"),C(e,t)}))}function C(t,r){var n=k(r);return n&&(!function(t,r){r.prefinished||r.finalCalled||("function"==typeof t._final?(r.pendingcb++,r.finalCalled=!0,e.nextTick(x,t,r)):(r.prefinished=!0,t.emit("prefinish")))}(t,r),0===r.pendingcb&&(r.finished=!0,t.emit("finish"))),n}return i.inherits(m,o),p.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},function(){try{Object.defineProperty(p.prototype,"buffer",{get:s.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(e){}}(),"function"==typeof Symbol&&Symbol.hasInstance&&"function"==typeof Function.prototype[Symbol.hasInstance]?(h=Function.prototype[Symbol.hasInstance],Object.defineProperty(m,Symbol.hasInstance,{value:function(e){return!!h.call(this,e)||this===m&&(e&&e._writableState instanceof p)}})):h=function(e){return e instanceof this},m.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},m.prototype.write=function(t,r,n){var a,i=this._writableState,s=!1,o=!i.objectMode&&(a=t,l.isBuffer(a)||a instanceof c);return o&&!l.isBuffer(t)&&(t=function(e){return l.from(e)}(t)),"function"==typeof r&&(n=r,r=null),o?r="buffer":r||(r=i.defaultEncoding),"function"!=typeof n&&(n=f),i.ended?function(t,r){var n=new Error("write after end");t.emit("error",n),e.nextTick(r,n)}(this,n):(o||function(t,r,n,a){var i=!0,s=!1;return null===n?s=new TypeError("May not write null values to stream"):"string"==typeof n||void 0===n||r.objectMode||(s=new TypeError("Invalid non-string/buffer chunk")),s&&(t.emit("error",s),e.nextTick(a,s),i=!1),i}(this,i,t,n))&&(i.pendingcb++,s=function(e,t,r,n,a,i){if(!r){var s=function(e,t,r){e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=l.from(t,r));return t}(t,n,a);n!==s&&(r=!0,a="buffer",n=s)}var o=t.objectMode?1:n.length;t.length+=o;var u=t.length<t.highWaterMark;u||(t.needDrain=!0);if(t.writing||t.corked){var c=t.lastBufferedRequest;t.lastBufferedRequest={chunk:n,encoding:a,isBuf:r,callback:i,next:null},c?c.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else y(e,t,!1,o,n,a,i);return u}(this,i,o,t,r,n)),s},m.prototype.cork=function(){this._writableState.corked++},m.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.bufferProcessing||!e.bufferedRequest||_(this,e))},m.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},Object.defineProperty(m.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),m.prototype._write=function(e,t,r){r(new Error("_write() is not implemented"))},m.prototype._writev=null,m.prototype.end=function(t,r,n){var a=this._writableState;"function"==typeof t?(n=t,t=null,r=null):"function"==typeof r&&(n=r,r=null),null!=t&&this.write(t,r),a.corked&&(a.corked=1,this.uncork()),a.ending||function(t,r,n){r.ending=!0,C(t,r),n&&(r.finished?e.nextTick(n):t.once("finish",n));r.ended=!0,t.writable=!1}(this,a,n)},Object.defineProperty(m.prototype,"destroyed",{get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(e){this._writableState&&(this._writableState.destroyed=e)}}),m.prototype.destroy=d.destroy,m.prototype._undestroy=d.undestroy,m.prototype._destroy=function(e,t){this.end(),t(e)},R}function W(){if(z)return I;z=1;var e=g(),t=Object.keys||function(e){var t=[];for(var r in e)t.push(r);return t};I=l;var r=Object.create(S());r.inherits=T();var n=ee(),a=U();r.inherits(l,n);for(var i=t(a.prototype),s=0;s<i.length;s++){var o=i[s];l.prototype[o]||(l.prototype[o]=a.prototype[o])}function l(e){if(!(this instanceof l))return new l(e);n.call(this,e),a.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",u)}function u(){this.allowHalfOpen||this._writableState.ended||e.nextTick(c,this)}function c(e){e.end()}return Object.defineProperty(l.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(l.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(e){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=e,this._writableState.destroyed=e)}}),l.prototype._destroy=function(t,r){this.push(null),this.end(),e.nextTick(r,t)},I}var H,$,Z,V,q,X,K,G,J,Y={};function Q(){if(H)return Y;H=1;var e=w().Buffer,t=e.isEncoding||function(e){switch((e=""+e)&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function r(r){var n;switch(this.encoding=function(r){var n=function(e){if(!e)return"utf8";for(var t;;)switch(e){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return e;default:if(t)return;e=(""+e).toLowerCase(),t=!0}}(r);if("string"!=typeof n&&(e.isEncoding===t||!t(r)))throw new Error("Unknown encoding: "+r);return n||r}(r),this.encoding){case"utf16le":this.text=i,this.end=s,n=4;break;case"utf8":this.fillLast=a,n=4;break;case"base64":this.text=o,this.end=l,n=3;break;default:return this.write=u,void(this.end=c)}this.lastNeed=0,this.lastTotal=0,this.lastChar=e.allocUnsafe(n)}function n(e){return e<=127?0:e>>5==6?2:e>>4==14?3:e>>3==30?4:e>>6==2?-1:-2}function a(e){var t=this.lastTotal-this.lastNeed,r=function(e,t,r){if(128!=(192&t[0]))return e.lastNeed=0,"�";if(e.lastNeed>1&&t.length>1){if(128!=(192&t[1]))return e.lastNeed=1,"�";if(e.lastNeed>2&&t.length>2&&128!=(192&t[2]))return e.lastNeed=2,"�"}}(this,e);return void 0!==r?r:this.lastNeed<=e.length?(e.copy(this.lastChar,t,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal)):(e.copy(this.lastChar,t,0,e.length),void(this.lastNeed-=e.length))}function i(e,t){if((e.length-t)%2==0){var r=e.toString("utf16le",t);if(r){var n=r.charCodeAt(r.length-1);if(n>=55296&&n<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1],r.slice(0,-1)}return r}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=e[e.length-1],e.toString("utf16le",t,e.length-1)}function s(e){var t=e&&e.length?this.write(e):"";if(this.lastNeed){var r=this.lastTotal-this.lastNeed;return t+this.lastChar.toString("utf16le",0,r)}return t}function o(e,t){var r=(e.length-t)%3;return 0===r?e.toString("base64",t):(this.lastNeed=3-r,this.lastTotal=3,1===r?this.lastChar[0]=e[e.length-1]:(this.lastChar[0]=e[e.length-2],this.lastChar[1]=e[e.length-1]),e.toString("base64",t,e.length-r))}function l(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+this.lastChar.toString("base64",0,3-this.lastNeed):t}function u(e){return e.toString(this.encoding)}function c(e){return e&&e.length?this.write(e):""}return Y.StringDecoder=r,r.prototype.write=function(e){if(0===e.length)return"";var t,r;if(this.lastNeed){if(void 0===(t=this.fillLast(e)))return"";r=this.lastNeed,this.lastNeed=0}else r=0;return r<e.length?t?t+this.text(e,r):this.text(e,r):t||""},r.prototype.end=function(e){var t=e&&e.length?this.write(e):"";return this.lastNeed?t+"�":t},r.prototype.text=function(e,t){var r=function(e,t,r){var a=t.length-1;if(a<r)return 0;var i=n(t[a]);if(i>=0)return i>0&&(e.lastNeed=i-1),i;if(--a<r||-2===i)return 0;if(i=n(t[a]),i>=0)return i>0&&(e.lastNeed=i-2),i;if(--a<r||-2===i)return 0;if(i=n(t[a]),i>=0)return i>0&&(2===i?i=0:e.lastNeed=i-3),i;return 0}(this,e,t);if(!this.lastNeed)return e.toString("utf8",t);this.lastTotal=r;var a=e.length-(r-this.lastNeed);return e.copy(this.lastChar,0,a),e.toString("utf8",t,a)},r.prototype.fillLast=function(e){if(this.lastNeed<=e.length)return e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);e.copy(this.lastChar,this.lastTotal-this.lastNeed,0,e.length),this.lastNeed-=e.length},Y}function ee(){if(Z)return $;Z=1;var e=g();$=x;var r,a=function(){if(s)return i;s=1;var e={}.toString;return i=Array.isArray||function(t){return"[object Array]"==e.call(t)}}();x.ReadableState=k,t.EventEmitter;var o=function(e,t){return e.listeners(t).length},l=b(),c=w().Buffer,h=(void 0!==u?u:"undefined"!=typeof window?window:"undefined"!=typeof self?self:{}).Uint8Array||function(){};var d=Object.create(S());d.inherits=T();var f=n,p=void 0;p=f&&f.debuglog?f.debuglog("stream"):function(){};var m,y=L(),v=j();d.inherits(x,l);var _=["error","close","destroy","pause","resume"];function k(e,t){e=e||{};var n=t instanceof(r=r||W());this.objectMode=!!e.objectMode,n&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var a=e.highWaterMark,i=e.readableHighWaterMark,s=this.objectMode?16:16384;this.highWaterMark=a||0===a?a:n&&(i||0===i)?i:s,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new y,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(m||(m=Q().StringDecoder),this.decoder=new m(e.encoding),this.encoding=e.encoding)}function x(e){if(r=r||W(),!(this instanceof x))return new x(e);this._readableState=new k(e,this),this.readable=!0,e&&("function"==typeof e.read&&(this._read=e.read),"function"==typeof e.destroy&&(this._destroy=e.destroy)),l.call(this)}function C(e,t,r,n,a){var i,s=e._readableState;null===t?(s.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,D(e)}(e,s)):(a||(i=function(e,t){var r;n=t,c.isBuffer(n)||n instanceof h||"string"==typeof t||void 0===t||e.objectMode||(r=new TypeError("Invalid non-string/buffer chunk"));var n;return r}(s,t)),i?e.emit("error",i):s.objectMode||t&&t.length>0?("string"==typeof t||s.objectMode||Object.getPrototypeOf(t)===c.prototype||(t=function(e){return c.from(e)}(t)),n?s.endEmitted?e.emit("error",new Error("stream.unshift() after end event")):A(e,s,t,!0):s.ended?e.emit("error",new Error("stream.push() after EOF")):(s.reading=!1,s.decoder&&!r?(t=s.decoder.write(t),s.objectMode||0!==t.length?A(e,s,t,!1):B(e,s)):A(e,s,t,!1))):n||(s.reading=!1));return function(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}(s)}function A(e,t,r,n){t.flowing&&0===t.length&&!t.sync?(e.emit("data",r),e.read(0)):(t.length+=t.objectMode?1:r.length,n?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&D(e)),B(e,t)}Object.defineProperty(x.prototype,"destroyed",{get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(e){this._readableState&&(this._readableState.destroyed=e)}}),x.prototype.destroy=v.destroy,x.prototype._undestroy=v.undestroy,x.prototype._destroy=function(e,t){this.push(null),t(e)},x.prototype.push=function(e,t){var r,n=this._readableState;return n.objectMode?r=!0:"string"==typeof e&&((t=t||n.defaultEncoding)!==n.encoding&&(e=c.from(e,t),t=""),r=!0),C(this,e,t,!1,r)},x.prototype.unshift=function(e){return C(this,e,null,!0,!1)},x.prototype.isPaused=function(){return!1===this._readableState.flowing},x.prototype.setEncoding=function(e){return m||(m=Q().StringDecoder),this._readableState.decoder=new m(e),this._readableState.encoding=e,this};var P=8388608;function E(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=P?e=P:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function D(t){var r=t._readableState;r.needReadable=!1,r.emittedReadable||(p("emitReadable",r.flowing),r.emittedReadable=!0,r.sync?e.nextTick(N,t):N(t))}function N(e){p("emit readable"),e.emit("readable"),I(e)}function B(t,r){r.readingMore||(r.readingMore=!0,e.nextTick(M,t,r))}function M(e,t){for(var r=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(p("maybeReadMore read 0"),e.read(0),r!==t.length);)r=t.length;t.readingMore=!1}function R(e){p("readable nexttick read 0"),e.read(0)}function O(e,t){t.reading||(p("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),I(e),t.flowing&&!t.reading&&e.read(0)}function I(e){var t=e._readableState;for(p("flow",t.flowing);t.flowing&&null!==e.read(););}function z(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):r=function(e,t,r){var n;e<t.head.data.length?(n=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):n=e===t.head.data.length?t.shift():r?function(e,t){var r=t.head,n=1,a=r.data;e-=a.length;for(;r=r.next;){var i=r.data,s=e>i.length?i.length:e;if(s===i.length?a+=i:a+=i.slice(0,e),0===(e-=s)){s===i.length?(++n,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=i.slice(s));break}++n}return t.length-=n,a}(e,t):function(e,t){var r=c.allocUnsafe(e),n=t.head,a=1;n.data.copy(r),e-=n.data.length;for(;n=n.next;){var i=n.data,s=e>i.length?i.length:e;if(i.copy(r,r.length-e,0,s),0===(e-=s)){s===i.length?(++a,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=i.slice(s));break}++a}return t.length-=a,r}(e,t);return n}(e,t.buffer,t.decoder),r);var r}function F(t){var r=t._readableState;if(r.length>0)throw new Error('"endReadable()" called on non-empty stream');r.endEmitted||(r.ended=!0,e.nextTick(U,r,t))}function U(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function H(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}return x.prototype.read=function(e){p("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return p("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?F(this):D(this),null;if(0===(e=E(e,t))&&t.ended)return 0===t.length&&F(this),null;var n,a=t.needReadable;return p("need readable",a),(0===t.length||t.length-e<t.highWaterMark)&&p("length less than watermark",a=!0),t.ended||t.reading?p("reading or ended",a=!1):a&&(p("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=E(r,t))),null===(n=e>0?z(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&F(this)),null!==n&&this.emit("data",n),n},x.prototype._read=function(e){this.emit("error",new Error("_read() is not implemented"))},x.prototype.pipe=function(t,r){var n=this,i=this._readableState;switch(i.pipesCount){case 0:i.pipes=t;break;case 1:i.pipes=[i.pipes,t];break;default:i.pipes.push(t)}i.pipesCount+=1,p("pipe count=%d opts=%j",i.pipesCount,r);var s=(!r||!1!==r.end)&&t!==process.stdout&&t!==process.stderr?u:y;function l(e,r){p("onunpipe"),e===n&&r&&!1===r.hasUnpiped&&(r.hasUnpiped=!0,p("cleanup"),t.removeListener("close",g),t.removeListener("finish",b),t.removeListener("drain",c),t.removeListener("error",m),t.removeListener("unpipe",l),n.removeListener("end",u),n.removeListener("end",y),n.removeListener("data",f),h=!0,!i.awaitDrain||t._writableState&&!t._writableState.needDrain||c())}function u(){p("onend"),t.end()}i.endEmitted?e.nextTick(s):n.once("end",s),t.on("unpipe",l);var c=function(e){return function(){var t=e._readableState;p("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&o(e,"data")&&(t.flowing=!0,I(e))}}(n);t.on("drain",c);var h=!1;var d=!1;function f(e){p("ondata"),d=!1,!1!==t.write(e)||d||((1===i.pipesCount&&i.pipes===t||i.pipesCount>1&&-1!==H(i.pipes,t))&&!h&&(p("false write response, pause",i.awaitDrain),i.awaitDrain++,d=!0),n.pause())}function m(e){p("onerror",e),y(),t.removeListener("error",m),0===o(t,"error")&&t.emit("error",e)}function g(){t.removeListener("finish",b),y()}function b(){p("onfinish"),t.removeListener("close",g),y()}function y(){p("unpipe"),n.unpipe(t)}return n.on("data",f),function(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?a(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(t,"error",m),t.once("close",g),t.once("finish",b),t.emit("pipe",n),i.flowing||(p("pipe resume"),n.resume()),t},x.prototype.unpipe=function(e){var t=this._readableState,r={hasUnpiped:!1};if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes||(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this,r)),this;if(!e){var n=t.pipes,a=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var i=0;i<a;i++)n[i].emit("unpipe",this,{hasUnpiped:!1});return this}var s=H(t.pipes,e);return-1===s||(t.pipes.splice(s,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this,r)),this},x.prototype.on=function(t,r){var n=l.prototype.on.call(this,t,r);if("data"===t)!1!==this._readableState.flowing&&this.resume();else if("readable"===t){var a=this._readableState;a.endEmitted||a.readableListening||(a.readableListening=a.needReadable=!0,a.emittedReadable=!1,a.reading?a.length&&D(this):e.nextTick(R,this))}return n},x.prototype.addListener=x.prototype.on,x.prototype.resume=function(){var t=this._readableState;return t.flowing||(p("resume"),t.flowing=!0,function(t,r){r.resumeScheduled||(r.resumeScheduled=!0,e.nextTick(O,t,r))}(this,t)),this},x.prototype.pause=function(){return p("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(p("pause"),this._readableState.flowing=!1,this.emit("pause")),this},x.prototype.wrap=function(e){var t=this,r=this._readableState,n=!1;for(var a in e.on("end",(function(){if(p("wrapped end"),r.decoder&&!r.ended){var e=r.decoder.end();e&&e.length&&t.push(e)}t.push(null)})),e.on("data",(function(a){(p("wrapped data"),r.decoder&&(a=r.decoder.write(a)),r.objectMode&&null==a)||(r.objectMode||a&&a.length)&&(t.push(a)||(n=!0,e.pause()))})),e)void 0===this[a]&&"function"==typeof e[a]&&(this[a]=function(t){return function(){return e[t].apply(e,arguments)}}(a));for(var i=0;i<_.length;i++)e.on(_[i],this.emit.bind(this,_[i]));return this._read=function(t){p("wrapped _read",t),n&&(n=!1,e.resume())},this},Object.defineProperty(x.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),x._fromList=z,$}function te(){if(q)return V;q=1,V=n;var e=W(),t=Object.create(S());function r(e,t){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(!n)return this.emit("error",new Error("write callback called multiple times"));r.writechunk=null,r.writecb=null,null!=t&&this.push(t),n(e);var a=this._readableState;a.reading=!1,(a.needReadable||a.length<a.highWaterMark)&&this._read(a.highWaterMark)}function n(t){if(!(this instanceof n))return new n(t);e.call(this,t),this._transformState={afterTransform:r.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"==typeof t.transform&&(this._transform=t.transform),"function"==typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",a)}function a(){var e=this;"function"==typeof this._flush?this._flush((function(t,r){i(e,t,r)})):i(this,null,null)}function i(e,t,r){if(t)return e.emit("error",t);if(null!=r&&e.push(r),e._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(e._transformState.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}return t.inherits=T(),t.inherits(n,e),n.prototype.push=function(t,r){return this._transformState.needTransform=!1,e.prototype.push.call(this,t,r)},n.prototype._transform=function(e,t,r){throw new Error("_transform() is not implemented")},n.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var a=this._readableState;(n.needTransform||a.needReadable||a.length<a.highWaterMark)&&this._read(a.highWaterMark)}},n.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},n.prototype._destroy=function(t,r){var n=this;e.prototype._destroy.call(this,t,(function(e){r(e),n.emit("close")}))},V}function re(){return G||(G=1,function(t,r){var n=e;"disable"===process.env.READABLE_STREAM&&n?(t.exports=n,(r=t.exports=n.Readable).Readable=n.Readable,r.Writable=n.Writable,r.Duplex=n.Duplex,r.Transform=n.Transform,r.PassThrough=n.PassThrough,r.Stream=n):((r=t.exports=ee()).Stream=n||r,r.Readable=r,r.Writable=U(),r.Duplex=W(),r.Transform=te(),r.PassThrough=function(){if(K)return X;K=1,X=r;var e=te(),t=Object.create(S());function r(t){if(!(this instanceof r))return new r(t);e.call(this,t)}return t.inherits=T(),t.inherits(r,e),r.prototype._transform=function(e,t,r){r(null,e)},X}())}(p,p.exports)),p.exports}function ne(){if(J)return f;if(J=1,f.base64=!0,f.array=!0,f.string=!0,f.arraybuffer="undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array,f.nodebuffer="undefined"!=typeof Buffer,f.uint8array="undefined"!=typeof Uint8Array,"undefined"==typeof ArrayBuffer)f.blob=!1;else{var e=new ArrayBuffer(0);try{f.blob=0===new Blob([e],{type:"application/zip"}).size}catch(r){try{var t=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);t.append(e),f.blob=0===t.getBlob("application/zip").size}catch(e){f.blob=!1}}}try{f.nodestream=!!re().Readable}catch(e){f.nodestream=!1}return f}var ae,ie,se,oe,le,ue,ce,he,de,fe={};function pe(){if(ae)return fe;ae=1;var e=Ne(),t=ne(),r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";return fe.encode=function(t){for(var n,a,i,s,o,l,u,c=[],h=0,d=t.length,f=d,p="string"!==e.getTypeOf(t);h<t.length;)f=d-h,p?(n=t[h++],a=h<d?t[h++]:0,i=h<d?t[h++]:0):(n=t.charCodeAt(h++),a=h<d?t.charCodeAt(h++):0,i=h<d?t.charCodeAt(h++):0),s=n>>2,o=(3&n)<<4|a>>4,l=f>1?(15&a)<<2|i>>6:64,u=f>2?63&i:64,c.push(r.charAt(s)+r.charAt(o)+r.charAt(l)+r.charAt(u));return c.join("")},fe.decode=function(e){var n,a,i,s,o,l,u=0,c=0,h="data:";if(e.substr(0,5)===h)throw new Error("Invalid base64 input, it looks like a data url.");var d,f=3*(e=e.replace(/[^A-Za-z0-9+/=]/g,"")).length/4;if(e.charAt(e.length-1)===r.charAt(64)&&f--,e.charAt(e.length-2)===r.charAt(64)&&f--,f%1!=0)throw new Error("Invalid base64 input, bad content length.");for(d=t.uint8array?new Uint8Array(0|f):new Array(0|f);u<e.length;)n=r.indexOf(e.charAt(u++))<<2|(s=r.indexOf(e.charAt(u++)))>>4,a=(15&s)<<4|(o=r.indexOf(e.charAt(u++)))>>2,i=(3&o)<<6|(l=r.indexOf(e.charAt(u++))),d[c++]=n,64!==o&&(d[c++]=a),64!==l&&(d[c++]=i);return d},fe}function me(){return se?ie:(se=1,ie={isNode:"undefined"!=typeof Buffer,newBufferFrom:function(e,t){if(Buffer.from&&Buffer.from!==Uint8Array.from)return Buffer.from(e,t);if("number"==typeof e)throw new Error('The "data" argument must not be a number');return new Buffer(e,t)},allocBuffer:function(e){if(Buffer.alloc)return Buffer.alloc(e);var t=new Buffer(e);return t.fill(0),t},isBuffer:function(e){return Buffer.isBuffer(e)},isStream:function(e){return e&&"function"==typeof e.on&&"function"==typeof e.pause&&"function"==typeof e.resume}})}function ge(){if(ce)return ue;ce=1;var e=function(){if(le)return oe;le=1;var e,t,r=u.MutationObserver||u.WebKitMutationObserver;if(process.browser)if(r){var n=0,a=new r(l),i=u.document.createTextNode("");a.observe(i,{characterData:!0}),e=function(){i.data=n=++n%2}}else if(u.setImmediate||void 0===u.MessageChannel)e="document"in u&&"onreadystatechange"in u.document.createElement("script")?function(){var e=u.document.createElement("script");e.onreadystatechange=function(){l(),e.onreadystatechange=null,e.parentNode.removeChild(e),e=null},u.document.documentElement.appendChild(e)}:function(){setTimeout(l,0)};else{var s=new u.MessageChannel;s.port1.onmessage=l,e=function(){s.port2.postMessage(0)}}else e=function(){process.nextTick(l)};var o=[];function l(){var e,r;t=!0;for(var n=o.length;n;){for(r=o,o=[],e=-1;++e<n;)r[e]();n=o.length}t=!1}return oe=function(r){1!==o.push(r)||t||e()}}();function t(){}var r={},n=["REJECTED"],a=["FULFILLED"],i=["PENDING"];if(!process.browser)var s=["UNHANDLED"];function o(e){if("function"!=typeof e)throw new TypeError("resolver must be a function");this.state=i,this.queue=[],this.outcome=void 0,process.browser||(this.handled=s),e!==t&&d(this,e)}function l(e,t,r){this.promise=e,"function"==typeof t&&(this.onFulfilled=t,this.callFulfilled=this.otherCallFulfilled),"function"==typeof r&&(this.onRejected=r,this.callRejected=this.otherCallRejected)}function c(t,n,a){e((function(){var e;try{e=n(a)}catch(e){return r.reject(t,e)}e===t?r.reject(t,new TypeError("Cannot resolve promise with itself")):r.resolve(t,e)}))}function h(e){var t=e&&e.then;if(e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof t)return function(){t.apply(e,arguments)}}function d(e,t){var n=!1;function a(t){n||(n=!0,r.reject(e,t))}function i(t){n||(n=!0,r.resolve(e,t))}var s=f((function(){t(i,a)}));"error"===s.status&&a(s.value)}function f(e,t){var r={};try{r.value=e(t),r.status="success"}catch(e){r.status="error",r.value=e}return r}return ue=o,o.prototype.finally=function(e){if("function"!=typeof e)return this;var t=this.constructor;return this.then((function(r){return t.resolve(e()).then((function(){return r}))}),(function(r){return t.resolve(e()).then((function(){throw r}))}))},o.prototype.catch=function(e){return this.then(null,e)},o.prototype.then=function(e,r){if("function"!=typeof e&&this.state===a||"function"!=typeof r&&this.state===n)return this;var o=new this.constructor(t);(process.browser||this.handled===s&&(this.handled=null),this.state!==i)?c(o,this.state===a?e:r,this.outcome):this.queue.push(new l(o,e,r));return o},l.prototype.callFulfilled=function(e){r.resolve(this.promise,e)},l.prototype.otherCallFulfilled=function(e){c(this.promise,this.onFulfilled,e)},l.prototype.callRejected=function(e){r.reject(this.promise,e)},l.prototype.otherCallRejected=function(e){c(this.promise,this.onRejected,e)},r.resolve=function(e,t){var n=f(h,t);if("error"===n.status)return r.reject(e,n.value);var i=n.value;if(i)d(e,i);else{e.state=a,e.outcome=t;for(var s=-1,o=e.queue.length;++s<o;)e.queue[s].callFulfilled(t)}return e},r.reject=function(t,r){t.state=n,t.outcome=r,process.browser||t.handled===s&&e((function(){t.handled===s&&process.emit("unhandledRejection",r,t)}));for(var a=-1,i=t.queue.length;++a<i;)t.queue[a].callRejected(r);return t},o.resolve=function(e){if(e instanceof this)return e;return r.resolve(new this(t),e)},o.reject=function(e){var n=new this(t);return r.reject(n,e)},o.all=function(e){var n=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var a=e.length,i=!1;if(!a)return this.resolve([]);var s=new Array(a),o=0,l=-1,u=new this(t);for(;++l<a;)c(e[l],l);return u;function c(e,t){n.resolve(e).then((function(e){s[t]=e,++o!==a||i||(i=!0,r.resolve(u,s))}),(function(e){i||(i=!0,r.reject(u,e))}))}},o.race=function(e){var n=this;if("[object Array]"!==Object.prototype.toString.call(e))return this.reject(new TypeError("must be an array"));var a=e.length,i=!1;if(!a)return this.resolve([]);var s=-1,o=new this(t);for(;++s<a;)l=e[s],n.resolve(l).then((function(e){i||(i=!0,r.resolve(o,e))}),(function(e){i||(i=!0,r.reject(o,e))}));var l;return o},ue}function be(){if(de)return he;de=1;var e=null;return e="undefined"!=typeof Promise?Promise:ge(),he={Promise:e}}var ye,ve,we,_e,ke,Se,xe,Ce,Ae,Pe,Te,Ee={};function De(){return ye||(ye=1,function(e,t){if(!e.setImmediate){var r,n,a,i,s,o=1,l={},u=!1,c=e.document,h=Object.getPrototypeOf&&Object.getPrototypeOf(e);h=h&&h.setTimeout?h:e,"[object process]"==={}.toString.call(e.process)?r=function(e){process.nextTick((function(){f(e)}))}:!function(){if(e.postMessage&&!e.importScripts){var t=!0,r=e.onmessage;return e.onmessage=function(){t=!1},e.postMessage("","*"),e.onmessage=r,t}}()?e.MessageChannel?((a=new MessageChannel).port1.onmessage=function(e){f(e.data)},r=function(e){a.port2.postMessage(e)}):c&&"onreadystatechange"in c.createElement("script")?(n=c.documentElement,r=function(e){var t=c.createElement("script");t.onreadystatechange=function(){f(e),t.onreadystatechange=null,n.removeChild(t),t=null},n.appendChild(t)}):r=function(e){setTimeout(f,0,e)}:(i="setImmediate$"+Math.random()+"$",s=function(t){t.source===e&&"string"==typeof t.data&&0===t.data.indexOf(i)&&f(+t.data.slice(i.length))},e.addEventListener?e.addEventListener("message",s,!1):e.attachEvent("onmessage",s),r=function(t){e.postMessage(i+t,"*")}),h.setImmediate=function(e){"function"!=typeof e&&(e=new Function(""+e));for(var t=new Array(arguments.length-1),n=0;n<t.length;n++)t[n]=arguments[n+1];var a={callback:e,args:t};return l[o]=a,r(o),o++},h.clearImmediate=d}function d(e){delete l[e]}function f(e){if(u)setTimeout(f,0,e);else{var r=l[e];if(r){u=!0;try{!function(e){var r=e.callback,n=e.args;switch(n.length){case 0:r();break;case 1:r(n[0]);break;case 2:r(n[0],n[1]);break;case 3:r(n[0],n[1],n[2]);break;default:r.apply(t,n)}}(r)}finally{d(e),u=!1}}}}}("undefined"==typeof self?u:self)),Ee}function Ne(){return ve||(ve=1,function(e){var t=ne(),r=pe(),n=me(),a=be();function i(e){return e}function s(e,t){for(var r=0;r<e.length;++r)t[r]=255&e.charCodeAt(r);return t}De(),e.newBlob=function(t,r){e.checkSupport("blob");try{return new Blob([t],{type:r})}catch(e){try{var n=new(self.BlobBuilder||self.WebKitBlobBuilder||self.MozBlobBuilder||self.MSBlobBuilder);return n.append(t),n.getBlob(r)}catch(e){throw new Error("Bug : can't construct the Blob.")}}};var o={stringifyByChunk:function(e,t,r){var n=[],a=0,i=e.length;if(i<=r)return String.fromCharCode.apply(null,e);for(;a<i;)"array"===t||"nodebuffer"===t?n.push(String.fromCharCode.apply(null,e.slice(a,Math.min(a+r,i)))):n.push(String.fromCharCode.apply(null,e.subarray(a,Math.min(a+r,i)))),a+=r;return n.join("")},stringifyByChar:function(e){for(var t="",r=0;r<e.length;r++)t+=String.fromCharCode(e[r]);return t},applyCanBeUsed:{uint8array:function(){try{return t.uint8array&&1===String.fromCharCode.apply(null,new Uint8Array(1)).length}catch(e){return!1}}(),nodebuffer:function(){try{return t.nodebuffer&&1===String.fromCharCode.apply(null,n.allocBuffer(1)).length}catch(e){return!1}}()}};function l(t){var r=65536,n=e.getTypeOf(t),a=!0;if("uint8array"===n?a=o.applyCanBeUsed.uint8array:"nodebuffer"===n&&(a=o.applyCanBeUsed.nodebuffer),a)for(;r>1;)try{return o.stringifyByChunk(t,n,r)}catch(e){r=Math.floor(r/2)}return o.stringifyByChar(t)}function u(e,t){for(var r=0;r<e.length;r++)t[r]=e[r];return t}e.applyFromCharCode=l;var c={};c.string={string:i,array:function(e){return s(e,new Array(e.length))},arraybuffer:function(e){return c.string.uint8array(e).buffer},uint8array:function(e){return s(e,new Uint8Array(e.length))},nodebuffer:function(e){return s(e,n.allocBuffer(e.length))}},c.array={string:l,array:i,arraybuffer:function(e){return new Uint8Array(e).buffer},uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return n.newBufferFrom(e)}},c.arraybuffer={string:function(e){return l(new Uint8Array(e))},array:function(e){return u(new Uint8Array(e),new Array(e.byteLength))},arraybuffer:i,uint8array:function(e){return new Uint8Array(e)},nodebuffer:function(e){return n.newBufferFrom(new Uint8Array(e))}},c.uint8array={string:l,array:function(e){return u(e,new Array(e.length))},arraybuffer:function(e){return e.buffer},uint8array:i,nodebuffer:function(e){return n.newBufferFrom(e)}},c.nodebuffer={string:l,array:function(e){return u(e,new Array(e.length))},arraybuffer:function(e){return c.nodebuffer.uint8array(e).buffer},uint8array:function(e){return u(e,new Uint8Array(e.length))},nodebuffer:i},e.transformTo=function(t,r){if(r||(r=""),!t)return r;e.checkSupport(t);var n=e.getTypeOf(r);return c[n][t](r)},e.resolve=function(e){for(var t=e.split("/"),r=[],n=0;n<t.length;n++){var a=t[n];"."===a||""===a&&0!==n&&n!==t.length-1||(".."===a?r.pop():r.push(a))}return r.join("/")},e.getTypeOf=function(e){return"string"==typeof e?"string":"[object Array]"===Object.prototype.toString.call(e)?"array":t.nodebuffer&&n.isBuffer(e)?"nodebuffer":t.uint8array&&e instanceof Uint8Array?"uint8array":t.arraybuffer&&e instanceof ArrayBuffer?"arraybuffer":void 0},e.checkSupport=function(e){if(!t[e.toLowerCase()])throw new Error(e+" is not supported by this platform")},e.MAX_VALUE_16BITS=65535,e.MAX_VALUE_32BITS=-1,e.pretty=function(e){var t,r,n="";for(r=0;r<(e||"").length;r++)n+="\\x"+((t=e.charCodeAt(r))<16?"0":"")+t.toString(16).toUpperCase();return n},e.delay=function(e,t,r){setImmediate((function(){e.apply(r||null,t||[])}))},e.inherits=function(e,t){var r=function(){};r.prototype=t.prototype,e.prototype=new r},e.extend=function(){var e,t,r={};for(e=0;e<arguments.length;e++)for(t in arguments[e])Object.prototype.hasOwnProperty.call(arguments[e],t)&&void 0===r[t]&&(r[t]=arguments[e][t]);return r},e.prepareContent=function(n,i,o,l,u){return a.Promise.resolve(i).then((function(e){return t.blob&&(e instanceof Blob||-1!==["[object File]","[object Blob]"].indexOf(Object.prototype.toString.call(e)))&&"undefined"!=typeof FileReader?new a.Promise((function(t,r){var n=new FileReader;n.onload=function(e){t(e.target.result)},n.onerror=function(e){r(e.target.error)},n.readAsArrayBuffer(e)})):e})).then((function(i){var c,h=e.getTypeOf(i);return h?("arraybuffer"===h?i=e.transformTo("uint8array",i):"string"===h&&(u?i=r.decode(i):o&&!0!==l&&(i=s(c=i,t.uint8array?new Uint8Array(c.length):new Array(c.length)))),i):a.Promise.reject(new Error("Can't read the data of '"+n+"'. Is it in a supported JavaScript type (String, Blob, ArrayBuffer, etc) ?"))}))}}(d)),d}function Be(){if(_e)return we;function e(e){this.name=e||"default",this.streamInfo={},this.generatedError=null,this.extraStreamInfo={},this.isPaused=!0,this.isFinished=!1,this.isLocked=!1,this._listeners={data:[],end:[],error:[]},this.previous=null}return _e=1,e.prototype={push:function(e){this.emit("data",e)},end:function(){if(this.isFinished)return!1;this.flush();try{this.emit("end"),this.cleanUp(),this.isFinished=!0}catch(e){this.emit("error",e)}return!0},error:function(e){return!this.isFinished&&(this.isPaused?this.generatedError=e:(this.isFinished=!0,this.emit("error",e),this.previous&&this.previous.error(e),this.cleanUp()),!0)},on:function(e,t){return this._listeners[e].push(t),this},cleanUp:function(){this.streamInfo=this.generatedError=this.extraStreamInfo=null,this._listeners=[]},emit:function(e,t){if(this._listeners[e])for(var r=0;r<this._listeners[e].length;r++)this._listeners[e][r].call(this,t)},pipe:function(e){return e.registerPrevious(this)},registerPrevious:function(e){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.streamInfo=e.streamInfo,this.mergeStreamInfo(),this.previous=e;var t=this;return e.on("data",(function(e){t.processChunk(e)})),e.on("end",(function(){t.end()})),e.on("error",(function(e){t.error(e)})),this},pause:function(){return!this.isPaused&&!this.isFinished&&(this.isPaused=!0,this.previous&&this.previous.pause(),!0)},resume:function(){if(!this.isPaused||this.isFinished)return!1;this.isPaused=!1;var e=!1;return this.generatedError&&(this.error(this.generatedError),e=!0),this.previous&&this.previous.resume(),!e},flush:function(){},processChunk:function(e){this.push(e)},withStreamInfo:function(e,t){return this.extraStreamInfo[e]=t,this.mergeStreamInfo(),this},mergeStreamInfo:function(){for(var e in this.extraStreamInfo)Object.prototype.hasOwnProperty.call(this.extraStreamInfo,e)&&(this.streamInfo[e]=this.extraStreamInfo[e])},lock:function(){if(this.isLocked)throw new Error("The stream '"+this+"' has already been used.");this.isLocked=!0,this.previous&&this.previous.lock()},toString:function(){var e="Worker "+this.name;return this.previous?this.previous+" -> "+e:e}},we=e}function Me(){return ke||(ke=1,function(e){for(var t=Ne(),r=ne(),n=me(),a=Be(),i=new Array(256),s=0;s<256;s++)i[s]=s>=252?6:s>=248?5:s>=240?4:s>=224?3:s>=192?2:1;i[254]=i[254]=1;function o(){a.call(this,"utf-8 decode"),this.leftOver=null}function l(){a.call(this,"utf-8 encode")}e.utf8encode=function(e){return r.nodebuffer?n.newBufferFrom(e,"utf-8"):function(e){var t,n,a,i,s,o=e.length,l=0;for(i=0;i<o;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(a=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(a-56320),i++),l+=n<128?1:n<2048?2:n<65536?3:4;for(t=r.uint8array?new Uint8Array(l):new Array(l),s=0,i=0;s<l;i++)55296==(64512&(n=e.charCodeAt(i)))&&i+1<o&&56320==(64512&(a=e.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(a-56320),i++),n<128?t[s++]=n:n<2048?(t[s++]=192|n>>>6,t[s++]=128|63&n):n<65536?(t[s++]=224|n>>>12,t[s++]=128|n>>>6&63,t[s++]=128|63&n):(t[s++]=240|n>>>18,t[s++]=128|n>>>12&63,t[s++]=128|n>>>6&63,t[s++]=128|63&n);return t}(e)},e.utf8decode=function(e){return r.nodebuffer?t.transformTo("nodebuffer",e).toString("utf-8"):function(e){var r,n,a,s,o=e.length,l=new Array(2*o);for(n=0,r=0;r<o;)if((a=e[r++])<128)l[n++]=a;else if((s=i[a])>4)l[n++]=65533,r+=s-1;else{for(a&=2===s?31:3===s?15:7;s>1&&r<o;)a=a<<6|63&e[r++],s--;s>1?l[n++]=65533:a<65536?l[n++]=a:(a-=65536,l[n++]=55296|a>>10&1023,l[n++]=56320|1023&a)}return l.length!==n&&(l.subarray?l=l.subarray(0,n):l.length=n),t.applyFromCharCode(l)}(e=t.transformTo(r.uint8array?"uint8array":"array",e))},t.inherits(o,a),o.prototype.processChunk=function(n){var a=t.transformTo(r.uint8array?"uint8array":"array",n.data);if(this.leftOver&&this.leftOver.length){if(r.uint8array){var s=a;(a=new Uint8Array(s.length+this.leftOver.length)).set(this.leftOver,0),a.set(s,this.leftOver.length)}else a=this.leftOver.concat(a);this.leftOver=null}var o=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;r>=0&&128==(192&e[r]);)r--;return r<0||0===r?t:r+i[e[r]]>t?r:t}(a),l=a;o!==a.length&&(r.uint8array?(l=a.subarray(0,o),this.leftOver=a.subarray(o,a.length)):(l=a.slice(0,o),this.leftOver=a.slice(o,a.length))),this.push({data:e.utf8decode(l),meta:n.meta})},o.prototype.flush=function(){this.leftOver&&this.leftOver.length&&(this.push({data:e.utf8decode(this.leftOver),meta:{}}),this.leftOver=null)},e.Utf8DecodeWorker=o,t.inherits(l,a),l.prototype.processChunk=function(t){this.push({data:e.utf8encode(t.data),meta:t.meta})},e.Utf8EncodeWorker=l}(h)),h}function Re(){if(Te)return Pe;Te=1;var e=Ne(),t=function(){if(xe)return Se;xe=1;var e=Be(),t=Ne();function r(t){e.call(this,"ConvertWorker to "+t),this.destType=t}return t.inherits(r,e),r.prototype.processChunk=function(e){this.push({data:t.transformTo(this.destType,e.data),meta:e.meta})},Se=r}(),r=Be(),n=pe(),a=ne(),i=be(),s=null;if(a.nodestream)try{s=function(){if(Ae)return Ce;Ae=1;var e=re().Readable;function t(t,r,n){e.call(this,r),this._helper=t;var a=this;t.on("data",(function(e,t){a.push(e)||a._helper.pause(),n&&n(t)})).on("error",(function(e){a.emit("error",e)})).on("end",(function(){a.push(null)}))}return Ne().inherits(t,e),t.prototype._read=function(){this._helper.resume()},Ce=t}()}catch(e){}function o(t,r){return new i.Promise((function(a,i){var s=[],o=t._internalType,l=t._outputType,u=t._mimeType;t.on("data",(function(e,t){s.push(e),r&&r(t)})).on("error",(function(e){s=[],i(e)})).on("end",(function(){try{var t=function(t,r,a){switch(t){case"blob":return e.newBlob(e.transformTo("arraybuffer",r),a);case"base64":return n.encode(r);default:return e.transformTo(t,r)}}(l,function(e,t){var r,n=0,a=null,i=0;for(r=0;r<t.length;r++)i+=t[r].length;switch(e){case"string":return t.join("");case"array":return Array.prototype.concat.apply([],t);case"uint8array":for(a=new Uint8Array(i),r=0;r<t.length;r++)a.set(t[r],n),n+=t[r].length;return a;case"nodebuffer":return Buffer.concat(t);default:throw new Error("concat : unsupported type '"+e+"'")}}(o,s),u);a(t)}catch(e){i(e)}s=[]})).resume()}))}function l(n,a,i){var s=a;switch(a){case"blob":case"arraybuffer":s="uint8array";break;case"base64":s="string"}try{this._internalType=s,this._outputType=a,this._mimeType=i,e.checkSupport(s),this._worker=n.pipe(new t(s)),n.lock()}catch(e){this._worker=new r("error"),this._worker.error(e)}}return l.prototype={accumulate:function(e){return o(this,e)},on:function(t,r){var n=this;return"data"===t?this._worker.on(t,(function(e){r.call(n,e.data,e.meta)})):this._worker.on(t,(function(){e.delay(r,arguments,n)})),this},resume:function(){return e.delay(this._worker.resume,[],this._worker),this},pause:function(){return this._worker.pause(),this},toNodejsStream:function(t){if(e.checkSupport("nodestream"),"nodebuffer"!==this._outputType)throw new Error(this._outputType+" is not supported by this method");return new s(this,{objectMode:"nodebuffer"!==this._outputType},t)}},Pe=l}var Oe,Ie,ze,Fe,Le,je,Ue,We,He,$e,Ze,Ve,qe,Xe={};function Ke(){return Oe||(Oe=1,Xe.base64=!1,Xe.binary=!1,Xe.dir=!1,Xe.createFolders=!0,Xe.date=null,Xe.compression=null,Xe.compressionOptions=null,Xe.comment=null,Xe.unixPermissions=null,Xe.dosPermissions=null),Xe}function Ge(){if(ze)return Ie;ze=1;var e=Ne(),t=Be();function r(r){t.call(this,"DataWorker");var n=this;this.dataIsReady=!1,this.index=0,this.max=0,this.data=null,this.type="",this._tickScheduled=!1,r.then((function(t){n.dataIsReady=!0,n.data=t,n.max=t&&t.length||0,n.type=e.getTypeOf(t),n.isPaused||n._tickAndRepeat()}),(function(e){n.error(e)}))}return e.inherits(r,t),r.prototype.cleanUp=function(){t.prototype.cleanUp.call(this),this.data=null},r.prototype.resume=function(){return!!t.prototype.resume.call(this)&&(!this._tickScheduled&&this.dataIsReady&&(this._tickScheduled=!0,e.delay(this._tickAndRepeat,[],this)),!0)},r.prototype._tickAndRepeat=function(){this._tickScheduled=!1,this.isPaused||this.isFinished||(this._tick(),this.isFinished||(e.delay(this._tickAndRepeat,[],this),this._tickScheduled=!0))},r.prototype._tick=function(){if(this.isPaused||this.isFinished)return!1;var e=null,t=Math.min(this.max,this.index+16384);if(this.index>=this.max)return this.end();switch(this.type){case"string":e=this.data.substring(this.index,t);break;case"uint8array":e=this.data.subarray(this.index,t);break;case"array":case"nodebuffer":e=this.data.slice(this.index,t)}return this.index=t,this.push({data:e,meta:{percent:this.max?this.index/this.max*100:0}})},Ie=r}function Je(){if(Le)return Fe;Le=1;var e=Ne();var t=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();return Fe=function(r,n){return void 0!==r&&r.length?"string"!==e.getTypeOf(r)?function(e,r,n,a){var i=t,s=a+n;e^=-1;for(var o=a;o<s;o++)e=e>>>8^i[255&(e^r[o])];return-1^e}(0|n,r,r.length,0):function(e,r,n,a){var i=t,s=a+n;e^=-1;for(var o=a;o<s;o++)e=e>>>8^i[255&(e^r.charCodeAt(o))];return-1^e}(0|n,r,r.length,0):0}}function Ye(){if(Ue)return je;Ue=1;var e=Be(),t=Je();function r(){e.call(this,"Crc32Probe"),this.withStreamInfo("crc32",0)}return Ne().inherits(r,e),r.prototype.processChunk=function(e){this.streamInfo.crc32=t(e.data,this.streamInfo.crc32||0),this.push(e)},je=r}function Qe(){if(Ze)return $e;Ze=1;var e=be(),t=Ge(),r=Ye(),n=function(){if(He)return We;He=1;var e=Ne(),t=Be();function r(e){t.call(this,"DataLengthProbe for "+e),this.propName=e,this.withStreamInfo(e,0)}return e.inherits(r,t),r.prototype.processChunk=function(e){if(e){var r=this.streamInfo[this.propName]||0;this.streamInfo[this.propName]=r+e.data.length}t.prototype.processChunk.call(this,e)},We=r}();function a(e,t,r,n,a){this.compressedSize=e,this.uncompressedSize=t,this.crc32=r,this.compression=n,this.compressedContent=a}return a.prototype={getContentWorker:function(){var r=new t(e.Promise.resolve(this.compressedContent)).pipe(this.compression.uncompressWorker()).pipe(new n("data_length")),a=this;return r.on("end",(function(){if(this.streamInfo.data_length!==a.uncompressedSize)throw new Error("Bug : uncompressed data size mismatch")})),r},getCompressedWorker:function(){return new t(e.Promise.resolve(this.compressedContent)).withStreamInfo("compressedSize",this.compressedSize).withStreamInfo("uncompressedSize",this.uncompressedSize).withStreamInfo("crc32",this.crc32).withStreamInfo("compression",this.compression)}},a.createWorkerFrom=function(e,t,a){return e.pipe(new r).pipe(new n("uncompressedSize")).pipe(t.compressWorker(a)).pipe(new n("compressedSize")).withStreamInfo("compression",t)},$e=a}var et,tt={},rt={},nt={},at={};function it(){return et||(et=1,function(e){var t="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Int32Array;function r(e,t){return Object.prototype.hasOwnProperty.call(e,t)}e.assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var n=t.shift();if(n){if("object"!=typeof n)throw new TypeError(n+"must be non-object");for(var a in n)r(n,a)&&(e[a]=n[a])}}return e},e.shrinkBuf=function(e,t){return e.length===t?e:e.subarray?e.subarray(0,t):(e.length=t,e)};var n={arraySet:function(e,t,r,n,a){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+n),a);else for(var i=0;i<n;i++)e[a+i]=t[r+i]},flattenChunks:function(e){var t,r,n,a,i,s;for(n=0,t=0,r=e.length;t<r;t++)n+=e[t].length;for(s=new Uint8Array(n),a=0,t=0,r=e.length;t<r;t++)i=e[t],s.set(i,a),a+=i.length;return s}},a={arraySet:function(e,t,r,n,a){for(var i=0;i<n;i++)e[a+i]=t[r+i]},flattenChunks:function(e){return[].concat.apply([],e)}};e.setTyped=function(t){t?(e.Buf8=Uint8Array,e.Buf16=Uint16Array,e.Buf32=Int32Array,e.assign(e,n)):(e.Buf8=Array,e.Buf16=Array,e.Buf32=Array,e.assign(e,a))},e.setTyped(t)}(at)),at}var st,ot,lt,ut,ct,ht,dt,ft,pt={},mt={},gt={};function bt(){if(st)return gt;st=1;var e=it(),t=0,r=1;function n(e){for(var t=e.length;--t>=0;)e[t]=0}var a=0,i=29,s=256,o=s+1+i,l=30,u=19,c=2*o+1,h=15,d=16,f=7,p=256,m=16,g=17,b=18,y=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],v=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],w=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],_=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],k=new Array(2*(o+2));n(k);var S=new Array(2*l);n(S);var x=new Array(512);n(x);var C=new Array(256);n(C);var A=new Array(i);n(A);var P,T,E,D=new Array(l);function N(e,t,r,n,a){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=a,this.has_stree=e&&e.length}function B(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function M(e){return e<256?x[e]:x[256+(e>>>7)]}function R(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function O(e,t,r){e.bi_valid>d-r?(e.bi_buf|=t<<e.bi_valid&65535,R(e,e.bi_buf),e.bi_buf=t>>d-e.bi_valid,e.bi_valid+=r-d):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function I(e,t,r){O(e,r[2*t],r[2*t+1])}function z(e,t){var r=0;do{r|=1&e,e>>>=1,r<<=1}while(--t>0);return r>>>1}function F(e,t,r){var n,a,i=new Array(h+1),s=0;for(n=1;n<=h;n++)i[n]=s=s+r[n-1]<<1;for(a=0;a<=t;a++){var o=e[2*a+1];0!==o&&(e[2*a]=z(i[o]++,o))}}function L(e){var t;for(t=0;t<o;t++)e.dyn_ltree[2*t]=0;for(t=0;t<l;t++)e.dyn_dtree[2*t]=0;for(t=0;t<u;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*p]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function j(e){e.bi_valid>8?R(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function U(e,t,r,n){var a=2*t,i=2*r;return e[a]<e[i]||e[a]===e[i]&&n[t]<=n[r]}function W(e,t,r){for(var n=e.heap[r],a=r<<1;a<=e.heap_len&&(a<e.heap_len&&U(t,e.heap[a+1],e.heap[a],e.depth)&&a++,!U(t,n,e.heap[a],e.depth));)e.heap[r]=e.heap[a],r=a,a<<=1;e.heap[r]=n}function H(e,t,r){var n,a,i,o,l=0;if(0!==e.last_lit)do{n=e.pending_buf[e.d_buf+2*l]<<8|e.pending_buf[e.d_buf+2*l+1],a=e.pending_buf[e.l_buf+l],l++,0===n?I(e,a,t):(I(e,(i=C[a])+s+1,t),0!==(o=y[i])&&O(e,a-=A[i],o),I(e,i=M(--n),r),0!==(o=v[i])&&O(e,n-=D[i],o))}while(l<e.last_lit);I(e,p,t)}function $(e,t){var r,n,a,i=t.dyn_tree,s=t.stat_desc.static_tree,o=t.stat_desc.has_stree,l=t.stat_desc.elems,u=-1;for(e.heap_len=0,e.heap_max=c,r=0;r<l;r++)0!==i[2*r]?(e.heap[++e.heap_len]=u=r,e.depth[r]=0):i[2*r+1]=0;for(;e.heap_len<2;)i[2*(a=e.heap[++e.heap_len]=u<2?++u:0)]=1,e.depth[a]=0,e.opt_len--,o&&(e.static_len-=s[2*a+1]);for(t.max_code=u,r=e.heap_len>>1;r>=1;r--)W(e,i,r);a=l;do{r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],W(e,i,1),n=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=n,i[2*a]=i[2*r]+i[2*n],e.depth[a]=(e.depth[r]>=e.depth[n]?e.depth[r]:e.depth[n])+1,i[2*r+1]=i[2*n+1]=a,e.heap[1]=a++,W(e,i,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var r,n,a,i,s,o,l=t.dyn_tree,u=t.max_code,d=t.stat_desc.static_tree,f=t.stat_desc.has_stree,p=t.stat_desc.extra_bits,m=t.stat_desc.extra_base,g=t.stat_desc.max_length,b=0;for(i=0;i<=h;i++)e.bl_count[i]=0;for(l[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<c;r++)(i=l[2*l[2*(n=e.heap[r])+1]+1]+1)>g&&(i=g,b++),l[2*n+1]=i,n>u||(e.bl_count[i]++,s=0,n>=m&&(s=p[n-m]),o=l[2*n],e.opt_len+=o*(i+s),f&&(e.static_len+=o*(d[2*n+1]+s)));if(0!==b){do{for(i=g-1;0===e.bl_count[i];)i--;e.bl_count[i]--,e.bl_count[i+1]+=2,e.bl_count[g]--,b-=2}while(b>0);for(i=g;0!==i;i--)for(n=e.bl_count[i];0!==n;)(a=e.heap[--r])>u||(l[2*a+1]!==i&&(e.opt_len+=(i-l[2*a+1])*l[2*a],l[2*a+1]=i),n--)}}(e,t),F(i,u,e.bl_count)}function Z(e,t,r){var n,a,i=-1,s=t[1],o=0,l=7,u=4;for(0===s&&(l=138,u=3),t[2*(r+1)+1]=65535,n=0;n<=r;n++)a=s,s=t[2*(n+1)+1],++o<l&&a===s||(o<u?e.bl_tree[2*a]+=o:0!==a?(a!==i&&e.bl_tree[2*a]++,e.bl_tree[2*m]++):o<=10?e.bl_tree[2*g]++:e.bl_tree[2*b]++,o=0,i=a,0===s?(l=138,u=3):a===s?(l=6,u=3):(l=7,u=4))}function V(e,t,r){var n,a,i=-1,s=t[1],o=0,l=7,u=4;for(0===s&&(l=138,u=3),n=0;n<=r;n++)if(a=s,s=t[2*(n+1)+1],!(++o<l&&a===s)){if(o<u)do{I(e,a,e.bl_tree)}while(0!=--o);else 0!==a?(a!==i&&(I(e,a,e.bl_tree),o--),I(e,m,e.bl_tree),O(e,o-3,2)):o<=10?(I(e,g,e.bl_tree),O(e,o-3,3)):(I(e,b,e.bl_tree),O(e,o-11,7));o=0,i=a,0===s?(l=138,u=3):a===s?(l=6,u=3):(l=7,u=4)}}n(D);var q=!1;function X(t,r,n,i){O(t,(a<<1)+(i?1:0),3),function(t,r,n,a){j(t),a&&(R(t,n),R(t,~n)),e.arraySet(t.pending_buf,t.window,r,n,t.pending),t.pending+=n}(t,r,n,!0)}return gt._tr_init=function(e){q||(!function(){var e,t,r,n,a,c=new Array(h+1);for(r=0,n=0;n<i-1;n++)for(A[n]=r,e=0;e<1<<y[n];e++)C[r++]=n;for(C[r-1]=n,a=0,n=0;n<16;n++)for(D[n]=a,e=0;e<1<<v[n];e++)x[a++]=n;for(a>>=7;n<l;n++)for(D[n]=a<<7,e=0;e<1<<v[n]-7;e++)x[256+a++]=n;for(t=0;t<=h;t++)c[t]=0;for(e=0;e<=143;)k[2*e+1]=8,e++,c[8]++;for(;e<=255;)k[2*e+1]=9,e++,c[9]++;for(;e<=279;)k[2*e+1]=7,e++,c[7]++;for(;e<=287;)k[2*e+1]=8,e++,c[8]++;for(F(k,o+1,c),e=0;e<l;e++)S[2*e+1]=5,S[2*e]=z(e,5);P=new N(k,y,s+1,o,h),T=new N(S,v,0,l,h),E=new N(new Array(0),w,0,u,f)}(),q=!0),e.l_desc=new B(e.dyn_ltree,P),e.d_desc=new B(e.dyn_dtree,T),e.bl_desc=new B(e.bl_tree,E),e.bi_buf=0,e.bi_valid=0,L(e)},gt._tr_stored_block=X,gt._tr_flush_block=function(e,n,a,i){var o,l,c=0;e.level>0?(2===e.strm.data_type&&(e.strm.data_type=function(e){var n,a=4093624447;for(n=0;n<=31;n++,a>>>=1)if(1&a&&0!==e.dyn_ltree[2*n])return t;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return r;for(n=32;n<s;n++)if(0!==e.dyn_ltree[2*n])return r;return t}(e)),$(e,e.l_desc),$(e,e.d_desc),c=function(e){var t;for(Z(e,e.dyn_ltree,e.l_desc.max_code),Z(e,e.dyn_dtree,e.d_desc.max_code),$(e,e.bl_desc),t=u-1;t>=3&&0===e.bl_tree[2*_[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),o=e.opt_len+3+7>>>3,(l=e.static_len+3+7>>>3)<=o&&(o=l)):o=l=a+5,a+4<=o&&-1!==n?X(e,n,a,i):4===e.strategy||l===o?(O(e,2+(i?1:0),3),H(e,k,S)):(O(e,4+(i?1:0),3),function(e,t,r,n){var a;for(O(e,t-257,5),O(e,r-1,5),O(e,n-4,4),a=0;a<n;a++)O(e,e.bl_tree[2*_[a]+1],3);V(e,e.dyn_ltree,t-1),V(e,e.dyn_dtree,r-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,c+1),H(e,e.dyn_ltree,e.dyn_dtree)),L(e),i&&j(e)},gt._tr_tally=function(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(C[r]+s+1)]++,e.dyn_dtree[2*M(t)]++),e.last_lit===e.lit_bufsize-1},gt._tr_align=function(e){O(e,2,3),I(e,p,k),function(e){16===e.bi_valid?(R(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)},gt}function yt(){if(lt)return ot;return lt=1,ot=function(e,t,r,n){for(var a=65535&e|0,i=e>>>16&65535|0,s=0;0!==r;){r-=s=r>2e3?2e3:r;do{i=i+(a=a+t[n++]|0)|0}while(--s);a%=65521,i%=65521}return a|i<<16|0}}function vt(){if(ct)return ut;ct=1;var e=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();return ut=function(t,r,n,a){var i=e,s=a+n;t^=-1;for(var o=a;o<s;o++)t=t>>>8^i[255&(t^r[o])];return-1^t}}function wt(){return dt?ht:(dt=1,ht={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"})}function _t(){if(ft)return mt;ft=1;var e,t=it(),r=bt(),n=yt(),a=vt(),i=wt(),s=0,o=4,l=0,u=-2,c=-1,h=4,d=2,f=8,p=9,m=286,g=30,b=19,y=2*m+1,v=15,w=3,_=258,k=_+w+1,S=42,x=103,C=113,A=666,P=1,T=2,E=3,D=4;function N(e,t){return e.msg=i[t],t}function B(e){return(e<<1)-(e>4?9:0)}function M(e){for(var t=e.length;--t>=0;)e[t]=0}function R(e){var r=e.state,n=r.pending;n>e.avail_out&&(n=e.avail_out),0!==n&&(t.arraySet(e.output,r.pending_buf,r.pending_out,n,e.next_out),e.next_out+=n,r.pending_out+=n,e.total_out+=n,e.avail_out-=n,r.pending-=n,0===r.pending&&(r.pending_out=0))}function O(e,t){r._tr_flush_block(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,R(e.strm)}function I(e,t){e.pending_buf[e.pending++]=t}function z(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function F(e,t){var r,n,a=e.max_chain_length,i=e.strstart,s=e.prev_length,o=e.nice_match,l=e.strstart>e.w_size-k?e.strstart-(e.w_size-k):0,u=e.window,c=e.w_mask,h=e.prev,d=e.strstart+_,f=u[i+s-1],p=u[i+s];e.prev_length>=e.good_match&&(a>>=2),o>e.lookahead&&(o=e.lookahead);do{if(u[(r=t)+s]===p&&u[r+s-1]===f&&u[r]===u[i]&&u[++r]===u[i+1]){i+=2,r++;do{}while(u[++i]===u[++r]&&u[++i]===u[++r]&&u[++i]===u[++r]&&u[++i]===u[++r]&&u[++i]===u[++r]&&u[++i]===u[++r]&&u[++i]===u[++r]&&u[++i]===u[++r]&&i<d);if(n=_-(d-i),i=d-_,n>s){if(e.match_start=t,s=n,n>=o)break;f=u[i+s-1],p=u[i+s]}}}while((t=h[t&c])>l&&0!=--a);return s<=e.lookahead?s:e.lookahead}function L(e){var r,i,s,o,l,u,c,h,d,f,p=e.w_size;do{if(o=e.window_size-e.lookahead-e.strstart,e.strstart>=p+(p-k)){t.arraySet(e.window,e.window,p,p,0),e.match_start-=p,e.strstart-=p,e.block_start-=p,r=i=e.hash_size;do{s=e.head[--r],e.head[r]=s>=p?s-p:0}while(--i);r=i=p;do{s=e.prev[--r],e.prev[r]=s>=p?s-p:0}while(--i);o+=p}if(0===e.strm.avail_in)break;if(u=e.strm,c=e.window,h=e.strstart+e.lookahead,d=o,f=void 0,(f=u.avail_in)>d&&(f=d),i=0===f?0:(u.avail_in-=f,t.arraySet(c,u.input,u.next_in,f,h),1===u.state.wrap?u.adler=n(u.adler,c,f,h):2===u.state.wrap&&(u.adler=a(u.adler,c,f,h)),u.next_in+=f,u.total_in+=f,f),e.lookahead+=i,e.lookahead+e.insert>=w)for(l=e.strstart-e.insert,e.ins_h=e.window[l],e.ins_h=(e.ins_h<<e.hash_shift^e.window[l+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[l+w-1])&e.hash_mask,e.prev[l&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=l,l++,e.insert--,!(e.lookahead+e.insert<w)););}while(e.lookahead<k&&0!==e.strm.avail_in)}function j(e,t){for(var n,a;;){if(e.lookahead<k){if(L(e),e.lookahead<k&&t===s)return P;if(0===e.lookahead)break}if(n=0,e.lookahead>=w&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+w-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-k&&(e.match_length=F(e,n)),e.match_length>=w)if(a=r._tr_tally(e,e.strstart-e.match_start,e.match_length-w),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=w){e.match_length--;do{e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+w-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else a=r._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(a&&(O(e,!1),0===e.strm.avail_out))return P}return e.insert=e.strstart<w-1?e.strstart:w-1,t===o?(O(e,!0),0===e.strm.avail_out?E:D):e.last_lit&&(O(e,!1),0===e.strm.avail_out)?P:T}function U(e,t){for(var n,a,i;;){if(e.lookahead<k){if(L(e),e.lookahead<k&&t===s)return P;if(0===e.lookahead)break}if(n=0,e.lookahead>=w&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+w-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=w-1,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-k&&(e.match_length=F(e,n),e.match_length<=5&&(1===e.strategy||e.match_length===w&&e.strstart-e.match_start>4096)&&(e.match_length=w-1)),e.prev_length>=w&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-w,a=r._tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-w),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+w-1])&e.hash_mask,n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=w-1,e.strstart++,a&&(O(e,!1),0===e.strm.avail_out))return P}else if(e.match_available){if((a=r._tr_tally(e,0,e.window[e.strstart-1]))&&O(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return P}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(a=r._tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<w-1?e.strstart:w-1,t===o?(O(e,!0),0===e.strm.avail_out?E:D):e.last_lit&&(O(e,!1),0===e.strm.avail_out)?P:T}function W(e,t,r,n,a){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=a}function H(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=f,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new t.Buf16(2*y),this.dyn_dtree=new t.Buf16(2*(2*g+1)),this.bl_tree=new t.Buf16(2*(2*b+1)),M(this.dyn_ltree),M(this.dyn_dtree),M(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new t.Buf16(v+1),this.heap=new t.Buf16(2*m+1),M(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new t.Buf16(2*m+1),M(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function $(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=d,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?S:C,e.adler=2===t.wrap?0:1,t.last_flush=s,r._tr_init(t),l):N(e,u)}function Z(t){var r,n=$(t);return n===l&&((r=t.state).window_size=2*r.w_size,M(r.head),r.max_lazy_match=e[r.level].max_lazy,r.good_match=e[r.level].good_length,r.nice_match=e[r.level].nice_length,r.max_chain_length=e[r.level].max_chain,r.strstart=0,r.block_start=0,r.lookahead=0,r.insert=0,r.match_length=r.prev_length=w-1,r.match_available=0,r.ins_h=0),n}function V(e,r,n,a,i,s){if(!e)return u;var o=1;if(r===c&&(r=6),a<0?(o=0,a=-a):a>15&&(o=2,a-=16),i<1||i>p||n!==f||a<8||a>15||r<0||r>9||s<0||s>h)return N(e,u);8===a&&(a=9);var l=new H;return e.state=l,l.strm=e,l.wrap=o,l.gzhead=null,l.w_bits=a,l.w_size=1<<l.w_bits,l.w_mask=l.w_size-1,l.hash_bits=i+7,l.hash_size=1<<l.hash_bits,l.hash_mask=l.hash_size-1,l.hash_shift=~~((l.hash_bits+w-1)/w),l.window=new t.Buf8(2*l.w_size),l.head=new t.Buf16(l.hash_size),l.prev=new t.Buf16(l.w_size),l.lit_bufsize=1<<i+6,l.pending_buf_size=4*l.lit_bufsize,l.pending_buf=new t.Buf8(l.pending_buf_size),l.d_buf=1*l.lit_bufsize,l.l_buf=3*l.lit_bufsize,l.level=r,l.strategy=s,l.method=n,Z(e)}return e=[new W(0,0,0,0,(function(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(L(e),0===e.lookahead&&t===s)return P;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,O(e,!1),0===e.strm.avail_out))return P;if(e.strstart-e.block_start>=e.w_size-k&&(O(e,!1),0===e.strm.avail_out))return P}return e.insert=0,t===o?(O(e,!0),0===e.strm.avail_out?E:D):(e.strstart>e.block_start&&(O(e,!1),e.strm.avail_out),P)})),new W(4,4,8,4,j),new W(4,5,16,8,j),new W(4,6,32,32,j),new W(4,4,16,16,U),new W(8,16,32,32,U),new W(8,16,128,128,U),new W(8,32,128,256,U),new W(32,128,258,1024,U),new W(32,258,258,4096,U)],mt.deflateInit=function(e,t){return V(e,t,f,15,8,0)},mt.deflateInit2=V,mt.deflateReset=Z,mt.deflateResetKeep=$,mt.deflateSetHeader=function(e,t){return e&&e.state?2!==e.state.wrap?u:(e.state.gzhead=t,l):u},mt.deflate=function(t,n){var i,c,h,d;if(!t||!t.state||n>5||n<0)return t?N(t,u):u;if(c=t.state,!t.output||!t.input&&0!==t.avail_in||c.status===A&&n!==o)return N(t,0===t.avail_out?-5:u);if(c.strm=t,i=c.last_flush,c.last_flush=n,c.status===S)if(2===c.wrap)t.adler=0,I(c,31),I(c,139),I(c,8),c.gzhead?(I(c,(c.gzhead.text?1:0)+(c.gzhead.hcrc?2:0)+(c.gzhead.extra?4:0)+(c.gzhead.name?8:0)+(c.gzhead.comment?16:0)),I(c,255&c.gzhead.time),I(c,c.gzhead.time>>8&255),I(c,c.gzhead.time>>16&255),I(c,c.gzhead.time>>24&255),I(c,9===c.level?2:c.strategy>=2||c.level<2?4:0),I(c,255&c.gzhead.os),c.gzhead.extra&&c.gzhead.extra.length&&(I(c,255&c.gzhead.extra.length),I(c,c.gzhead.extra.length>>8&255)),c.gzhead.hcrc&&(t.adler=a(t.adler,c.pending_buf,c.pending,0)),c.gzindex=0,c.status=69):(I(c,0),I(c,0),I(c,0),I(c,0),I(c,0),I(c,9===c.level?2:c.strategy>=2||c.level<2?4:0),I(c,3),c.status=C);else{var p=f+(c.w_bits-8<<4)<<8;p|=(c.strategy>=2||c.level<2?0:c.level<6?1:6===c.level?2:3)<<6,0!==c.strstart&&(p|=32),p+=31-p%31,c.status=C,z(c,p),0!==c.strstart&&(z(c,t.adler>>>16),z(c,65535&t.adler)),t.adler=1}if(69===c.status)if(c.gzhead.extra){for(h=c.pending;c.gzindex<(65535&c.gzhead.extra.length)&&(c.pending!==c.pending_buf_size||(c.gzhead.hcrc&&c.pending>h&&(t.adler=a(t.adler,c.pending_buf,c.pending-h,h)),R(t),h=c.pending,c.pending!==c.pending_buf_size));)I(c,255&c.gzhead.extra[c.gzindex]),c.gzindex++;c.gzhead.hcrc&&c.pending>h&&(t.adler=a(t.adler,c.pending_buf,c.pending-h,h)),c.gzindex===c.gzhead.extra.length&&(c.gzindex=0,c.status=73)}else c.status=73;if(73===c.status)if(c.gzhead.name){h=c.pending;do{if(c.pending===c.pending_buf_size&&(c.gzhead.hcrc&&c.pending>h&&(t.adler=a(t.adler,c.pending_buf,c.pending-h,h)),R(t),h=c.pending,c.pending===c.pending_buf_size)){d=1;break}d=c.gzindex<c.gzhead.name.length?255&c.gzhead.name.charCodeAt(c.gzindex++):0,I(c,d)}while(0!==d);c.gzhead.hcrc&&c.pending>h&&(t.adler=a(t.adler,c.pending_buf,c.pending-h,h)),0===d&&(c.gzindex=0,c.status=91)}else c.status=91;if(91===c.status)if(c.gzhead.comment){h=c.pending;do{if(c.pending===c.pending_buf_size&&(c.gzhead.hcrc&&c.pending>h&&(t.adler=a(t.adler,c.pending_buf,c.pending-h,h)),R(t),h=c.pending,c.pending===c.pending_buf_size)){d=1;break}d=c.gzindex<c.gzhead.comment.length?255&c.gzhead.comment.charCodeAt(c.gzindex++):0,I(c,d)}while(0!==d);c.gzhead.hcrc&&c.pending>h&&(t.adler=a(t.adler,c.pending_buf,c.pending-h,h)),0===d&&(c.status=x)}else c.status=x;if(c.status===x&&(c.gzhead.hcrc?(c.pending+2>c.pending_buf_size&&R(t),c.pending+2<=c.pending_buf_size&&(I(c,255&t.adler),I(c,t.adler>>8&255),t.adler=0,c.status=C)):c.status=C),0!==c.pending){if(R(t),0===t.avail_out)return c.last_flush=-1,l}else if(0===t.avail_in&&B(n)<=B(i)&&n!==o)return N(t,-5);if(c.status===A&&0!==t.avail_in)return N(t,-5);if(0!==t.avail_in||0!==c.lookahead||n!==s&&c.status!==A){var m=2===c.strategy?function(e,t){for(var n;;){if(0===e.lookahead&&(L(e),0===e.lookahead)){if(t===s)return P;break}if(e.match_length=0,n=r._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(O(e,!1),0===e.strm.avail_out))return P}return e.insert=0,t===o?(O(e,!0),0===e.strm.avail_out?E:D):e.last_lit&&(O(e,!1),0===e.strm.avail_out)?P:T}(c,n):3===c.strategy?function(e,t){for(var n,a,i,l,u=e.window;;){if(e.lookahead<=_){if(L(e),e.lookahead<=_&&t===s)return P;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=w&&e.strstart>0&&(a=u[i=e.strstart-1])===u[++i]&&a===u[++i]&&a===u[++i]){l=e.strstart+_;do{}while(a===u[++i]&&a===u[++i]&&a===u[++i]&&a===u[++i]&&a===u[++i]&&a===u[++i]&&a===u[++i]&&a===u[++i]&&i<l);e.match_length=_-(l-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=w?(n=r._tr_tally(e,1,e.match_length-w),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=r._tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(O(e,!1),0===e.strm.avail_out))return P}return e.insert=0,t===o?(O(e,!0),0===e.strm.avail_out?E:D):e.last_lit&&(O(e,!1),0===e.strm.avail_out)?P:T}(c,n):e[c.level].func(c,n);if(m!==E&&m!==D||(c.status=A),m===P||m===E)return 0===t.avail_out&&(c.last_flush=-1),l;if(m===T&&(1===n?r._tr_align(c):5!==n&&(r._tr_stored_block(c,0,0,!1),3===n&&(M(c.head),0===c.lookahead&&(c.strstart=0,c.block_start=0,c.insert=0))),R(t),0===t.avail_out))return c.last_flush=-1,l}return n!==o?l:c.wrap<=0?1:(2===c.wrap?(I(c,255&t.adler),I(c,t.adler>>8&255),I(c,t.adler>>16&255),I(c,t.adler>>24&255),I(c,255&t.total_in),I(c,t.total_in>>8&255),I(c,t.total_in>>16&255),I(c,t.total_in>>24&255)):(z(c,t.adler>>>16),z(c,65535&t.adler)),R(t),c.wrap>0&&(c.wrap=-c.wrap),0!==c.pending?l:1)},mt.deflateEnd=function(e){var t;return e&&e.state?(t=e.state.status)!==S&&69!==t&&73!==t&&91!==t&&t!==x&&t!==C&&t!==A?N(e,u):(e.state=null,t===C?N(e,-3):l):u},mt.deflateSetDictionary=function(e,r){var a,i,s,o,c,h,d,f,p=r.length;if(!e||!e.state)return u;if(2===(o=(a=e.state).wrap)||1===o&&a.status!==S||a.lookahead)return u;for(1===o&&(e.adler=n(e.adler,r,p,0)),a.wrap=0,p>=a.w_size&&(0===o&&(M(a.head),a.strstart=0,a.block_start=0,a.insert=0),f=new t.Buf8(a.w_size),t.arraySet(f,r,p-a.w_size,a.w_size,0),r=f,p=a.w_size),c=e.avail_in,h=e.next_in,d=e.input,e.avail_in=p,e.next_in=0,e.input=r,L(a);a.lookahead>=w;){i=a.strstart,s=a.lookahead-(w-1);do{a.ins_h=(a.ins_h<<a.hash_shift^a.window[i+w-1])&a.hash_mask,a.prev[i&a.w_mask]=a.head[a.ins_h],a.head[a.ins_h]=i,i++}while(--s);a.strstart=i,a.lookahead=w-1,L(a)}return a.strstart+=a.lookahead,a.block_start=a.strstart,a.insert=a.lookahead,a.lookahead=0,a.match_length=a.prev_length=w-1,a.match_available=0,e.next_in=h,e.input=d,e.avail_in=c,a.wrap=o,l},mt.deflateInfo="pako deflate (from Nodeca project)",mt}var kt,St,xt,Ct,At={};function Pt(){if(kt)return At;kt=1;var e=it(),t=!0,r=!0;try{String.fromCharCode.apply(null,[0])}catch(e){t=!1}try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(e){r=!1}for(var n=new e.Buf8(256),a=0;a<256;a++)n[a]=a>=252?6:a>=248?5:a>=240?4:a>=224?3:a>=192?2:1;function i(n,a){if(a<65534&&(n.subarray&&r||!n.subarray&&t))return String.fromCharCode.apply(null,e.shrinkBuf(n,a));for(var i="",s=0;s<a;s++)i+=String.fromCharCode(n[s]);return i}return n[254]=n[254]=1,At.string2buf=function(t){var r,n,a,i,s,o=t.length,l=0;for(i=0;i<o;i++)55296==(64512&(n=t.charCodeAt(i)))&&i+1<o&&56320==(64512&(a=t.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(a-56320),i++),l+=n<128?1:n<2048?2:n<65536?3:4;for(r=new e.Buf8(l),s=0,i=0;s<l;i++)55296==(64512&(n=t.charCodeAt(i)))&&i+1<o&&56320==(64512&(a=t.charCodeAt(i+1)))&&(n=65536+(n-55296<<10)+(a-56320),i++),n<128?r[s++]=n:n<2048?(r[s++]=192|n>>>6,r[s++]=128|63&n):n<65536?(r[s++]=224|n>>>12,r[s++]=128|n>>>6&63,r[s++]=128|63&n):(r[s++]=240|n>>>18,r[s++]=128|n>>>12&63,r[s++]=128|n>>>6&63,r[s++]=128|63&n);return r},At.buf2binstring=function(e){return i(e,e.length)},At.binstring2buf=function(t){for(var r=new e.Buf8(t.length),n=0,a=r.length;n<a;n++)r[n]=t.charCodeAt(n);return r},At.buf2string=function(e,t){var r,a,s,o,l=t||e.length,u=new Array(2*l);for(a=0,r=0;r<l;)if((s=e[r++])<128)u[a++]=s;else if((o=n[s])>4)u[a++]=65533,r+=o-1;else{for(s&=2===o?31:3===o?15:7;o>1&&r<l;)s=s<<6|63&e[r++],o--;o>1?u[a++]=65533:s<65536?u[a++]=s:(s-=65536,u[a++]=55296|s>>10&1023,u[a++]=56320|1023&s)}return i(u,a)},At.utf8border=function(e,t){var r;for((t=t||e.length)>e.length&&(t=e.length),r=t-1;r>=0&&128==(192&e[r]);)r--;return r<0||0===r?t:r+n[e[r]]>t?r:t},At}function Tt(){if(xt)return St;return xt=1,St=function(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}}var Et,Dt,Nt,Bt,Mt,Rt,Ot,It,zt,Ft,Lt,jt,Ut,Wt,Ht={},$t={};function Zt(){if(Mt)return $t;Mt=1;var e=it(),t=yt(),r=vt(),n=Dt?Et:(Dt=1,Et=function(e,t){var r,n,a,i,s,o,l,u,c,h,d,f,p,m,g,b,y,v,w,_,k,S,x,C,A;r=e.state,n=e.next_in,C=e.input,a=n+(e.avail_in-5),i=e.next_out,A=e.output,s=i-(t-e.avail_out),o=i+(e.avail_out-257),l=r.dmax,u=r.wsize,c=r.whave,h=r.wnext,d=r.window,f=r.hold,p=r.bits,m=r.lencode,g=r.distcode,b=(1<<r.lenbits)-1,y=(1<<r.distbits)-1;e:do{p<15&&(f+=C[n++]<<p,p+=8,f+=C[n++]<<p,p+=8),v=m[f&b];t:for(;;){if(f>>>=w=v>>>24,p-=w,0==(w=v>>>16&255))A[i++]=65535&v;else{if(!(16&w)){if(0==(64&w)){v=m[(65535&v)+(f&(1<<w)-1)];continue t}if(32&w){r.mode=12;break e}e.msg="invalid literal/length code",r.mode=30;break e}_=65535&v,(w&=15)&&(p<w&&(f+=C[n++]<<p,p+=8),_+=f&(1<<w)-1,f>>>=w,p-=w),p<15&&(f+=C[n++]<<p,p+=8,f+=C[n++]<<p,p+=8),v=g[f&y];r:for(;;){if(f>>>=w=v>>>24,p-=w,!(16&(w=v>>>16&255))){if(0==(64&w)){v=g[(65535&v)+(f&(1<<w)-1)];continue r}e.msg="invalid distance code",r.mode=30;break e}if(k=65535&v,p<(w&=15)&&(f+=C[n++]<<p,(p+=8)<w&&(f+=C[n++]<<p,p+=8)),(k+=f&(1<<w)-1)>l){e.msg="invalid distance too far back",r.mode=30;break e}if(f>>>=w,p-=w,k>(w=i-s)){if((w=k-w)>c&&r.sane){e.msg="invalid distance too far back",r.mode=30;break e}if(S=0,x=d,0===h){if(S+=u-w,w<_){_-=w;do{A[i++]=d[S++]}while(--w);S=i-k,x=A}}else if(h<w){if(S+=u+h-w,(w-=h)<_){_-=w;do{A[i++]=d[S++]}while(--w);if(S=0,h<_){_-=w=h;do{A[i++]=d[S++]}while(--w);S=i-k,x=A}}}else if(S+=h-w,w<_){_-=w;do{A[i++]=d[S++]}while(--w);S=i-k,x=A}for(;_>2;)A[i++]=x[S++],A[i++]=x[S++],A[i++]=x[S++],_-=3;_&&(A[i++]=x[S++],_>1&&(A[i++]=x[S++]))}else{S=i-k;do{A[i++]=A[S++],A[i++]=A[S++],A[i++]=A[S++],_-=3}while(_>2);_&&(A[i++]=A[S++],_>1&&(A[i++]=A[S++]))}break}}break}}while(n<a&&i<o);n-=_=p>>3,f&=(1<<(p-=_<<3))-1,e.next_in=n,e.next_out=i,e.avail_in=n<a?a-n+5:5-(n-a),e.avail_out=i<o?o-i+257:257-(i-o),r.hold=f,r.bits=p}),a=function(){if(Bt)return Nt;Bt=1;var e=it(),t=15,r=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],n=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],a=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],i=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];return Nt=function(s,o,l,u,c,h,d,f){var p,m,g,b,y,v,w,_,k,S=f.bits,x=0,C=0,A=0,P=0,T=0,E=0,D=0,N=0,B=0,M=0,R=null,O=0,I=new e.Buf16(16),z=new e.Buf16(16),F=null,L=0;for(x=0;x<=t;x++)I[x]=0;for(C=0;C<u;C++)I[o[l+C]]++;for(T=S,P=t;P>=1&&0===I[P];P--);if(T>P&&(T=P),0===P)return c[h++]=20971520,c[h++]=20971520,f.bits=1,0;for(A=1;A<P&&0===I[A];A++);for(T<A&&(T=A),N=1,x=1;x<=t;x++)if(N<<=1,(N-=I[x])<0)return-1;if(N>0&&(0===s||1!==P))return-1;for(z[1]=0,x=1;x<t;x++)z[x+1]=z[x]+I[x];for(C=0;C<u;C++)0!==o[l+C]&&(d[z[o[l+C]]++]=C);if(0===s?(R=F=d,v=19):1===s?(R=r,O-=257,F=n,L-=257,v=256):(R=a,F=i,v=-1),M=0,C=0,x=A,y=h,E=T,D=0,g=-1,b=(B=1<<T)-1,1===s&&B>852||2===s&&B>592)return 1;for(;;){w=x-D,d[C]<v?(_=0,k=d[C]):d[C]>v?(_=F[L+d[C]],k=R[O+d[C]]):(_=96,k=0),p=1<<x-D,A=m=1<<E;do{c[y+(M>>D)+(m-=p)]=w<<24|_<<16|k|0}while(0!==m);for(p=1<<x-1;M&p;)p>>=1;if(0!==p?(M&=p-1,M+=p):M=0,C++,0==--I[x]){if(x===P)break;x=o[l+d[C]]}if(x>T&&(M&b)!==g){for(0===D&&(D=T),y+=A,N=1<<(E=x-D);E+D<P&&!((N-=I[E+D])<=0);)E++,N<<=1;if(B+=1<<E,1===s&&B>852||2===s&&B>592)return 1;c[g=M&b]=T<<24|E<<16|y-h|0}}return 0!==M&&(c[y+M]=x-D<<24|64<<16|0),f.bits=T,0}}(),i=1,s=2,o=0,l=-2,u=1,c=12,h=30,d=852,f=592;function p(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function m(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new e.Buf16(320),this.work=new e.Buf16(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function g(t){var r;return t&&t.state?(r=t.state,t.total_in=t.total_out=r.total=0,t.msg="",r.wrap&&(t.adler=1&r.wrap),r.mode=u,r.last=0,r.havedict=0,r.dmax=32768,r.head=null,r.hold=0,r.bits=0,r.lencode=r.lendyn=new e.Buf32(d),r.distcode=r.distdyn=new e.Buf32(f),r.sane=1,r.back=-1,o):l}function b(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,g(e)):l}function y(e,t){var r,n;return e&&e.state?(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?l:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,b(e))):l}function v(e,t){var r,n;return e?(n=new m,e.state=n,n.window=null,(r=y(e,t))!==o&&(e.state=null),r):l}var w,_,k=!0;function S(t){if(k){var r;for(w=new e.Buf32(512),_=new e.Buf32(32),r=0;r<144;)t.lens[r++]=8;for(;r<256;)t.lens[r++]=9;for(;r<280;)t.lens[r++]=7;for(;r<288;)t.lens[r++]=8;for(a(i,t.lens,0,288,w,0,t.work,{bits:9}),r=0;r<32;)t.lens[r++]=5;a(s,t.lens,0,32,_,0,t.work,{bits:5}),k=!1}t.lencode=w,t.lenbits=9,t.distcode=_,t.distbits=5}function x(t,r,n,a){var i,s=t.state;return null===s.window&&(s.wsize=1<<s.wbits,s.wnext=0,s.whave=0,s.window=new e.Buf8(s.wsize)),a>=s.wsize?(e.arraySet(s.window,r,n-s.wsize,s.wsize,0),s.wnext=0,s.whave=s.wsize):((i=s.wsize-s.wnext)>a&&(i=a),e.arraySet(s.window,r,n-a,i,s.wnext),(a-=i)?(e.arraySet(s.window,r,n-a,a,0),s.wnext=a,s.whave=s.wsize):(s.wnext+=i,s.wnext===s.wsize&&(s.wnext=0),s.whave<s.wsize&&(s.whave+=i))),0}return $t.inflateReset=b,$t.inflateReset2=y,$t.inflateResetKeep=g,$t.inflateInit=function(e){return v(e,15)},$t.inflateInit2=v,$t.inflate=function(d,f){var m,g,b,y,v,w,_,k,C,A,P,T,E,D,N,B,M,R,O,I,z,F,L,j,U=0,W=new e.Buf8(4),H=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!d||!d.state||!d.output||!d.input&&0!==d.avail_in)return l;(m=d.state).mode===c&&(m.mode=13),v=d.next_out,b=d.output,_=d.avail_out,y=d.next_in,g=d.input,w=d.avail_in,k=m.hold,C=m.bits,A=w,P=_,F=o;e:for(;;)switch(m.mode){case u:if(0===m.wrap){m.mode=13;break}for(;C<16;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}if(2&m.wrap&&35615===k){m.check=0,W[0]=255&k,W[1]=k>>>8&255,m.check=r(m.check,W,2,0),k=0,C=0,m.mode=2;break}if(m.flags=0,m.head&&(m.head.done=!1),!(1&m.wrap)||(((255&k)<<8)+(k>>8))%31){d.msg="incorrect header check",m.mode=h;break}if(8!=(15&k)){d.msg="unknown compression method",m.mode=h;break}if(C-=4,z=8+(15&(k>>>=4)),0===m.wbits)m.wbits=z;else if(z>m.wbits){d.msg="invalid window size",m.mode=h;break}m.dmax=1<<z,d.adler=m.check=1,m.mode=512&k?10:c,k=0,C=0;break;case 2:for(;C<16;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}if(m.flags=k,8!=(255&m.flags)){d.msg="unknown compression method",m.mode=h;break}if(57344&m.flags){d.msg="unknown header flags set",m.mode=h;break}m.head&&(m.head.text=k>>8&1),512&m.flags&&(W[0]=255&k,W[1]=k>>>8&255,m.check=r(m.check,W,2,0)),k=0,C=0,m.mode=3;case 3:for(;C<32;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}m.head&&(m.head.time=k),512&m.flags&&(W[0]=255&k,W[1]=k>>>8&255,W[2]=k>>>16&255,W[3]=k>>>24&255,m.check=r(m.check,W,4,0)),k=0,C=0,m.mode=4;case 4:for(;C<16;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}m.head&&(m.head.xflags=255&k,m.head.os=k>>8),512&m.flags&&(W[0]=255&k,W[1]=k>>>8&255,m.check=r(m.check,W,2,0)),k=0,C=0,m.mode=5;case 5:if(1024&m.flags){for(;C<16;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}m.length=k,m.head&&(m.head.extra_len=k),512&m.flags&&(W[0]=255&k,W[1]=k>>>8&255,m.check=r(m.check,W,2,0)),k=0,C=0}else m.head&&(m.head.extra=null);m.mode=6;case 6:if(1024&m.flags&&((T=m.length)>w&&(T=w),T&&(m.head&&(z=m.head.extra_len-m.length,m.head.extra||(m.head.extra=new Array(m.head.extra_len)),e.arraySet(m.head.extra,g,y,T,z)),512&m.flags&&(m.check=r(m.check,g,T,y)),w-=T,y+=T,m.length-=T),m.length))break e;m.length=0,m.mode=7;case 7:if(2048&m.flags){if(0===w)break e;T=0;do{z=g[y+T++],m.head&&z&&m.length<65536&&(m.head.name+=String.fromCharCode(z))}while(z&&T<w);if(512&m.flags&&(m.check=r(m.check,g,T,y)),w-=T,y+=T,z)break e}else m.head&&(m.head.name=null);m.length=0,m.mode=8;case 8:if(4096&m.flags){if(0===w)break e;T=0;do{z=g[y+T++],m.head&&z&&m.length<65536&&(m.head.comment+=String.fromCharCode(z))}while(z&&T<w);if(512&m.flags&&(m.check=r(m.check,g,T,y)),w-=T,y+=T,z)break e}else m.head&&(m.head.comment=null);m.mode=9;case 9:if(512&m.flags){for(;C<16;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}if(k!==(65535&m.check)){d.msg="header crc mismatch",m.mode=h;break}k=0,C=0}m.head&&(m.head.hcrc=m.flags>>9&1,m.head.done=!0),d.adler=m.check=0,m.mode=c;break;case 10:for(;C<32;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}d.adler=m.check=p(k),k=0,C=0,m.mode=11;case 11:if(0===m.havedict)return d.next_out=v,d.avail_out=_,d.next_in=y,d.avail_in=w,m.hold=k,m.bits=C,2;d.adler=m.check=1,m.mode=c;case c:if(5===f||6===f)break e;case 13:if(m.last){k>>>=7&C,C-=7&C,m.mode=27;break}for(;C<3;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}switch(m.last=1&k,C-=1,3&(k>>>=1)){case 0:m.mode=14;break;case 1:if(S(m),m.mode=20,6===f){k>>>=2,C-=2;break e}break;case 2:m.mode=17;break;case 3:d.msg="invalid block type",m.mode=h}k>>>=2,C-=2;break;case 14:for(k>>>=7&C,C-=7&C;C<32;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}if((65535&k)!=(k>>>16^65535)){d.msg="invalid stored block lengths",m.mode=h;break}if(m.length=65535&k,k=0,C=0,m.mode=15,6===f)break e;case 15:m.mode=16;case 16:if(T=m.length){if(T>w&&(T=w),T>_&&(T=_),0===T)break e;e.arraySet(b,g,y,T,v),w-=T,y+=T,_-=T,v+=T,m.length-=T;break}m.mode=c;break;case 17:for(;C<14;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}if(m.nlen=257+(31&k),k>>>=5,C-=5,m.ndist=1+(31&k),k>>>=5,C-=5,m.ncode=4+(15&k),k>>>=4,C-=4,m.nlen>286||m.ndist>30){d.msg="too many length or distance symbols",m.mode=h;break}m.have=0,m.mode=18;case 18:for(;m.have<m.ncode;){for(;C<3;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}m.lens[H[m.have++]]=7&k,k>>>=3,C-=3}for(;m.have<19;)m.lens[H[m.have++]]=0;if(m.lencode=m.lendyn,m.lenbits=7,L={bits:m.lenbits},F=a(0,m.lens,0,19,m.lencode,0,m.work,L),m.lenbits=L.bits,F){d.msg="invalid code lengths set",m.mode=h;break}m.have=0,m.mode=19;case 19:for(;m.have<m.nlen+m.ndist;){for(;B=(U=m.lencode[k&(1<<m.lenbits)-1])>>>16&255,M=65535&U,!((N=U>>>24)<=C);){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}if(M<16)k>>>=N,C-=N,m.lens[m.have++]=M;else{if(16===M){for(j=N+2;C<j;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}if(k>>>=N,C-=N,0===m.have){d.msg="invalid bit length repeat",m.mode=h;break}z=m.lens[m.have-1],T=3+(3&k),k>>>=2,C-=2}else if(17===M){for(j=N+3;C<j;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}C-=N,z=0,T=3+(7&(k>>>=N)),k>>>=3,C-=3}else{for(j=N+7;C<j;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}C-=N,z=0,T=11+(127&(k>>>=N)),k>>>=7,C-=7}if(m.have+T>m.nlen+m.ndist){d.msg="invalid bit length repeat",m.mode=h;break}for(;T--;)m.lens[m.have++]=z}}if(m.mode===h)break;if(0===m.lens[256]){d.msg="invalid code -- missing end-of-block",m.mode=h;break}if(m.lenbits=9,L={bits:m.lenbits},F=a(i,m.lens,0,m.nlen,m.lencode,0,m.work,L),m.lenbits=L.bits,F){d.msg="invalid literal/lengths set",m.mode=h;break}if(m.distbits=6,m.distcode=m.distdyn,L={bits:m.distbits},F=a(s,m.lens,m.nlen,m.ndist,m.distcode,0,m.work,L),m.distbits=L.bits,F){d.msg="invalid distances set",m.mode=h;break}if(m.mode=20,6===f)break e;case 20:m.mode=21;case 21:if(w>=6&&_>=258){d.next_out=v,d.avail_out=_,d.next_in=y,d.avail_in=w,m.hold=k,m.bits=C,n(d,P),v=d.next_out,b=d.output,_=d.avail_out,y=d.next_in,g=d.input,w=d.avail_in,k=m.hold,C=m.bits,m.mode===c&&(m.back=-1);break}for(m.back=0;B=(U=m.lencode[k&(1<<m.lenbits)-1])>>>16&255,M=65535&U,!((N=U>>>24)<=C);){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}if(B&&0==(240&B)){for(R=N,O=B,I=M;B=(U=m.lencode[I+((k&(1<<R+O)-1)>>R)])>>>16&255,M=65535&U,!(R+(N=U>>>24)<=C);){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}k>>>=R,C-=R,m.back+=R}if(k>>>=N,C-=N,m.back+=N,m.length=M,0===B){m.mode=26;break}if(32&B){m.back=-1,m.mode=c;break}if(64&B){d.msg="invalid literal/length code",m.mode=h;break}m.extra=15&B,m.mode=22;case 22:if(m.extra){for(j=m.extra;C<j;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}m.length+=k&(1<<m.extra)-1,k>>>=m.extra,C-=m.extra,m.back+=m.extra}m.was=m.length,m.mode=23;case 23:for(;B=(U=m.distcode[k&(1<<m.distbits)-1])>>>16&255,M=65535&U,!((N=U>>>24)<=C);){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}if(0==(240&B)){for(R=N,O=B,I=M;B=(U=m.distcode[I+((k&(1<<R+O)-1)>>R)])>>>16&255,M=65535&U,!(R+(N=U>>>24)<=C);){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}k>>>=R,C-=R,m.back+=R}if(k>>>=N,C-=N,m.back+=N,64&B){d.msg="invalid distance code",m.mode=h;break}m.offset=M,m.extra=15&B,m.mode=24;case 24:if(m.extra){for(j=m.extra;C<j;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}m.offset+=k&(1<<m.extra)-1,k>>>=m.extra,C-=m.extra,m.back+=m.extra}if(m.offset>m.dmax){d.msg="invalid distance too far back",m.mode=h;break}m.mode=25;case 25:if(0===_)break e;if(T=P-_,m.offset>T){if((T=m.offset-T)>m.whave&&m.sane){d.msg="invalid distance too far back",m.mode=h;break}T>m.wnext?(T-=m.wnext,E=m.wsize-T):E=m.wnext-T,T>m.length&&(T=m.length),D=m.window}else D=b,E=v-m.offset,T=m.length;T>_&&(T=_),_-=T,m.length-=T;do{b[v++]=D[E++]}while(--T);0===m.length&&(m.mode=21);break;case 26:if(0===_)break e;b[v++]=m.length,_--,m.mode=21;break;case 27:if(m.wrap){for(;C<32;){if(0===w)break e;w--,k|=g[y++]<<C,C+=8}if(P-=_,d.total_out+=P,m.total+=P,P&&(d.adler=m.check=m.flags?r(m.check,b,P,v-P):t(m.check,b,P,v-P)),P=_,(m.flags?k:p(k))!==m.check){d.msg="incorrect data check",m.mode=h;break}k=0,C=0}m.mode=28;case 28:if(m.wrap&&m.flags){for(;C<32;){if(0===w)break e;w--,k+=g[y++]<<C,C+=8}if(k!==(4294967295&m.total)){d.msg="incorrect length check",m.mode=h;break}k=0,C=0}m.mode=29;case 29:F=1;break e;case h:F=-3;break e;case 31:return-4;default:return l}return d.next_out=v,d.avail_out=_,d.next_in=y,d.avail_in=w,m.hold=k,m.bits=C,(m.wsize||P!==d.avail_out&&m.mode<h&&(m.mode<27||4!==f))&&x(d,d.output,d.next_out,P-d.avail_out),A-=d.avail_in,P-=d.avail_out,d.total_in+=A,d.total_out+=P,m.total+=P,m.wrap&&P&&(d.adler=m.check=m.flags?r(m.check,b,P,d.next_out-P):t(m.check,b,P,d.next_out-P)),d.data_type=m.bits+(m.last?64:0)+(m.mode===c?128:0)+(20===m.mode||15===m.mode?256:0),(0===A&&0===P||4===f)&&F===o&&(F=-5),F},$t.inflateEnd=function(e){if(!e||!e.state)return l;var t=e.state;return t.window&&(t.window=null),e.state=null,o},$t.inflateGetHeader=function(e,t){var r;return e&&e.state?0==(2&(r=e.state).wrap)?l:(r.head=t,t.done=!1,o):l},$t.inflateSetDictionary=function(e,r){var n,a=r.length;return e&&e.state?0!==(n=e.state).wrap&&11!==n.mode?l:11===n.mode&&t(1,r,a,0)!==n.check?-3:x(e,r,a,a)?(n.mode=31,-4):(n.havedict=1,o):l},$t.inflateInfo="pako inflate (from Nodeca project)",$t}function Vt(){return Ot?Rt:(Ot=1,Rt={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8})}function qt(){if(Ft)return Ht;Ft=1;var e=Zt(),t=it(),r=Pt(),n=Vt(),a=wt(),i=Tt(),s=zt?It:(zt=1,It=function(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}),o=Object.prototype.toString;function l(u){if(!(this instanceof l))return new l(u);this.options=t.assign({chunkSize:16384,windowBits:0,to:""},u||{});var c=this.options;c.raw&&c.windowBits>=0&&c.windowBits<16&&(c.windowBits=-c.windowBits,0===c.windowBits&&(c.windowBits=-15)),!(c.windowBits>=0&&c.windowBits<16)||u&&u.windowBits||(c.windowBits+=32),c.windowBits>15&&c.windowBits<48&&0==(15&c.windowBits)&&(c.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new i,this.strm.avail_out=0;var h=e.inflateInit2(this.strm,c.windowBits);if(h!==n.Z_OK)throw new Error(a[h]);if(this.header=new s,e.inflateGetHeader(this.strm,this.header),c.dictionary&&("string"==typeof c.dictionary?c.dictionary=r.string2buf(c.dictionary):"[object ArrayBuffer]"===o.call(c.dictionary)&&(c.dictionary=new Uint8Array(c.dictionary)),c.raw&&(h=e.inflateSetDictionary(this.strm,c.dictionary))!==n.Z_OK))throw new Error(a[h])}function u(e,t){var r=new l(t);if(r.push(e,!0),r.err)throw r.msg||a[r.err];return r.result}return l.prototype.push=function(a,i){var s,l,u,c,h,d=this.strm,f=this.options.chunkSize,p=this.options.dictionary,m=!1;if(this.ended)return!1;l=i===~~i?i:!0===i?n.Z_FINISH:n.Z_NO_FLUSH,"string"==typeof a?d.input=r.binstring2buf(a):"[object ArrayBuffer]"===o.call(a)?d.input=new Uint8Array(a):d.input=a,d.next_in=0,d.avail_in=d.input.length;do{if(0===d.avail_out&&(d.output=new t.Buf8(f),d.next_out=0,d.avail_out=f),(s=e.inflate(d,n.Z_NO_FLUSH))===n.Z_NEED_DICT&&p&&(s=e.inflateSetDictionary(this.strm,p)),s===n.Z_BUF_ERROR&&!0===m&&(s=n.Z_OK,m=!1),s!==n.Z_STREAM_END&&s!==n.Z_OK)return this.onEnd(s),this.ended=!0,!1;d.next_out&&(0!==d.avail_out&&s!==n.Z_STREAM_END&&(0!==d.avail_in||l!==n.Z_FINISH&&l!==n.Z_SYNC_FLUSH)||("string"===this.options.to?(u=r.utf8border(d.output,d.next_out),c=d.next_out-u,h=r.buf2string(d.output,u),d.next_out=c,d.avail_out=f-c,c&&t.arraySet(d.output,d.output,u,c,0),this.onData(h)):this.onData(t.shrinkBuf(d.output,d.next_out)))),0===d.avail_in&&0===d.avail_out&&(m=!0)}while((d.avail_in>0||0===d.avail_out)&&s!==n.Z_STREAM_END);return s===n.Z_STREAM_END&&(l=n.Z_FINISH),l===n.Z_FINISH?(s=e.inflateEnd(this.strm),this.onEnd(s),this.ended=!0,s===n.Z_OK):l!==n.Z_SYNC_FLUSH||(this.onEnd(n.Z_OK),d.avail_out=0,!0)},l.prototype.onData=function(e){this.chunks.push(e)},l.prototype.onEnd=function(e){e===n.Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=t.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},Ht.Inflate=l,Ht.inflate=u,Ht.inflateRaw=function(e,t){return(t=t||{}).raw=!0,u(e,t)},Ht.ungzip=u,Ht}function Xt(){if(jt)return Lt;jt=1;var e=it().assign,t=function(){if(Ct)return pt;Ct=1;var e=_t(),t=it(),r=Pt(),n=wt(),a=Tt(),i=Object.prototype.toString,s=0,o=-1,l=0,u=8;function c(h){if(!(this instanceof c))return new c(h);this.options=t.assign({level:o,method:u,chunkSize:16384,windowBits:15,memLevel:8,strategy:l,to:""},h||{});var d=this.options;d.raw&&d.windowBits>0?d.windowBits=-d.windowBits:d.gzip&&d.windowBits>0&&d.windowBits<16&&(d.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new a,this.strm.avail_out=0;var f=e.deflateInit2(this.strm,d.level,d.method,d.windowBits,d.memLevel,d.strategy);if(f!==s)throw new Error(n[f]);if(d.header&&e.deflateSetHeader(this.strm,d.header),d.dictionary){var p;if(p="string"==typeof d.dictionary?r.string2buf(d.dictionary):"[object ArrayBuffer]"===i.call(d.dictionary)?new Uint8Array(d.dictionary):d.dictionary,(f=e.deflateSetDictionary(this.strm,p))!==s)throw new Error(n[f]);this._dict_set=!0}}function h(e,t){var r=new c(t);if(r.push(e,!0),r.err)throw r.msg||n[r.err];return r.result}return c.prototype.push=function(n,a){var o,l,u=this.strm,c=this.options.chunkSize;if(this.ended)return!1;l=a===~~a?a:!0===a?4:0,"string"==typeof n?u.input=r.string2buf(n):"[object ArrayBuffer]"===i.call(n)?u.input=new Uint8Array(n):u.input=n,u.next_in=0,u.avail_in=u.input.length;do{if(0===u.avail_out&&(u.output=new t.Buf8(c),u.next_out=0,u.avail_out=c),1!==(o=e.deflate(u,l))&&o!==s)return this.onEnd(o),this.ended=!0,!1;0!==u.avail_out&&(0!==u.avail_in||4!==l&&2!==l)||("string"===this.options.to?this.onData(r.buf2binstring(t.shrinkBuf(u.output,u.next_out))):this.onData(t.shrinkBuf(u.output,u.next_out)))}while((u.avail_in>0||0===u.avail_out)&&1!==o);return 4===l?(o=e.deflateEnd(this.strm),this.onEnd(o),this.ended=!0,o===s):2!==l||(this.onEnd(s),u.avail_out=0,!0)},c.prototype.onData=function(e){this.chunks.push(e)},c.prototype.onEnd=function(e){e===s&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=t.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg},pt.Deflate=c,pt.deflate=h,pt.deflateRaw=function(e,t){return(t=t||{}).raw=!0,h(e,t)},pt.gzip=function(e,t){return(t=t||{}).gzip=!0,h(e,t)},pt}(),r={};return e(r,t,qt(),Vt()),Lt=r}function Kt(){if(Wt)return rt;Wt=1;var e=Be();return rt.STORE={magic:"\0\0",compressWorker:function(){return new e("STORE compression")},uncompressWorker:function(){return new e("STORE decompression")}},rt.DEFLATE=function(){if(Ut)return nt;Ut=1;var e="undefined"!=typeof Uint8Array&&"undefined"!=typeof Uint16Array&&"undefined"!=typeof Uint32Array,t=Xt(),r=Ne(),n=Be(),a=e?"uint8array":"array";function i(e,t){n.call(this,"FlateWorker/"+e),this._pako=null,this._pakoAction=e,this._pakoOptions=t,this.meta={}}return nt.magic="\b\0",r.inherits(i,n),i.prototype.processChunk=function(e){this.meta=e.meta,null===this._pako&&this._createPako(),this._pako.push(r.transformTo(a,e.data),!1)},i.prototype.flush=function(){n.prototype.flush.call(this),null===this._pako&&this._createPako(),this._pako.push([],!0)},i.prototype.cleanUp=function(){n.prototype.cleanUp.call(this),this._pako=null},i.prototype._createPako=function(){this._pako=new t[this._pakoAction]({raw:!0,level:this._pakoOptions.level||-1});var e=this;this._pako.onData=function(t){e.push({data:t,meta:e.meta})}},nt.compressWorker=function(e){return new i("Deflate",e)},nt.uncompressWorker=function(){return new i("Inflate",{})},nt}(),rt}var Gt,Jt,Yt,Qt,er,tr,rr,nr,ar,ir,sr,or,lr,ur,cr,hr,dr,fr,pr,mr,gr,br,yr,vr,wr,_r,kr,Sr,xr={};function Cr(){return Gt||(Gt=1,xr.LOCAL_FILE_HEADER="PK",xr.CENTRAL_FILE_HEADER="PK",xr.CENTRAL_DIRECTORY_END="PK",xr.ZIP64_CENTRAL_DIRECTORY_LOCATOR="PK",xr.ZIP64_CENTRAL_DIRECTORY_END="PK",xr.DATA_DESCRIPTOR="PK\b"),xr}function Ar(){if(Yt)return Jt;Yt=1;var e=Ne(),t=Be(),r=Me(),n=Je(),a=Cr(),i=function(e,t){var r,n="";for(r=0;r<t;r++)n+=String.fromCharCode(255&e),e>>>=8;return n},s=function(t,s,o,l,u,c){var h,d,f=t.file,p=t.compression,m=c!==r.utf8encode,g=e.transformTo("string",c(f.name)),b=e.transformTo("string",r.utf8encode(f.name)),y=f.comment,v=e.transformTo("string",c(y)),w=e.transformTo("string",r.utf8encode(y)),_=b.length!==f.name.length,k=w.length!==y.length,S="",x="",C="",A=f.dir,P=f.date,T={crc32:0,compressedSize:0,uncompressedSize:0};s&&!o||(T.crc32=t.crc32,T.compressedSize=t.compressedSize,T.uncompressedSize=t.uncompressedSize);var E=0;s&&(E|=8),m||!_&&!k||(E|=2048);var D,N,B,M=0,R=0;A&&(M|=16),"UNIX"===u?(R=798,M|=(D=f.unixPermissions,N=A,B=D,D||(B=N?16893:33204),(65535&B)<<16)):(R=20,M|=63&(f.dosPermissions||0)),h=P.getUTCHours(),h<<=6,h|=P.getUTCMinutes(),h<<=5,h|=P.getUTCSeconds()/2,d=P.getUTCFullYear()-1980,d<<=4,d|=P.getUTCMonth()+1,d<<=5,d|=P.getUTCDate(),_&&(x=i(1,1)+i(n(g),4)+b,S+="up"+i(x.length,2)+x),k&&(C=i(1,1)+i(n(v),4)+w,S+="uc"+i(C.length,2)+C);var O="";return O+="\n\0",O+=i(E,2),O+=p.magic,O+=i(h,2),O+=i(d,2),O+=i(T.crc32,4),O+=i(T.compressedSize,4),O+=i(T.uncompressedSize,4),O+=i(g.length,2),O+=i(S.length,2),{fileRecord:a.LOCAL_FILE_HEADER+O+g+S,dirRecord:a.CENTRAL_FILE_HEADER+i(R,2)+O+i(v.length,2)+"\0\0\0\0"+i(M,4)+i(l,4)+g+S+v}},o=function(e){return a.DATA_DESCRIPTOR+i(e.crc32,4)+i(e.compressedSize,4)+i(e.uncompressedSize,4)};function l(e,r,n,a){t.call(this,"ZipFileWorker"),this.bytesWritten=0,this.zipComment=r,this.zipPlatform=n,this.encodeFileName=a,this.streamFiles=e,this.accumulate=!1,this.contentBuffer=[],this.dirRecords=[],this.currentSourceOffset=0,this.entriesCount=0,this.currentFile=null,this._sources=[]}return e.inherits(l,t),l.prototype.push=function(e){var r=e.meta.percent||0,n=this.entriesCount,a=this._sources.length;this.accumulate?this.contentBuffer.push(e):(this.bytesWritten+=e.data.length,t.prototype.push.call(this,{data:e.data,meta:{currentFile:this.currentFile,percent:n?(r+100*(n-a-1))/n:100}}))},l.prototype.openedSource=function(e){this.currentSourceOffset=this.bytesWritten,this.currentFile=e.file.name;var t=this.streamFiles&&!e.file.dir;if(t){var r=s(e,t,!1,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);this.push({data:r.fileRecord,meta:{percent:0}})}else this.accumulate=!0},l.prototype.closedSource=function(e){this.accumulate=!1;var t=this.streamFiles&&!e.file.dir,r=s(e,t,!0,this.currentSourceOffset,this.zipPlatform,this.encodeFileName);if(this.dirRecords.push(r.dirRecord),t)this.push({data:o(e),meta:{percent:100}});else for(this.push({data:r.fileRecord,meta:{percent:0}});this.contentBuffer.length;)this.push(this.contentBuffer.shift());this.currentFile=null},l.prototype.flush=function(){for(var t=this.bytesWritten,r=0;r<this.dirRecords.length;r++)this.push({data:this.dirRecords[r],meta:{percent:100}});var n=this.bytesWritten-t,s=function(t,r,n,s,o){var l=e.transformTo("string",o(s));return a.CENTRAL_DIRECTORY_END+"\0\0\0\0"+i(t,2)+i(t,2)+i(r,4)+i(n,4)+i(l.length,2)+l}(this.dirRecords.length,n,t,this.zipComment,this.encodeFileName);this.push({data:s,meta:{percent:100}})},l.prototype.prepareNextSource=function(){this.previous=this._sources.shift(),this.openedSource(this.previous.streamInfo),this.isPaused?this.previous.pause():this.previous.resume()},l.prototype.registerPrevious=function(e){this._sources.push(e);var t=this;return e.on("data",(function(e){t.processChunk(e)})),e.on("end",(function(){t.closedSource(t.previous.streamInfo),t._sources.length?t.prepareNextSource():t.end()})),e.on("error",(function(e){t.error(e)})),this},l.prototype.resume=function(){return!!t.prototype.resume.call(this)&&(!this.previous&&this._sources.length?(this.prepareNextSource(),!0):this.previous||this._sources.length||this.generatedError?void 0:(this.end(),!0))},l.prototype.error=function(e){var r=this._sources;if(!t.prototype.error.call(this,e))return!1;for(var n=0;n<r.length;n++)try{r[n].error(e)}catch(e){}return!0},l.prototype.lock=function(){t.prototype.lock.call(this);for(var e=this._sources,r=0;r<e.length;r++)e[r].lock()},Jt=l}function Pr(){if(Qt)return tt;Qt=1;var e=Kt(),t=Ar();return tt.generateWorker=function(r,n,a){var i=new t(n.streamFiles,a,n.platform,n.encodeFileName),s=0;try{r.forEach((function(t,r){s++;var a=function(t,r){var n=t||r,a=e[n];if(!a)throw new Error(n+" is not a valid compression method !");return a}(r.options.compression,n.compression),o=r.options.compressionOptions||n.compressionOptions||{},l=r.dir,u=r.date;r._compressWorker(a,o).withStreamInfo("file",{name:t,dir:l,date:u,comment:r.comment||"",unixPermissions:r.unixPermissions,dosPermissions:r.dosPermissions}).pipe(i)})),i.entriesCount=s}catch(e){i.error(e)}return i},tt}function Tr(){if(nr)return rr;nr=1;var e=Me(),t=Ne(),r=Be(),n=Re(),a=Ke(),i=Qe(),s=function(){if(qe)return Ve;qe=1;var e=Re(),t=Ge(),r=Me(),n=Qe(),a=Be(),i=function(e,t,r){this.name=e,this.dir=r.dir,this.date=r.date,this.comment=r.comment,this.unixPermissions=r.unixPermissions,this.dosPermissions=r.dosPermissions,this._data=t,this._dataBinary=r.binary,this.options={compression:r.compression,compressionOptions:r.compressionOptions}};i.prototype={internalStream:function(t){var n=null,i="string";try{if(!t)throw new Error("No output type specified.");var s="string"===(i=t.toLowerCase())||"text"===i;"binarystring"!==i&&"text"!==i||(i="string"),n=this._decompressWorker();var o=!this._dataBinary;o&&!s&&(n=n.pipe(new r.Utf8EncodeWorker)),!o&&s&&(n=n.pipe(new r.Utf8DecodeWorker))}catch(e){(n=new a("error")).error(e)}return new e(n,i,"")},async:function(e,t){return this.internalStream(e).accumulate(t)},nodeStream:function(e,t){return this.internalStream(e||"nodebuffer").toNodejsStream(t)},_compressWorker:function(e,t){if(this._data instanceof n&&this._data.compression.magic===e.magic)return this._data.getCompressedWorker();var a=this._decompressWorker();return this._dataBinary||(a=a.pipe(new r.Utf8EncodeWorker)),n.createWorkerFrom(a,e,t)},_decompressWorker:function(){return this._data instanceof n?this._data.getContentWorker():this._data instanceof a?this._data:new t(this._data)}};for(var s=["asText","asBinary","asNodeBuffer","asUint8Array","asArrayBuffer"],o=function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},l=0;l<s.length;l++)i.prototype[s[l]]=o;return Ve=i}(),o=Pr(),l=me(),u=function(){if(tr)return er;tr=1;var e=Ne(),t=Be();function r(e,r){t.call(this,"Nodejs stream input adapter for "+e),this._upstreamEnded=!1,this._bindStream(r)}return e.inherits(r,t),r.prototype._bindStream=function(e){var t=this;this._stream=e,e.pause(),e.on("data",(function(e){t.push({data:e,meta:{percent:0}})})).on("error",(function(e){t.isPaused?this.generatedError=e:t.error(e)})).on("end",(function(){t.isPaused?t._upstreamEnded=!0:t.end()}))},r.prototype.pause=function(){return!!t.prototype.pause.call(this)&&(this._stream.pause(),!0)},r.prototype.resume=function(){return!!t.prototype.resume.call(this)&&(this._upstreamEnded?this.end():this._stream.resume(),!0)},er=r}(),c=function(e,n,o){var c,p=t.getTypeOf(n),m=t.extend(o||{},a);m.date=m.date||new Date,null!==m.compression&&(m.compression=m.compression.toUpperCase()),"string"==typeof m.unixPermissions&&(m.unixPermissions=parseInt(m.unixPermissions,8)),m.unixPermissions&&16384&m.unixPermissions&&(m.dir=!0),m.dosPermissions&&16&m.dosPermissions&&(m.dir=!0),m.dir&&(e=d(e)),m.createFolders&&(c=h(e))&&f.call(this,c,!0);var g="string"===p&&!1===m.binary&&!1===m.base64;o&&void 0!==o.binary||(m.binary=!g),(n instanceof i&&0===n.uncompressedSize||m.dir||!n||0===n.length)&&(m.base64=!1,m.binary=!0,n="",m.compression="STORE",p="string");var b=null;b=n instanceof i||n instanceof r?n:l.isNode&&l.isStream(n)?new u(e,n):t.prepareContent(e,n,m.binary,m.optimizedBinaryString,m.base64);var y=new s(e,b,m);this.files[e]=y},h=function(e){"/"===e.slice(-1)&&(e=e.substring(0,e.length-1));var t=e.lastIndexOf("/");return t>0?e.substring(0,t):""},d=function(e){return"/"!==e.slice(-1)&&(e+="/"),e},f=function(e,t){return t=void 0!==t?t:a.createFolders,e=d(e),this.files[e]||c.call(this,e,null,{dir:!0,createFolders:t}),this.files[e]};function p(e){return"[object RegExp]"===Object.prototype.toString.call(e)}var m={load:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},forEach:function(e){var t,r,n;for(t in this.files)n=this.files[t],(r=t.slice(this.root.length,t.length))&&t.slice(0,this.root.length)===this.root&&e(r,n)},filter:function(e){var t=[];return this.forEach((function(r,n){e(r,n)&&t.push(n)})),t},file:function(e,t,r){if(1===arguments.length){if(p(e)){var n=e;return this.filter((function(e,t){return!t.dir&&n.test(e)}))}var a=this.files[this.root+e];return a&&!a.dir?a:null}return e=this.root+e,c.call(this,e,t,r),this},folder:function(e){if(!e)return this;if(p(e))return this.filter((function(t,r){return r.dir&&e.test(t)}));var t=this.root+e,r=f.call(this,t),n=this.clone();return n.root=r.name,n},remove:function(e){e=this.root+e;var t=this.files[e];if(t||("/"!==e.slice(-1)&&(e+="/"),t=this.files[e]),t&&!t.dir)delete this.files[e];else for(var r=this.filter((function(t,r){return r.name.slice(0,e.length)===e})),n=0;n<r.length;n++)delete this.files[r[n].name];return this},generate:function(){throw new Error("This method has been removed in JSZip 3.0, please check the upgrade guide.")},generateInternalStream:function(a){var i,s={};try{if((s=t.extend(a||{},{streamFiles:!1,compression:"STORE",compressionOptions:null,type:"",platform:"DOS",comment:null,mimeType:"application/zip",encodeFileName:e.utf8encode})).type=s.type.toLowerCase(),s.compression=s.compression.toUpperCase(),"binarystring"===s.type&&(s.type="string"),!s.type)throw new Error("No output type specified.");t.checkSupport(s.type),"darwin"!==s.platform&&"freebsd"!==s.platform&&"linux"!==s.platform&&"sunos"!==s.platform||(s.platform="UNIX"),"win32"===s.platform&&(s.platform="DOS");var l=s.comment||this.comment||"";i=o.generateWorker(this,s,l)}catch(e){(i=new r("error")).error(e)}return new n(i,s.type||"string",s.mimeType)},generateAsync:function(e,t){return this.generateInternalStream(e).accumulate(t)},generateNodeStream:function(e,t){return(e=e||{}).type||(e.type="nodebuffer"),this.generateInternalStream(e).toNodejsStream(t)}};return rr=m}function Er(){if(ir)return ar;ir=1;var e=Ne();function t(e){this.data=e,this.length=e.length,this.index=0,this.zero=0}return t.prototype={checkOffset:function(e){this.checkIndex(this.index+e)},checkIndex:function(e){if(this.length<this.zero+e||e<0)throw new Error("End of data reached (data length = "+this.length+", asked index = "+e+"). Corrupted zip ?")},setIndex:function(e){this.checkIndex(e),this.index=e},skip:function(e){this.setIndex(this.index+e)},byteAt:function(){},readInt:function(e){var t,r=0;for(this.checkOffset(e),t=this.index+e-1;t>=this.index;t--)r=(r<<8)+this.byteAt(t);return this.index+=e,r},readString:function(t){return e.transformTo("string",this.readData(t))},readData:function(){},lastIndexOfSignature:function(){},readAndCheckSignature:function(){},readDate:function(){var e=this.readInt(4);return new Date(Date.UTC(1980+(e>>25&127),(e>>21&15)-1,e>>16&31,e>>11&31,e>>5&63,(31&e)<<1))}},ar=t}function Dr(){if(or)return sr;or=1;var e=Er();function t(t){e.call(this,t);for(var r=0;r<this.data.length;r++)t[r]=255&t[r]}return Ne().inherits(t,e),t.prototype.byteAt=function(e){return this.data[this.zero+e]},t.prototype.lastIndexOfSignature=function(e){for(var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),a=e.charCodeAt(3),i=this.length-4;i>=0;--i)if(this.data[i]===t&&this.data[i+1]===r&&this.data[i+2]===n&&this.data[i+3]===a)return i-this.zero;return-1},t.prototype.readAndCheckSignature=function(e){var t=e.charCodeAt(0),r=e.charCodeAt(1),n=e.charCodeAt(2),a=e.charCodeAt(3),i=this.readData(4);return t===i[0]&&r===i[1]&&n===i[2]&&a===i[3]},t.prototype.readData=function(e){if(this.checkOffset(e),0===e)return[];var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},sr=t}function Nr(){if(hr)return cr;hr=1;var e=Dr();function t(t){e.call(this,t)}return Ne().inherits(t,e),t.prototype.readData=function(e){if(this.checkOffset(e),0===e)return new Uint8Array(0);var t=this.data.subarray(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},cr=t}function Br(){if(mr)return pr;mr=1;var e=Ne(),t=ne(),r=Dr(),n=function(){if(ur)return lr;ur=1;var e=Er();function t(t){e.call(this,t)}return Ne().inherits(t,e),t.prototype.byteAt=function(e){return this.data.charCodeAt(this.zero+e)},t.prototype.lastIndexOfSignature=function(e){return this.data.lastIndexOf(e)-this.zero},t.prototype.readAndCheckSignature=function(e){return e===this.readData(4)},t.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},lr=t}(),a=function(){if(fr)return dr;fr=1;var e=Nr();function t(t){e.call(this,t)}return Ne().inherits(t,e),t.prototype.readData=function(e){this.checkOffset(e);var t=this.data.slice(this.zero+this.index,this.zero+this.index+e);return this.index+=e,t},dr=t}(),i=Nr();return pr=function(s){var o=e.getTypeOf(s);return e.checkSupport(o),"string"!==o||t.uint8array?"nodebuffer"===o?new a(s):t.uint8array?new i(e.transformTo("uint8array",s)):new r(e.transformTo("array",s)):new n(s)}}function Mr(){if(br)return gr;br=1;var e=Br(),t=Ne(),r=Qe(),n=Je(),a=Me(),i=Kt(),s=ne();function o(e,t){this.options=e,this.loadOptions=t}return o.prototype={isEncrypted:function(){return 1==(1&this.bitFlag)},useUTF8:function(){return 2048==(2048&this.bitFlag)},readLocalPart:function(e){var n,a;if(e.skip(22),this.fileNameLength=e.readInt(2),a=e.readInt(2),this.fileName=e.readData(this.fileNameLength),e.skip(a),-1===this.compressedSize||-1===this.uncompressedSize)throw new Error("Bug or corrupted zip : didn't get enough information from the central directory (compressedSize === -1 || uncompressedSize === -1)");if(null===(n=function(e){for(var t in i)if(Object.prototype.hasOwnProperty.call(i,t)&&i[t].magic===e)return i[t];return null}(this.compressionMethod)))throw new Error("Corrupted zip : compression "+t.pretty(this.compressionMethod)+" unknown (inner file : "+t.transformTo("string",this.fileName)+")");this.decompressed=new r(this.compressedSize,this.uncompressedSize,this.crc32,n,e.readData(this.compressedSize))},readCentralPart:function(e){this.versionMadeBy=e.readInt(2),e.skip(2),this.bitFlag=e.readInt(2),this.compressionMethod=e.readString(2),this.date=e.readDate(),this.crc32=e.readInt(4),this.compressedSize=e.readInt(4),this.uncompressedSize=e.readInt(4);var t=e.readInt(2);if(this.extraFieldsLength=e.readInt(2),this.fileCommentLength=e.readInt(2),this.diskNumberStart=e.readInt(2),this.internalFileAttributes=e.readInt(2),this.externalFileAttributes=e.readInt(4),this.localHeaderOffset=e.readInt(4),this.isEncrypted())throw new Error("Encrypted zip are not supported");e.skip(t),this.readExtraFields(e),this.parseZIP64ExtraField(e),this.fileComment=e.readData(this.fileCommentLength)},processAttributes:function(){this.unixPermissions=null,this.dosPermissions=null;var e=this.versionMadeBy>>8;this.dir=!!(16&this.externalFileAttributes),0===e&&(this.dosPermissions=63&this.externalFileAttributes),3===e&&(this.unixPermissions=this.externalFileAttributes>>16&65535),this.dir||"/"!==this.fileNameStr.slice(-1)||(this.dir=!0)},parseZIP64ExtraField:function(){if(this.extraFields[1]){var r=e(this.extraFields[1].value);this.uncompressedSize===t.MAX_VALUE_32BITS&&(this.uncompressedSize=r.readInt(8)),this.compressedSize===t.MAX_VALUE_32BITS&&(this.compressedSize=r.readInt(8)),this.localHeaderOffset===t.MAX_VALUE_32BITS&&(this.localHeaderOffset=r.readInt(8)),this.diskNumberStart===t.MAX_VALUE_32BITS&&(this.diskNumberStart=r.readInt(4))}},readExtraFields:function(e){var t,r,n,a=e.index+this.extraFieldsLength;for(this.extraFields||(this.extraFields={});e.index+4<a;)t=e.readInt(2),r=e.readInt(2),n=e.readData(r),this.extraFields[t]={id:t,length:r,value:n};e.setIndex(a)},handleUTF8:function(){var e=s.uint8array?"uint8array":"array";if(this.useUTF8())this.fileNameStr=a.utf8decode(this.fileName),this.fileCommentStr=a.utf8decode(this.fileComment);else{var r=this.findExtraFieldUnicodePath();if(null!==r)this.fileNameStr=r;else{var n=t.transformTo(e,this.fileName);this.fileNameStr=this.loadOptions.decodeFileName(n)}var i=this.findExtraFieldUnicodeComment();if(null!==i)this.fileCommentStr=i;else{var o=t.transformTo(e,this.fileComment);this.fileCommentStr=this.loadOptions.decodeFileName(o)}}},findExtraFieldUnicodePath:function(){var t=this.extraFields[28789];if(t){var r=e(t.value);return 1!==r.readInt(1)||n(this.fileName)!==r.readInt(4)?null:a.utf8decode(r.readData(t.length-5))}return null},findExtraFieldUnicodeComment:function(){var t=this.extraFields[25461];if(t){var r=e(t.value);return 1!==r.readInt(1)||n(this.fileComment)!==r.readInt(4)?null:a.utf8decode(r.readData(t.length-5))}return null}},gr=o}function Rr(){if(_r)return wr;_r=1;var e=Ne(),t=be(),r=Me(),n=function(){if(vr)return yr;vr=1;var e=Br(),t=Ne(),r=Cr(),n=Mr(),a=ne();function i(e){this.files=[],this.loadOptions=e}return i.prototype={checkSignature:function(e){if(!this.reader.readAndCheckSignature(e)){this.reader.index-=4;var r=this.reader.readString(4);throw new Error("Corrupted zip or bug: unexpected signature ("+t.pretty(r)+", expected "+t.pretty(e)+")")}},isSignature:function(e,t){var r=this.reader.index;this.reader.setIndex(e);var n=this.reader.readString(4)===t;return this.reader.setIndex(r),n},readBlockEndOfCentral:function(){this.diskNumber=this.reader.readInt(2),this.diskWithCentralDirStart=this.reader.readInt(2),this.centralDirRecordsOnThisDisk=this.reader.readInt(2),this.centralDirRecords=this.reader.readInt(2),this.centralDirSize=this.reader.readInt(4),this.centralDirOffset=this.reader.readInt(4),this.zipCommentLength=this.reader.readInt(2);var e=this.reader.readData(this.zipCommentLength),r=a.uint8array?"uint8array":"array",n=t.transformTo(r,e);this.zipComment=this.loadOptions.decodeFileName(n)},readBlockZip64EndOfCentral:function(){this.zip64EndOfCentralSize=this.reader.readInt(8),this.reader.skip(4),this.diskNumber=this.reader.readInt(4),this.diskWithCentralDirStart=this.reader.readInt(4),this.centralDirRecordsOnThisDisk=this.reader.readInt(8),this.centralDirRecords=this.reader.readInt(8),this.centralDirSize=this.reader.readInt(8),this.centralDirOffset=this.reader.readInt(8),this.zip64ExtensibleData={};for(var e,t,r,n=this.zip64EndOfCentralSize-44;0<n;)e=this.reader.readInt(2),t=this.reader.readInt(4),r=this.reader.readData(t),this.zip64ExtensibleData[e]={id:e,length:t,value:r}},readBlockZip64EndOfCentralLocator:function(){if(this.diskWithZip64CentralDirStart=this.reader.readInt(4),this.relativeOffsetEndOfZip64CentralDir=this.reader.readInt(8),this.disksCount=this.reader.readInt(4),this.disksCount>1)throw new Error("Multi-volumes zip are not supported")},readLocalFiles:function(){var e,t;for(e=0;e<this.files.length;e++)t=this.files[e],this.reader.setIndex(t.localHeaderOffset),this.checkSignature(r.LOCAL_FILE_HEADER),t.readLocalPart(this.reader),t.handleUTF8(),t.processAttributes()},readCentralDir:function(){var e;for(this.reader.setIndex(this.centralDirOffset);this.reader.readAndCheckSignature(r.CENTRAL_FILE_HEADER);)(e=new n({zip64:this.zip64},this.loadOptions)).readCentralPart(this.reader),this.files.push(e);if(this.centralDirRecords!==this.files.length&&0!==this.centralDirRecords&&0===this.files.length)throw new Error("Corrupted zip or bug: expected "+this.centralDirRecords+" records in central dir, got "+this.files.length)},readEndOfCentral:function(){var e=this.reader.lastIndexOfSignature(r.CENTRAL_DIRECTORY_END);if(e<0)throw this.isSignature(0,r.LOCAL_FILE_HEADER)?new Error("Corrupted zip: can't find end of central directory"):new Error("Can't find end of central directory : is this a zip file ? If it is, see https://stuk.github.io/jszip/documentation/howto/read_zip.html");this.reader.setIndex(e);var n=e;if(this.checkSignature(r.CENTRAL_DIRECTORY_END),this.readBlockEndOfCentral(),this.diskNumber===t.MAX_VALUE_16BITS||this.diskWithCentralDirStart===t.MAX_VALUE_16BITS||this.centralDirRecordsOnThisDisk===t.MAX_VALUE_16BITS||this.centralDirRecords===t.MAX_VALUE_16BITS||this.centralDirSize===t.MAX_VALUE_32BITS||this.centralDirOffset===t.MAX_VALUE_32BITS){if(this.zip64=!0,(e=this.reader.lastIndexOfSignature(r.ZIP64_CENTRAL_DIRECTORY_LOCATOR))<0)throw new Error("Corrupted zip: can't find the ZIP64 end of central directory locator");if(this.reader.setIndex(e),this.checkSignature(r.ZIP64_CENTRAL_DIRECTORY_LOCATOR),this.readBlockZip64EndOfCentralLocator(),!this.isSignature(this.relativeOffsetEndOfZip64CentralDir,r.ZIP64_CENTRAL_DIRECTORY_END)&&(this.relativeOffsetEndOfZip64CentralDir=this.reader.lastIndexOfSignature(r.ZIP64_CENTRAL_DIRECTORY_END),this.relativeOffsetEndOfZip64CentralDir<0))throw new Error("Corrupted zip: can't find the ZIP64 end of central directory");this.reader.setIndex(this.relativeOffsetEndOfZip64CentralDir),this.checkSignature(r.ZIP64_CENTRAL_DIRECTORY_END),this.readBlockZip64EndOfCentral()}var a=this.centralDirOffset+this.centralDirSize;this.zip64&&(a+=20,a+=12+this.zip64EndOfCentralSize);var i=n-a;if(i>0)this.isSignature(n,r.CENTRAL_FILE_HEADER)||(this.reader.zero=i);else if(i<0)throw new Error("Corrupted zip: missing "+Math.abs(i)+" bytes.")},prepareReader:function(t){this.reader=e(t)},load:function(e){this.prepareReader(e),this.readEndOfCentral(),this.readCentralDir(),this.readLocalFiles()}},yr=i}(),a=Ye(),i=me();function s(e){return new t.Promise((function(t,r){var n=e.decompressed.getContentWorker().pipe(new a);n.on("error",(function(e){r(e)})).on("end",(function(){n.streamInfo.crc32!==e.decompressed.crc32?r(new Error("Corrupted zip : CRC32 mismatch")):t()})).resume()}))}return wr=function(a,o){var l=this;return o=e.extend(o||{},{base64:!1,checkCRC32:!1,optimizedBinaryString:!1,createFolders:!1,decodeFileName:r.utf8decode}),i.isNode&&i.isStream(a)?t.Promise.reject(new Error("JSZip can't accept a stream when loading a zip file.")):e.prepareContent("the loaded zip file",a,!0,o.optimizedBinaryString,o.base64).then((function(e){var t=new n(o);return t.load(e),t})).then((function(e){var r=[t.Promise.resolve(e)],n=e.files;if(o.checkCRC32)for(var a=0;a<n.length;a++)r.push(s(n[a]));return t.Promise.all(r)})).then((function(t){for(var r=t.shift(),n=r.files,a=0;a<n.length;a++){var i=n[a],s=i.fileNameStr,u=e.resolve(i.fileNameStr);l.file(u,i.decompressed,{binary:!0,optimizedBinaryString:!0,date:i.date,dir:i.dir,comment:i.fileCommentStr.length?i.fileCommentStr:null,unixPermissions:i.unixPermissions,dosPermissions:i.dosPermissions,createFolders:o.createFolders}),i.dir||(l.file(u).unsafeOriginalName=s)}return r.zipComment.length&&(l.comment=r.zipComment),l}))},wr}!function(e,t){var r;globalThis,r=e=>(()=>{var t={438:(e,t,r)=>{r.r(t),r.d(t,{default:()=>d});var n=r(537),a=r.n(n),i=r(645),s=r.n(i),o=r(806),l=r.n(o),u=new URL(r(583),r.b),c=s()(a()),h=l()(u);c.push([e.id,`@namespace "http://www.w3.org/1998/Math/MathML";math{display:inline-block;line-height:initial}mfrac{display:inline-block;vertical-align:-50%;text-align:center}mfrac>:first-child{border-bottom:solid thin currentColor}mfrac>*{display:block}msub>:nth-child(2){font-size:smaller;vertical-align:sub}msup>:nth-child(2){font-size:smaller;vertical-align:super}munder,mover,munderover{display:inline-flex;flex-flow:column nowrap;vertical-align:middle;text-align:center}munder>:not(:first-child),mover>:not(:first-child),munderover>:not(:first-child){font-size:smaller}munderover>:last-child{order:-1}mroot,msqrt{position:relative;display:inline-block;border-top:solid thin currentColor;margin-top:.5px;vertical-align:middle;margin-left:1ch}mroot:before,msqrt:before{content:"";display:inline-block;position:absolute;width:1ch;left:-1ch;top:-1px;bottom:0;background-image:url(${h})}`,"",{version:3,sources:["webpack://./src/mathml.scss"],names:[],mappings:"AAAA,+CAAA,CAEA,KACI,oBAAA,CACA,mBAAA,CAGJ,MACI,oBAAA,CACA,mBAAA,CACA,iBAAA,CAEA,mBACI,qCAAA,CAGJ,QACI,aAAA,CAKJ,mBACI,iBAAA,CACA,kBAAA,CAKJ,mBACI,iBAAA,CACA,oBAAA,CAIR,wBACI,mBAAA,CACA,uBAAA,CACA,qBAAA,CACA,iBAAA,CAEA,iFACI,iBAAA,CAKJ,uBAAA,QAAA,CAGJ,YACI,iBAAA,CACA,oBAAA,CACA,kCAAA,CACA,eAAA,CACA,qBAAA,CACA,eAAA,CAEA,0BACI,UAAA,CACA,oBAAA,CACA,iBAAA,CACA,SAAA,CACA,SAAA,CACA,QAAA,CACA,QAAA,CACA,wDAAA",sourcesContent:["@namespace \"http://www.w3.org/1998/Math/MathML\";\r\n\r\nmath {\r\n    display: inline-block;\r\n    line-height: initial;\r\n}\r\n\r\nmfrac {\r\n    display: inline-block;\r\n    vertical-align: -50%;\r\n    text-align: center;\r\n\r\n    &>:first-child {\r\n        border-bottom: solid thin currentColor;\r\n    }\r\n\r\n    &>* {\r\n        display: block;\r\n    }\r\n}\r\n\r\nmsub {\r\n    &>:nth-child(2) {\r\n        font-size: smaller;\r\n        vertical-align: sub;\r\n    }\r\n}\r\n\r\nmsup {\r\n    &>:nth-child(2) {\r\n        font-size: smaller;\r\n        vertical-align: super;\r\n    }\r\n}\r\n\r\nmunder, mover, munderover {\r\n    display: inline-flex;\r\n    flex-flow: column nowrap;\r\n    vertical-align: middle;\r\n    text-align: center;\r\n\r\n    &>:not(:first-child) {\r\n        font-size: smaller;\r\n    }\r\n}\r\n\r\nmunderover {\r\n    &>:last-child { order: -1; }\r\n}\r\n\r\nmroot, msqrt {\r\n    position: relative;\r\n    display: inline-block;\r\n    border-top: solid thin currentColor;  \r\n    margin-top: 0.5px;\r\n    vertical-align: middle;  \r\n    margin-left: 1ch; \r\n\r\n    &:before {\r\n        content: \"\";\r\n        display: inline-block;\r\n        position: absolute;\r\n        width: 1ch;\r\n        left: -1ch;\r\n        top: -1px;\r\n        bottom: 0;\r\n        background-image: url(\"data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 100' preserveAspectRatio='none'%3E%3Cpath d='m0,75 l5,0 l5,25 l10,-100' stroke='black' fill='none' vector-effect='non-scaling-stroke'/%3E%3C/svg%3E\");\r\n    }\r\n}"],sourceRoot:""}]);const d=c.toString()},645:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var r="",n=void 0!==t[5];return t[4]&&(r+="@supports (".concat(t[4],") {")),t[2]&&(r+="@media ".concat(t[2]," {")),n&&(r+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),r+=e(t),n&&(r+="}"),t[2]&&(r+="}"),t[4]&&(r+="}"),r})).join("")},t.i=function(e,r,n,a,i){"string"==typeof e&&(e=[[null,e,void 0]]);var s={};if(n)for(var o=0;o<this.length;o++){var l=this[o][0];null!=l&&(s[l]=!0)}for(var u=0;u<e.length;u++){var c=[].concat(e[u]);n&&s[c[0]]||(void 0!==i&&(void 0===c[5]||(c[1]="@layer".concat(c[5].length>0?" ".concat(c[5]):""," {").concat(c[1],"}")),c[5]=i),r&&(c[2]?(c[1]="@media ".concat(c[2]," {").concat(c[1],"}"),c[2]=r):c[2]=r),a&&(c[4]?(c[1]="@supports (".concat(c[4],") {").concat(c[1],"}"),c[4]=a):c[4]="".concat(a)),t.push(c))}},t}},806:e=>{e.exports=function(e,t){return t||(t={}),e?(e=String(e.__esModule?e.default:e),/^['"].*['"]$/.test(e)&&(e=e.slice(1,-1)),t.hash&&(e+=t.hash),/["'() \t\n]|(%20)/.test(e)||t.needQuotes?'"'.concat(e.replace(/"/g,'\\"').replace(/\n/g,"\\n"),'"'):e):e}},537:e=>{e.exports=function(e){var t=e[1],r=e[3];if(!r)return t;if("function"==typeof btoa){var n=btoa(unescape(encodeURIComponent(JSON.stringify(r)))),a="sourceMappingURL=data:application/json;charset=utf-8;base64,".concat(n),i="/*# ".concat(a," */");return[t].concat([i]).join("\n")}return[t].join("\n")}},522:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.OpenXmlPackage=void 0;const n=r(626),a=r(472),i=r(593),s=r(461);class o{constructor(e,t){this._zip=e,this.options=t,this.xmlParser=new a.XmlParser}get(e){return this._zip.files[function(e){return e.startsWith("/")?e.substr(1):e}(e)]}update(e,t){this._zip.file(e,t)}static async load(e,t){const r=await n.loadAsync(e);return new o(r,t)}save(e="blob"){return this._zip.generateAsync({type:e})}load(e,t="string"){var r,n;return null!==(n=null===(r=this.get(e))||void 0===r?void 0:r.async(t))&&void 0!==n?n:Promise.resolve(null)}async loadRelationships(e=null){let t="_rels/.rels";if(null!=e){const[r,n]=(0,i.splitPath)(e);t=`${r}_rels/${n}.rels`}const r=await this.load(t);return r?(0,s.parseRelationships)(this.parseXmlDocument(r).firstElementChild,this.xmlParser):null}parseXmlDocument(e){return(0,a.parseXmlString)(e,this.options.trimXmlDeclaration)}}t.OpenXmlPackage=o},530:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.Part=void 0;const n=r(472);t.Part=class{constructor(e,t){this._package=e,this.path=t}async load(){this.rels=await this._package.loadRelationships(this.path);const e=await this._package.load(this.path),t=this._package.parseXmlDocument(e);this._package.options.keepOrigin&&(this._xmlDocument=t),this.parseXml(t.firstElementChild)}save(){this._package.update(this.path,(0,n.serializeXmlString)(this._xmlDocument))}parseXml(e){}}},461:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.parseRelationships=t.RelationshipTypes=void 0,function(e){e.OfficeDocument="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument",e.FontTable="http://schemas.openxmlformats.org/officeDocument/2006/relationships/fontTable",e.Image="http://schemas.openxmlformats.org/officeDocument/2006/relationships/image",e.Numbering="http://schemas.openxmlformats.org/officeDocument/2006/relationships/numbering",e.Styles="http://schemas.openxmlformats.org/officeDocument/2006/relationships/styles",e.StylesWithEffects="http://schemas.microsoft.com/office/2007/relationships/stylesWithEffects",e.Theme="http://schemas.openxmlformats.org/officeDocument/2006/relationships/theme",e.Settings="http://schemas.openxmlformats.org/officeDocument/2006/relationships/settings",e.WebSettings="http://schemas.openxmlformats.org/officeDocument/2006/relationships/webSettings",e.Hyperlink="http://schemas.openxmlformats.org/officeDocument/2006/relationships/hyperlink",e.Footnotes="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footnotes",e.Endnotes="http://schemas.openxmlformats.org/officeDocument/2006/relationships/endnotes",e.Footer="http://schemas.openxmlformats.org/officeDocument/2006/relationships/footer",e.Header="http://schemas.openxmlformats.org/officeDocument/2006/relationships/header",e.ExtendedProperties="http://schemas.openxmlformats.org/officeDocument/2006/relationships/extended-properties",e.CoreProperties="http://schemas.openxmlformats.org/package/2006/relationships/metadata/core-properties",e.CustomProperties="http://schemas.openxmlformats.org/package/2006/relationships/metadata/custom-properties"}(r||(t.RelationshipTypes=r={})),t.parseRelationships=function(e,t){return t.elements(e).map((e=>({id:t.attr(e,"Id"),type:t.attr(e,"Type"),target:t.attr(e,"Target"),targetMode:t.attr(e,"TargetMode")})))}},168:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentParser=t.autos=void 0;const n=r(120),a=r(109),i=r(59),s=r(472),o=r(488),l=r(172),u=r(149),c=r(320);t.autos={shd:"inherit",color:"black",borderColor:"black",highlight:"transparent"};const h=[],d={oMath:n.DomType.MmlMath,oMathPara:n.DomType.MmlMathParagraph,f:n.DomType.MmlFraction,func:n.DomType.MmlFunction,fName:n.DomType.MmlFunctionName,num:n.DomType.MmlNumerator,den:n.DomType.MmlDenominator,rad:n.DomType.MmlRadical,deg:n.DomType.MmlDegree,e:n.DomType.MmlBase,sSup:n.DomType.MmlSuperscript,sSub:n.DomType.MmlSubscript,sPre:n.DomType.MmlPreSubSuper,sup:n.DomType.MmlSuperArgument,sub:n.DomType.MmlSubArgument,d:n.DomType.MmlDelimiter,nary:n.DomType.MmlNary,eqArr:n.DomType.MmlEquationArray,lim:n.DomType.MmlLimit,limLow:n.DomType.MmlLimitLower,m:n.DomType.MmlMatrix,mr:n.DomType.MmlMatrixRow,box:n.DomType.MmlBox,bar:n.DomType.MmlBar,groupChr:n.DomType.MmlGroupChar};t.DocumentParser=class{constructor(e){this.options=Object.assign({ignoreWidth:!1,debug:!1},e)}parseNotes(e,t,r){var n=[];for(let a of s.default.elements(e,t)){const e=new r;e.id=s.default.attr(a,"id"),e.noteType=s.default.attr(a,"type"),e.children=this.parseBodyElements(a),n.push(e)}return n}parseDocumentFile(e){var t=s.default.element(e,"body"),r=s.default.element(e,"background"),a=s.default.element(t,"sectPr");return{type:n.DomType.Document,children:this.parseBodyElements(t),props:a?(0,i.parseSectionProperties)(a,s.default):{},cssStyle:r?this.parseBackground(r):{}}}parseBackground(e){var t={},r=p.colorAttr(e,"color");return r&&(t["background-color"]=r),t}parseBodyElements(e){var t=[];for(let r of s.default.elements(e))switch(r.localName){case"p":t.push(this.parseParagraph(r));break;case"tbl":t.push(this.parseTable(r));break;case"sdt":t.push(...this.parseSdt(r,(e=>this.parseBodyElements(e))))}return t}parseStylesFile(e){var t=[];return p.foreach(e,(e=>{switch(e.localName){case"style":t.push(this.parseStyle(e));break;case"docDefaults":t.push(this.parseDefaultStyles(e))}})),t}parseDefaultStyles(e){var t={id:null,name:null,target:null,basedOn:null,styles:[]};return p.foreach(e,(e=>{switch(e.localName){case"rPrDefault":var r=s.default.element(e,"rPr");r&&t.styles.push({target:"span",values:this.parseDefaultProperties(r,{})});break;case"pPrDefault":var n=s.default.element(e,"pPr");n&&t.styles.push({target:"p",values:this.parseDefaultProperties(n,{})})}})),t}parseStyle(e){var t={id:s.default.attr(e,"styleId"),isDefault:s.default.boolAttr(e,"default"),name:null,target:null,basedOn:null,styles:[],linked:null};switch(s.default.attr(e,"type")){case"paragraph":t.target="p";break;case"table":t.target="table";break;case"character":t.target="span"}return p.foreach(e,(e=>{switch(e.localName){case"basedOn":t.basedOn=s.default.attr(e,"val");break;case"name":t.name=s.default.attr(e,"val");break;case"link":t.linked=s.default.attr(e,"val");break;case"next":t.next=s.default.attr(e,"val");break;case"aliases":t.aliases=s.default.attr(e,"val").split(",");break;case"pPr":t.styles.push({target:"p",values:this.parseDefaultProperties(e,{})}),t.paragraphProps=(0,a.parseParagraphProperties)(e,s.default);break;case"rPr":t.styles.push({target:"span",values:this.parseDefaultProperties(e,{})}),t.runProps=(0,o.parseRunProperties)(e,s.default);break;case"tblPr":case"tcPr":t.styles.push({target:"td",values:this.parseDefaultProperties(e,{})});break;case"tblStylePr":for(let r of this.parseTableStyle(e))t.styles.push(r);break;case"rsid":case"qFormat":case"hidden":case"semiHidden":case"unhideWhenUsed":case"autoRedefine":case"uiPriority":break;default:this.options.debug&&console.warn(`DOCX: Unknown style element: ${e.localName}`)}})),t}parseTableStyle(e){var t=[],r=s.default.attr(e,"type"),n="",a="";switch(r){case"firstRow":a=".first-row",n="tr.first-row td";break;case"lastRow":a=".last-row",n="tr.last-row td";break;case"firstCol":a=".first-col",n="td.first-col";break;case"lastCol":a=".last-col",n="td.last-col";break;case"band1Vert":a=":not(.no-vband)",n="td.odd-col";break;case"band2Vert":a=":not(.no-vband)",n="td.even-col";break;case"band1Horz":a=":not(.no-hband)",n="tr.odd-row";break;case"band2Horz":a=":not(.no-hband)",n="tr.even-row";break;default:return[]}return p.foreach(e,(e=>{switch(e.localName){case"pPr":t.push({target:`${n} p`,mod:a,values:this.parseDefaultProperties(e,{})});break;case"rPr":t.push({target:`${n} span`,mod:a,values:this.parseDefaultProperties(e,{})});break;case"tblPr":case"tcPr":t.push({target:n,mod:a,values:this.parseDefaultProperties(e,{})})}})),t}parseNumberingFile(e){var t=[],r={},n=[];return p.foreach(e,(e=>{switch(e.localName){case"abstractNum":this.parseAbstractNumbering(e,n).forEach((e=>t.push(e)));break;case"numPicBullet":n.push(this.parseNumberingPicBullet(e));break;case"num":var a=s.default.attr(e,"numId"),i=s.default.elementAttr(e,"abstractNumId","val");r[i]=a}})),t.forEach((e=>e.id=r[e.id])),t}parseNumberingPicBullet(e){var t=s.default.element(e,"pict"),r=t&&s.default.element(t,"shape"),n=r&&s.default.element(r,"imagedata");return n?{id:s.default.intAttr(e,"numPicBulletId"),src:s.default.attr(n,"id"),style:s.default.attr(r,"style")}:null}parseAbstractNumbering(e,t){var r=[],n=s.default.attr(e,"abstractNumId");return p.foreach(e,(e=>{"lvl"===e.localName&&r.push(this.parseNumberingLevel(n,e,t))})),r}parseNumberingLevel(e,t,r){var n={id:e,level:s.default.intAttr(t,"ilvl"),start:1,pStyleName:void 0,pStyle:{},rStyle:{},suff:"tab"};return p.foreach(t,(e=>{switch(e.localName){case"start":n.start=s.default.intAttr(e,"val");break;case"pPr":this.parseDefaultProperties(e,n.pStyle);break;case"rPr":this.parseDefaultProperties(e,n.rStyle);break;case"lvlPicBulletId":var t=s.default.intAttr(e,"val");n.bullet=r.find((e=>e.id==t));break;case"lvlText":n.levelText=s.default.attr(e,"val");break;case"pStyle":n.pStyleName=s.default.attr(e,"val");break;case"numFmt":n.format=s.default.attr(e,"val");break;case"suff":n.suff=s.default.attr(e,"val")}})),n}parseSdt(e,t){const r=s.default.element(e,"sdtContent");return r?t(r):[]}parseInserted(e,t){var r,a;return{type:n.DomType.Inserted,children:null!==(a=null===(r=t(e))||void 0===r?void 0:r.children)&&void 0!==a?a:[]}}parseDeleted(e,t){var r,a;return{type:n.DomType.Deleted,children:null!==(a=null===(r=t(e))||void 0===r?void 0:r.children)&&void 0!==a?a:[]}}parseParagraph(e){var t={type:n.DomType.Paragraph,children:[]};for(let r of s.default.elements(e))switch(r.localName){case"pPr":this.parseParagraphProperties(r,t);break;case"r":t.children.push(this.parseRun(r,t));break;case"hyperlink":t.children.push(this.parseHyperlink(r,t));break;case"bookmarkStart":t.children.push((0,l.parseBookmarkStart)(r,s.default));break;case"bookmarkEnd":t.children.push((0,l.parseBookmarkEnd)(r,s.default));break;case"oMath":case"oMathPara":t.children.push(this.parseMathElement(r));break;case"sdt":t.children.push(...this.parseSdt(r,(e=>this.parseParagraph(e).children)));break;case"ins":t.children.push(this.parseInserted(r,(e=>this.parseParagraph(e))));break;case"del":t.children.push(this.parseDeleted(r,(e=>this.parseParagraph(e))))}return t}parseParagraphProperties(e,t){this.parseDefaultProperties(e,t.cssStyle={},null,(e=>{if((0,a.parseParagraphProperty)(e,t,s.default))return!0;switch(e.localName){case"pStyle":t.styleName=s.default.attr(e,"val");break;case"cnfStyle":t.className=m.classNameOfCnfStyle(e);break;case"framePr":this.parseFrame(e,t);break;case"rPr":break;default:return!1}return!0}))}parseFrame(e,t){"drop"==s.default.attr(e,"dropCap")&&(t.cssStyle.float="left")}parseHyperlink(e,t){var r={type:n.DomType.Hyperlink,parent:t,children:[]},a=s.default.attr(e,"anchor"),i=s.default.attr(e,"id");return a&&(r.href="#"+a),i&&(r.id=i),p.foreach(e,(e=>{"r"===e.localName&&r.children.push(this.parseRun(e,r))})),r}parseRun(e,t){var r={type:n.DomType.Run,parent:t,children:[]};return p.foreach(e,(e=>{switch((e=this.checkAlternateContent(e)).localName){case"t":r.children.push({type:n.DomType.Text,text:e.textContent});break;case"delText":r.children.push({type:n.DomType.DeletedText,text:e.textContent});break;case"fldSimple":r.children.push({type:n.DomType.SimpleField,instruction:s.default.attr(e,"instr"),lock:s.default.boolAttr(e,"lock",!1),dirty:s.default.boolAttr(e,"dirty",!1)});break;case"instrText":r.fieldRun=!0,r.children.push({type:n.DomType.Instruction,text:e.textContent});break;case"fldChar":r.fieldRun=!0,r.children.push({type:n.DomType.ComplexField,charType:s.default.attr(e,"fldCharType"),lock:s.default.boolAttr(e,"lock",!1),dirty:s.default.boolAttr(e,"dirty",!1)});break;case"noBreakHyphen":r.children.push({type:n.DomType.NoBreakHyphen});break;case"br":r.children.push({type:n.DomType.Break,break:s.default.attr(e,"type")||"textWrapping"});break;case"lastRenderedPageBreak":r.children.push({type:n.DomType.Break,break:"lastRenderedPageBreak"});break;case"sym":r.children.push({type:n.DomType.Symbol,font:s.default.attr(e,"font"),char:s.default.attr(e,"char")});break;case"tab":r.children.push({type:n.DomType.Tab});break;case"footnoteReference":r.children.push({type:n.DomType.FootnoteReference,id:s.default.attr(e,"id")});break;case"endnoteReference":r.children.push({type:n.DomType.EndnoteReference,id:s.default.attr(e,"id")});break;case"drawing":let t=this.parseDrawing(e);t&&(r.children=[t]);break;case"pict":r.children.push(this.parseVmlPicture(e));break;case"rPr":this.parseRunProperties(e,r)}})),r}parseMathElement(e){const t=`${e.localName}Pr`,r={type:d[e.localName],children:[]};for(const i of s.default.elements(e))if(d[i.localName])r.children.push(this.parseMathElement(i));else if("r"==i.localName){var a=this.parseRun(i);a.type=n.DomType.MmlRun,r.children.push(a)}else i.localName==t&&(r.props=this.parseMathProperies(i));return r}parseMathProperies(e){const t={};for(const r of s.default.elements(e))switch(r.localName){case"chr":t.char=s.default.attr(r,"val");break;case"vertJc":t.verticalJustification=s.default.attr(r,"val");break;case"pos":t.position=s.default.attr(r,"val");break;case"degHide":t.hideDegree=s.default.boolAttr(r,"val");break;case"begChr":t.beginChar=s.default.attr(r,"val");break;case"endChr":t.endChar=s.default.attr(r,"val")}return t}parseRunProperties(e,t){this.parseDefaultProperties(e,t.cssStyle={},null,(e=>{switch(e.localName){case"rStyle":t.styleName=s.default.attr(e,"val");break;case"vertAlign":t.verticalAlign=m.valueOfVertAlign(e,!0);break;default:return!1}return!0}))}parseVmlPicture(e){const t={type:n.DomType.VmlPicture,children:[]};for(const r of s.default.elements(e)){const e=(0,c.parseVmlElement)(r,this);e&&t.children.push(e)}return t}checkAlternateContent(e){var t;if("AlternateContent"!=e.localName)return e;var r=s.default.element(e,"Choice");if(r){var n=s.default.attr(r,"Requires"),a=e.lookupNamespaceURI(n);if(h.includes(a))return r.firstElementChild}return null===(t=s.default.element(e,"Fallback"))||void 0===t?void 0:t.firstElementChild}parseDrawing(e){for(var t of s.default.elements(e))switch(t.localName){case"inline":case"anchor":return this.parseDrawingWrapper(t)}}parseDrawingWrapper(e){var t,r={type:n.DomType.Drawing,children:[],cssStyle:{}},a="anchor"==e.localName;let i=null,o=s.default.boolAttr(e,"simplePos"),l={relative:"page",align:"left",offset:"0"},c={relative:"page",align:"top",offset:"0"};for(var h of s.default.elements(e))switch(h.localName){case"simplePos":o&&(l.offset=s.default.lengthAttr(h,"x",u.LengthUsage.Emu),c.offset=s.default.lengthAttr(h,"y",u.LengthUsage.Emu));break;case"extent":r.cssStyle.width=s.default.lengthAttr(h,"cx",u.LengthUsage.Emu),r.cssStyle.height=s.default.lengthAttr(h,"cy",u.LengthUsage.Emu);break;case"positionH":case"positionV":if(!o){let e="positionH"==h.localName?l:c;var d=s.default.element(h,"align"),f=s.default.element(h,"posOffset");e.relative=null!==(t=s.default.attr(h,"relativeFrom"))&&void 0!==t?t:e.relative,d&&(e.align=d.textContent),f&&(e.offset=p.sizeValue(f,u.LengthUsage.Emu))}break;case"wrapTopAndBottom":i="wrapTopAndBottom";break;case"wrapNone":i="wrapNone";break;case"graphic":var m=this.parseGraphic(h);m&&r.children.push(m)}return"wrapTopAndBottom"==i?(r.cssStyle.display="block",l.align&&(r.cssStyle["text-align"]=l.align,r.cssStyle.width="100%")):"wrapNone"==i?(r.cssStyle.display="block",r.cssStyle.position="relative",r.cssStyle.width="0px",r.cssStyle.height="0px",l.offset&&(r.cssStyle.left=l.offset),c.offset&&(r.cssStyle.top=c.offset)):!a||"left"!=l.align&&"right"!=l.align||(r.cssStyle.float=l.align),r}parseGraphic(e){var t=s.default.element(e,"graphicData");for(let e of s.default.elements(t))if("pic"===e.localName)return this.parsePicture(e);return null}parsePicture(e){var t={type:n.DomType.Image,src:"",cssStyle:{}},r=s.default.element(e,"blipFill"),a=s.default.element(r,"blip");t.src=s.default.attr(a,"embed");var i=s.default.element(e,"spPr"),o=s.default.element(i,"xfrm");for(var l of(t.cssStyle.position="relative",s.default.elements(o)))switch(l.localName){case"ext":t.cssStyle.width=s.default.lengthAttr(l,"cx",u.LengthUsage.Emu),t.cssStyle.height=s.default.lengthAttr(l,"cy",u.LengthUsage.Emu);break;case"off":t.cssStyle.left=s.default.lengthAttr(l,"x",u.LengthUsage.Emu),t.cssStyle.top=s.default.lengthAttr(l,"y",u.LengthUsage.Emu)}return t}parseTable(e){var t={type:n.DomType.Table,children:[]};return p.foreach(e,(e=>{switch(e.localName){case"tr":t.children.push(this.parseTableRow(e));break;case"tblGrid":t.columns=this.parseTableColumns(e);break;case"tblPr":this.parseTableProperties(e,t)}})),t}parseTableColumns(e){var t=[];return p.foreach(e,(e=>{"gridCol"===e.localName&&t.push({width:s.default.lengthAttr(e,"w")})})),t}parseTableProperties(e,t){switch(t.cssStyle={},t.cellStyle={},this.parseDefaultProperties(e,t.cssStyle,t.cellStyle,(e=>{switch(e.localName){case"tblStyle":t.styleName=s.default.attr(e,"val");break;case"tblLook":t.className=m.classNameOftblLook(e);break;case"tblpPr":this.parseTablePosition(e,t);break;case"tblStyleColBandSize":t.colBandSize=s.default.intAttr(e,"val");break;case"tblStyleRowBandSize":t.rowBandSize=s.default.intAttr(e,"val");break;default:return!1}return!0})),t.cssStyle["text-align"]){case"center":delete t.cssStyle["text-align"],t.cssStyle["margin-left"]="auto",t.cssStyle["margin-right"]="auto";break;case"right":delete t.cssStyle["text-align"],t.cssStyle["margin-left"]="auto"}}parseTablePosition(e,t){var r=s.default.lengthAttr(e,"topFromText"),n=s.default.lengthAttr(e,"bottomFromText"),a=s.default.lengthAttr(e,"rightFromText"),i=s.default.lengthAttr(e,"leftFromText");t.cssStyle.float="left",t.cssStyle["margin-bottom"]=m.addSize(t.cssStyle["margin-bottom"],n),t.cssStyle["margin-left"]=m.addSize(t.cssStyle["margin-left"],i),t.cssStyle["margin-right"]=m.addSize(t.cssStyle["margin-right"],a),t.cssStyle["margin-top"]=m.addSize(t.cssStyle["margin-top"],r)}parseTableRow(e){var t={type:n.DomType.Row,children:[]};return p.foreach(e,(e=>{switch(e.localName){case"tc":t.children.push(this.parseTableCell(e));break;case"trPr":this.parseTableRowProperties(e,t)}})),t}parseTableRowProperties(e,t){t.cssStyle=this.parseDefaultProperties(e,{},null,(e=>{switch(e.localName){case"cnfStyle":t.className=m.classNameOfCnfStyle(e);break;case"tblHeader":t.isHeader=s.default.boolAttr(e,"val");break;default:return!1}return!0}))}parseTableCell(e){var t={type:n.DomType.Cell,children:[]};return p.foreach(e,(e=>{switch(e.localName){case"tbl":t.children.push(this.parseTable(e));break;case"p":t.children.push(this.parseParagraph(e));break;case"tcPr":this.parseTableCellProperties(e,t)}})),t}parseTableCellProperties(e,t){t.cssStyle=this.parseDefaultProperties(e,{},null,(e=>{var r;switch(e.localName){case"gridSpan":t.span=s.default.intAttr(e,"val",null);break;case"vMerge":t.verticalMerge=null!==(r=s.default.attr(e,"val"))&&void 0!==r?r:"continue";break;case"cnfStyle":t.className=m.classNameOfCnfStyle(e);break;default:return!1}return!0}))}parseDefaultProperties(e,r=null,n=null,a=null){return r=r||{},p.foreach(e,(i=>{if(!(null==a?void 0:a(i)))switch(i.localName){case"jc":r["text-align"]=m.valueOfJc(i);break;case"textAlignment":r["vertical-align"]=m.valueOfTextAlignment(i);break;case"color":r.color=p.colorAttr(i,"val",null,t.autos.color);break;case"sz":r["font-size"]=r["min-height"]=s.default.lengthAttr(i,"val",u.LengthUsage.FontSize);break;case"shd":r["background-color"]=p.colorAttr(i,"fill",null,t.autos.shd);break;case"highlight":r["background-color"]=p.colorAttr(i,"val",null,t.autos.highlight);break;case"vertAlign":break;case"position":r.verticalAlign=s.default.lengthAttr(i,"val",u.LengthUsage.FontSize);break;case"tcW":if(this.options.ignoreWidth)break;case"tblW":r.width=m.valueOfSize(i,"w");break;case"trHeight":this.parseTrHeight(i,r);break;case"strike":r["text-decoration"]=s.default.boolAttr(i,"val",!0)?"line-through":"none";break;case"b":r["font-weight"]=s.default.boolAttr(i,"val",!0)?"bold":"normal";break;case"i":r["font-style"]=s.default.boolAttr(i,"val",!0)?"italic":"normal";break;case"caps":r["text-transform"]=s.default.boolAttr(i,"val",!0)?"uppercase":"none";break;case"smallCaps":r["text-transform"]=s.default.boolAttr(i,"val",!0)?"lowercase":"none";break;case"u":this.parseUnderline(i,r);break;case"ind":case"tblInd":this.parseIndentation(i,r);break;case"rFonts":this.parseFont(i,r);break;case"tblBorders":this.parseBorderProperties(i,n||r);break;case"tblCellSpacing":r["border-spacing"]=m.valueOfMargin(i),r["border-collapse"]="separate";break;case"pBdr":this.parseBorderProperties(i,r);break;case"bdr":r.border=m.valueOfBorder(i);break;case"tcBorders":this.parseBorderProperties(i,r);break;case"vanish":s.default.boolAttr(i,"val",!0)&&(r.display="none");break;case"kern":case"noWrap":break;case"tblCellMar":case"tcMar":this.parseMarginProperties(i,n||r);break;case"tblLayout":r["table-layout"]=m.valueOfTblLayout(i);break;case"vAlign":r["vertical-align"]=m.valueOfTextAlignment(i);break;case"spacing":"pPr"==e.localName&&this.parseSpacing(i,r);break;case"wordWrap":s.default.boolAttr(i,"val")&&(r["overflow-wrap"]="break-word");break;case"suppressAutoHyphens":r.hyphens=s.default.boolAttr(i,"val",!0)?"none":"auto";break;case"lang":r.$lang=s.default.attr(i,"val");break;case"bCs":case"iCs":case"szCs":case"tabs":case"outlineLvl":case"contextualSpacing":case"tblStyleColBandSize":case"tblStyleRowBandSize":case"webHidden":case"pageBreakBefore":case"suppressLineNumbers":case"keepLines":case"keepNext":case"widowControl":case"bidi":case"rtl":case"noProof":break;default:this.options.debug&&console.warn(`DOCX: Unknown document element: ${e.localName}.${i.localName}`)}})),r}parseUnderline(e,t){var r=s.default.attr(e,"val");if(null!=r){switch(r){case"dash":case"dashDotDotHeavy":case"dashDotHeavy":case"dashedHeavy":case"dashLong":case"dashLongHeavy":case"dotDash":case"dotDotDash":t["text-decoration-style"]="dashed";break;case"dotted":case"dottedHeavy":t["text-decoration-style"]="dotted";break;case"double":t["text-decoration-style"]="double";break;case"single":case"thick":case"words":t["text-decoration"]="underline";break;case"wave":case"wavyDouble":case"wavyHeavy":t["text-decoration-style"]="wavy";break;case"none":t["text-decoration"]="none"}var n=p.colorAttr(e,"color");n&&(t["text-decoration-color"]=n)}}parseFont(e,t){var r=[s.default.attr(e,"ascii"),m.themeValue(e,"asciiTheme")].filter((e=>e)).join(", ");r.length>0&&(t["font-family"]=r)}parseIndentation(e,t){var r=s.default.lengthAttr(e,"firstLine"),n=s.default.lengthAttr(e,"hanging"),a=s.default.lengthAttr(e,"left"),i=s.default.lengthAttr(e,"start"),o=s.default.lengthAttr(e,"right"),l=s.default.lengthAttr(e,"end");r&&(t["text-indent"]=r),n&&(t["text-indent"]=`-${n}`),(a||i)&&(t["margin-left"]=a||i),(o||l)&&(t["margin-right"]=o||l)}parseSpacing(e,t){var r=s.default.lengthAttr(e,"before"),n=s.default.lengthAttr(e,"after"),a=s.default.intAttr(e,"line",null),i=s.default.attr(e,"lineRule");if(r&&(t["margin-top"]=r),n&&(t["margin-bottom"]=n),null!==a)switch(i){case"auto":t["line-height"]=`${(a/240).toFixed(2)}`;break;case"atLeast":t["line-height"]=`calc(100% + ${a/20}pt)`;break;default:t["line-height"]=t["min-height"]=a/20+"pt"}}parseMarginProperties(e,t){p.foreach(e,(e=>{switch(e.localName){case"left":t["padding-left"]=m.valueOfMargin(e);break;case"right":t["padding-right"]=m.valueOfMargin(e);break;case"top":t["padding-top"]=m.valueOfMargin(e);break;case"bottom":t["padding-bottom"]=m.valueOfMargin(e)}}))}parseTrHeight(e,t){s.default.attr(e,"hRule"),t.height=s.default.lengthAttr(e,"val")}parseBorderProperties(e,t){p.foreach(e,(e=>{switch(e.localName){case"start":case"left":t["border-left"]=m.valueOfBorder(e);break;case"end":case"right":t["border-right"]=m.valueOfBorder(e);break;case"top":t["border-top"]=m.valueOfBorder(e);break;case"bottom":t["border-bottom"]=m.valueOfBorder(e)}}))}};const f=["black","blue","cyan","darkBlue","darkCyan","darkGray","darkGreen","darkMagenta","darkRed","darkYellow","green","lightGray","magenta","none","red","white","yellow"];class p{static foreach(e,t){for(var r=0;r<e.childNodes.length;r++){let n=e.childNodes[r];n.nodeType==Node.ELEMENT_NODE&&t(n)}}static colorAttr(e,t,r=null,n="black"){var a=s.default.attr(e,t);if(a)return"auto"==a?n:f.includes(a)?a:`#${a}`;var i=s.default.attr(e,"themeColor");return i?`var(--docx-${i}-color)`:r}static sizeValue(e,t=u.LengthUsage.Dxa){return(0,u.convertLength)(e.textContent,t)}}class m{static themeValue(e,t){var r=s.default.attr(e,t);return r?`var(--docx-${r}-font)`:null}static valueOfSize(e,t){var r=u.LengthUsage.Dxa;switch(s.default.attr(e,"type")){case"dxa":break;case"pct":r=u.LengthUsage.Percent;break;case"auto":return"auto"}return s.default.lengthAttr(e,t,r)}static valueOfMargin(e){return s.default.lengthAttr(e,"w")}static valueOfBorder(e){if("nil"==s.default.attr(e,"val"))return"none";var r=p.colorAttr(e,"color");return`${s.default.lengthAttr(e,"sz",u.LengthUsage.Border)} solid ${"auto"==r?t.autos.borderColor:r}`}static valueOfTblLayout(e){return"fixed"==s.default.attr(e,"val")?"fixed":"auto"}static classNameOfCnfStyle(e){const t=s.default.attr(e,"val");return["first-row","last-row","first-col","last-col","odd-col","even-col","odd-row","even-row","ne-cell","nw-cell","se-cell","sw-cell"].filter(((e,r)=>"1"==t[r])).join(" ")}static valueOfJc(e){var t=s.default.attr(e,"val");switch(t){case"start":case"left":return"left";case"center":return"center";case"end":case"right":return"right";case"both":return"justify"}return t}static valueOfVertAlign(e,t=!1){var r=s.default.attr(e,"val");switch(r){case"subscript":return"sub";case"superscript":return t?"sup":"super"}return t?null:r}static valueOfTextAlignment(e){var t=s.default.attr(e,"val");switch(t){case"auto":case"baseline":return"baseline";case"top":return"top";case"center":return"middle";case"bottom":return"bottom"}return t}static addSize(e,t){return null==e?t:null==t?e:`calc(${e} + ${t})`}static classNameOftblLook(e){const t=s.default.hexAttr(e,"val",0);let r="";return(s.default.boolAttr(e,"firstRow")||32&t)&&(r+=" first-row"),(s.default.boolAttr(e,"lastRow")||64&t)&&(r+=" last-row"),(s.default.boolAttr(e,"firstColumn")||128&t)&&(r+=" first-col"),(s.default.boolAttr(e,"lastColumn")||256&t)&&(r+=" last-col"),(s.default.boolAttr(e,"noHBand")||512&t)&&(r+=" no-hband"),(s.default.boolAttr(e,"noVBand")||1024&t)&&(r+=" no-vband"),r.trim()}}},162:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CorePropsPart=void 0;const n=r(530),a=r(614);class i extends n.Part{parseXml(e){this.props=(0,a.parseCoreProps)(e,this._package.xmlParser)}}t.CorePropsPart=i},614:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseCoreProps=void 0,t.parseCoreProps=function(e,t){const r={};for(let n of t.elements(e))switch(n.localName){case"title":r.title=n.textContent;break;case"description":r.description=n.textContent;break;case"subject":r.subject=n.textContent;break;case"creator":r.creator=n.textContent;break;case"keywords":r.keywords=n.textContent;break;case"language":r.language=n.textContent;break;case"lastModifiedBy":r.lastModifiedBy=n.textContent;break;case"revision":n.textContent&&(r.revision=parseInt(n.textContent))}return r}},177:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CustomPropsPart=void 0;const n=r(530),a=r(821);class i extends n.Part{parseXml(e){this.props=(0,a.parseCustomProps)(e,this._package.xmlParser)}}t.CustomPropsPart=i},821:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseCustomProps=void 0,t.parseCustomProps=function(e,t){return t.elements(e,"property").map((e=>{const r=e.firstChild;return{formatId:t.attr(e,"fmtid"),name:t.attr(e,"name"),type:r.nodeName,value:r.textContent}}))}},665:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ExtendedPropsPart=void 0;const n=r(530),a=r(668);class i extends n.Part{parseXml(e){this.props=(0,a.parseExtendedProps)(e,this._package.xmlParser)}}t.ExtendedPropsPart=i},668:(e,t)=>{function r(e){if(void 0!==e)return parseInt(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.parseExtendedProps=void 0,t.parseExtendedProps=function(e,t){const n={};for(let a of t.elements(e))switch(a.localName){case"Template":n.template=a.textContent;break;case"Pages":n.pages=r(a.textContent);break;case"Words":n.words=r(a.textContent);break;case"Characters":n.characters=r(a.textContent);break;case"Application":n.application=a.textContent;break;case"Lines":n.lines=r(a.textContent);break;case"Paragraphs":n.paragraphs=r(a.textContent);break;case"Company":n.company=a.textContent;break;case"AppVersion":n.appVersion=a.textContent}return n}},172:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseBookmarkEnd=t.parseBookmarkStart=void 0;const n=r(120);t.parseBookmarkStart=function(e,t){return{type:n.DomType.BookmarkStart,id:t.attr(e,"id"),name:t.attr(e,"name"),colFirst:t.intAttr(e,"colFirst"),colLast:t.intAttr(e,"colLast")}},t.parseBookmarkEnd=function(e,t){return{type:n.DomType.BookmarkEnd,id:t.attr(e,"id")}}},191:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseBorders=t.parseBorder=void 0;const n=r(149);function a(e,t){return{type:t.attr(e,"val"),color:t.attr(e,"color"),size:t.lengthAttr(e,"sz",n.LengthUsage.Border),offset:t.lengthAttr(e,"space",n.LengthUsage.Point),frame:t.boolAttr(e,"frame"),shadow:t.boolAttr(e,"shadow")}}t.parseBorder=a,t.parseBorders=function(e,t){var r={};for(let n of t.elements(e))switch(n.localName){case"left":r.left=a(n,t);break;case"top":r.top=a(n,t);break;case"right":r.right=a(n,t);break;case"bottom":r.bottom=a(n,t)}return r}},149:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseCommonProperty=t.convertPercentage=t.convertBoolean=t.convertLength=t.LengthUsage=t.ns=void 0,t.ns={wordml:"http://schemas.openxmlformats.org/wordprocessingml/2006/main",drawingml:"http://schemas.openxmlformats.org/drawingml/2006/main",picture:"http://schemas.openxmlformats.org/drawingml/2006/picture",compatibility:"http://schemas.openxmlformats.org/markup-compatibility/2006",math:"http://schemas.openxmlformats.org/officeDocument/2006/math"},t.LengthUsage={Dxa:{mul:.05,unit:"pt"},Emu:{mul:1/12700,unit:"pt"},FontSize:{mul:.5,unit:"pt"},Border:{mul:.125,unit:"pt"},Point:{mul:1,unit:"pt"},Percent:{mul:.02,unit:"%"},LineHeight:{mul:1/240,unit:""},VmlEmu:{mul:1/12700,unit:""}},t.convertLength=function(e,r=t.LengthUsage.Dxa){return null==e||/.+(p[xt]|[%])$/.test(e)?e:`${(parseInt(e)*r.mul).toFixed(2)}${r.unit}`},t.convertBoolean=function(e,t=!1){switch(e){case"1":case"on":case"true":return!0;case"0":case"off":case"false":return!1;default:return t}},t.convertPercentage=function(e){return e?parseInt(e)/100:null},t.parseCommonProperty=function(e,r,n){if(e.namespaceURI!=t.ns.wordml)return!1;switch(e.localName){case"color":r.color=n.attr(e,"val");break;case"sz":r.fontSize=n.lengthAttr(e,"val",t.LengthUsage.FontSize);break;default:return!1}return!0}},448:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DocumentPart=void 0;const n=r(530);class a extends n.Part{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.body=this._documentParser.parseDocumentFile(e)}}t.DocumentPart=a},120:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),t.OpenXmlElementBase=t.DomType=void 0,function(e){e.Document="document",e.Paragraph="paragraph",e.Run="run",e.Break="break",e.NoBreakHyphen="noBreakHyphen",e.Table="table",e.Row="row",e.Cell="cell",e.Hyperlink="hyperlink",e.Drawing="drawing",e.Image="image",e.Text="text",e.Tab="tab",e.Symbol="symbol",e.BookmarkStart="bookmarkStart",e.BookmarkEnd="bookmarkEnd",e.Footer="footer",e.Header="header",e.FootnoteReference="footnoteReference",e.EndnoteReference="endnoteReference",e.Footnote="footnote",e.Endnote="endnote",e.SimpleField="simpleField",e.ComplexField="complexField",e.Instruction="instruction",e.VmlPicture="vmlPicture",e.MmlMath="mmlMath",e.MmlMathParagraph="mmlMathParagraph",e.MmlFraction="mmlFraction",e.MmlFunction="mmlFunction",e.MmlFunctionName="mmlFunctionName",e.MmlNumerator="mmlNumerator",e.MmlDenominator="mmlDenominator",e.MmlRadical="mmlRadical",e.MmlBase="mmlBase",e.MmlDegree="mmlDegree",e.MmlSuperscript="mmlSuperscript",e.MmlSubscript="mmlSubscript",e.MmlPreSubSuper="mmlPreSubSuper",e.MmlSubArgument="mmlSubArgument",e.MmlSuperArgument="mmlSuperArgument",e.MmlNary="mmlNary",e.MmlDelimiter="mmlDelimiter",e.MmlRun="mmlRun",e.MmlEquationArray="mmlEquationArray",e.MmlLimit="mmlLimit",e.MmlLimitLower="mmlLimitLower",e.MmlMatrix="mmlMatrix",e.MmlMatrixRow="mmlMatrixRow",e.MmlBox="mmlBox",e.MmlBar="mmlBar",e.MmlGroupChar="mmlGroupChar",e.VmlElement="vmlElement",e.Inserted="inserted",e.Deleted="deleted",e.DeletedText="deletedText"}(r||(t.DomType=r={})),t.OpenXmlElementBase=class{constructor(){this.children=[],this.cssStyle={}}}},931:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseLineSpacing=void 0,t.parseLineSpacing=function(e,t){return{before:t.lengthAttr(e,"before"),after:t.lengthAttr(e,"after"),line:t.intAttr(e,"line"),lineRule:t.attr(e,"lineRule")}}},109:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseNumbering=t.parseTabs=t.parseParagraphProperty=t.parseParagraphProperties=void 0;const n=r(149),a=r(59),i=r(931),s=r(488);function o(e,t,r){if(e.namespaceURI!=n.ns.wordml)return!1;if((0,n.parseCommonProperty)(e,t,r))return!0;switch(e.localName){case"tabs":t.tabs=l(e,r);break;case"sectPr":t.sectionProps=(0,a.parseSectionProperties)(e,r);break;case"numPr":t.numbering=u(e,r);break;case"spacing":return t.lineSpacing=(0,i.parseLineSpacing)(e,r),!1;case"textAlignment":return t.textAlignment=r.attr(e,"val"),!1;case"keepNext":t.keepLines=r.boolAttr(e,"val",!0);break;case"keepNext":t.keepNext=r.boolAttr(e,"val",!0);break;case"pageBreakBefore":t.pageBreakBefore=r.boolAttr(e,"val",!0);break;case"outlineLvl":t.outlineLevel=r.intAttr(e,"val");break;case"pStyle":t.styleName=r.attr(e,"val");break;case"rPr":t.runProps=(0,s.parseRunProperties)(e,r);break;default:return!1}return!0}function l(e,t){return t.elements(e,"tab").map((e=>({position:t.lengthAttr(e,"pos"),leader:t.attr(e,"leader"),style:t.attr(e,"val")})))}function u(e,t){var r={};for(let n of t.elements(e))switch(n.localName){case"numId":r.id=t.attr(n,"val");break;case"ilvl":r.level=t.intAttr(n,"val")}return r}t.parseParagraphProperties=function(e,t){let r={};for(let n of t.elements(e))o(n,r,t);return r},t.parseParagraphProperty=o,t.parseTabs=l,t.parseNumbering=u},488:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseRunProperty=t.parseRunProperties=void 0;const n=r(149);function a(e,t,r){return!!(0,n.parseCommonProperty)(e,t,r)}t.parseRunProperties=function(e,t){let r={};for(let n of t.elements(e))a(n,r,t);return r},t.parseRunProperty=a},59:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseSectionProperties=t.SectionType=void 0;const n=r(472),a=r(191);var i;function s(e,t){return{numberOfColumns:t.intAttr(e,"num"),space:t.lengthAttr(e,"space"),separator:t.boolAttr(e,"sep"),equalWidth:t.boolAttr(e,"equalWidth",!0),columns:t.elements(e,"col").map((e=>({width:t.lengthAttr(e,"w"),space:t.lengthAttr(e,"space")})))}}function o(e,t){return{chapSep:t.attr(e,"chapSep"),chapStyle:t.attr(e,"chapStyle"),format:t.attr(e,"fmt"),start:t.intAttr(e,"start")}}function l(e,t){return{id:t.attr(e,"id"),type:t.attr(e,"type")}}!function(e){e.Continuous="continuous",e.NextPage="nextPage",e.NextColumn="nextColumn",e.EvenPage="evenPage",e.OddPage="oddPage"}(i||(t.SectionType=i={})),t.parseSectionProperties=function(e,t=n.default){var r,i,u={};for(let n of t.elements(e))switch(n.localName){case"pgSz":u.pageSize={width:t.lengthAttr(n,"w"),height:t.lengthAttr(n,"h"),orientation:t.attr(n,"orient")};break;case"type":u.type=t.attr(n,"val");break;case"pgMar":u.pageMargins={left:t.lengthAttr(n,"left"),right:t.lengthAttr(n,"right"),top:t.lengthAttr(n,"top"),bottom:t.lengthAttr(n,"bottom"),header:t.lengthAttr(n,"header"),footer:t.lengthAttr(n,"footer"),gutter:t.lengthAttr(n,"gutter")};break;case"cols":u.columns=s(n,t);break;case"headerReference":(null!==(r=u.headerRefs)&&void 0!==r?r:u.headerRefs=[]).push(l(n,t));break;case"footerReference":(null!==(i=u.footerRefs)&&void 0!==i?i:u.footerRefs=[]).push(l(n,t));break;case"titlePg":u.titlePage=t.boolAttr(n,"val",!0);break;case"pgBorders":u.pageBorders=(0,a.parseBorders)(n,t);break;case"pgNumType":u.pageNumber=o(n,t)}return u}},667:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.renderAsync=t.praseAsync=t.defaultOptions=void 0;const n=r(213),a=r(168),i=r(932);t.defaultOptions={ignoreHeight:!1,ignoreWidth:!1,ignoreFonts:!1,breakPages:!0,debug:!1,experimental:!1,className:"docx",inWrapper:!0,trimXmlDeclaration:!0,ignoreLastRenderedPageBreak:!0,renderHeaders:!0,renderFooters:!0,renderFootnotes:!0,renderEndnotes:!0,useBase64URL:!1,useMathMLPolyfill:!1,renderChanges:!1},t.praseAsync=function(e,r=null){const i=Object.assign(Object.assign({},t.defaultOptions),r);return n.WordDocument.load(e,new a.DocumentParser(i),i)},t.renderAsync=async function(e,r,s=null,o=null){const l=Object.assign(Object.assign({},t.defaultOptions),o),u=new i.HtmlRenderer(window.document),c=await n.WordDocument.load(e,new a.DocumentParser(l),l);return u.render(c,r,s,l),c}},380:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FontTablePart=void 0;const n=r(530),a=r(512);class i extends n.Part{parseXml(e){this.fonts=(0,a.parseFonts)(e,this._package.xmlParser)}}t.FontTablePart=i},512:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseEmbedFontRef=t.parseFont=t.parseFonts=void 0;const r={embedRegular:"regular",embedBold:"bold",embedItalic:"italic",embedBoldItalic:"boldItalic"};function n(e,t){let r={name:t.attr(e,"name"),embedFontRefs:[]};for(let n of t.elements(e))switch(n.localName){case"family":r.family=t.attr(n,"val");break;case"altName":r.altName=t.attr(n,"val");break;case"embedRegular":case"embedBold":case"embedItalic":case"embedBoldItalic":r.embedFontRefs.push(a(n,t))}return r}function a(e,t){return{id:t.attr(e,"id"),key:t.attr(e,"fontKey"),type:r[e.localName]}}t.parseFonts=function(e,t){return t.elements(e).map((e=>n(e,t)))},t.parseFont=n,t.parseEmbedFontRef=a},984:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WmlFooter=t.WmlHeader=void 0;const n=r(120);class a extends n.OpenXmlElementBase{constructor(){super(...arguments),this.type=n.DomType.Header}}t.WmlHeader=a;class i extends n.OpenXmlElementBase{constructor(){super(...arguments),this.type=n.DomType.Footer}}t.WmlFooter=i},985:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.FooterPart=t.HeaderPart=t.BaseHeaderFooterPart=void 0;const n=r(530),a=r(984);class i extends n.Part{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.rootElement=this.createRootElement(),this.rootElement.children=this._documentParser.parseBodyElements(e)}}t.BaseHeaderFooterPart=i,t.HeaderPart=class extends i{createRootElement(){return new a.WmlHeader}},t.FooterPart=class extends i{createRootElement(){return new a.WmlFooter}}},932:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HtmlRenderer=void 0;const n=r(120),a=r(593),i=r(630),s=r(438),o="http://www.w3.org/1998/Math/MathML";function l(e,t,r){return c(void 0,e,t,r)}function u(e,t,r){return c("http://www.w3.org/2000/svg",e,t,r)}function c(e,t,r,n){var a=e?document.createElementNS(e,t):document.createElement(t);return Object.assign(a,r),n&&d(a,n),a}function h(e){e.innerHTML=""}function d(e,t){t.forEach((t=>e.appendChild((0,a.isString)(t)?document.createTextNode(t):t)))}function f(e){return l("style",{innerHTML:e})}function p(e,t){e.appendChild(document.createComment(t))}t.HtmlRenderer=class{constructor(e){this.htmlDocument=e,this.className="docx",this.styleMap={},this.currentPart=null,this.tableVerticalMerges=[],this.currentVerticalMerge=null,this.tableCellPositions=[],this.currentCellPosition=null,this.footnoteMap={},this.endnoteMap={},this.currentEndnoteIds=[],this.usedHederFooterParts=[],this.currentTabs=[],this.tabsTimeout=0,this.createElement=l}render(e,t,r=null,n){var i;this.document=e,this.options=n,this.className=n.className,this.rootSelector=n.inWrapper?`.${this.className}-wrapper`:":root",this.styleMap=null,h(r=r||t),h(t),p(r,"docxjs library predefined styles"),r.appendChild(this.renderDefaultStyle()),!window.MathMLElement&&n.useMathMLPolyfill&&(p(r,"docxjs mathml polyfill styles"),r.appendChild(f(s.default))),e.themePart&&(p(r,"docxjs document theme values"),this.renderTheme(e.themePart,r)),null!=e.stylesPart&&(this.styleMap=this.processStyles(e.stylesPart.styles),p(r,"docxjs document styles"),r.appendChild(this.renderStyles(e.stylesPart.styles))),e.numberingPart&&(this.prodessNumberings(e.numberingPart.domNumberings),p(r,"docxjs document numbering styles"),r.appendChild(this.renderNumbering(e.numberingPart.domNumberings,r))),e.footnotesPart&&(this.footnoteMap=(0,a.keyBy)(e.footnotesPart.notes,(e=>e.id))),e.endnotesPart&&(this.endnoteMap=(0,a.keyBy)(e.endnotesPart.notes,(e=>e.id))),e.settingsPart&&(this.defaultTabSize=null===(i=e.settingsPart.settings)||void 0===i?void 0:i.defaultTabStop),!n.ignoreFonts&&e.fontTablePart&&this.renderFontTable(e.fontTablePart,r);var o=this.renderSections(e.documentPart.body);this.options.inWrapper?t.appendChild(this.renderWrapper(o)):d(t,o),this.refreshTabStops()}renderTheme(e,t){var r,n;const a={},i=null===(r=e.theme)||void 0===r?void 0:r.fontScheme;i&&(i.majorFont&&(a["--docx-majorHAnsi-font"]=i.majorFont.latinTypeface),i.minorFont&&(a["--docx-minorHAnsi-font"]=i.minorFont.latinTypeface));const s=null===(n=e.theme)||void 0===n?void 0:n.colorScheme;if(s)for(let[e,t]of Object.entries(s.colors))a[`--docx-${e}-color`]=`#${t}`;const o=this.styleToString(`.${this.className}`,a);t.appendChild(f(o))}renderFontTable(e,t){for(let r of e.fonts)for(let e of r.embedFontRefs)this.document.loadFont(e.id,e.key).then((n=>{const a={"font-family":r.name,src:`url(${n})`};"bold"!=e.type&&"boldItalic"!=e.type||(a["font-weight"]="bold"),"italic"!=e.type&&"boldItalic"!=e.type||(a["font-style"]="italic"),p(t,`docxjs ${r.name} font`);const i=this.styleToString("@font-face",a);t.appendChild(f(i)),this.refreshTabStops()}))}processStyleName(e){return e?`${this.className}_${(0,a.escapeClassName)(e)}`:this.className}processStyles(e){const t=(0,a.keyBy)(e.filter((e=>null!=e.id)),(e=>e.id));for(const n of e.filter((e=>e.basedOn))){var r=t[n.basedOn];if(r){n.paragraphProps=(0,a.mergeDeep)(n.paragraphProps,r.paragraphProps),n.runProps=(0,a.mergeDeep)(n.runProps,r.runProps);for(const e of r.styles){const t=n.styles.find((t=>t.target==e.target));t?this.copyStyleProperties(e.values,t.values):n.styles.push(Object.assign(Object.assign({},e),{values:Object.assign({},e.values)}))}}else this.options.debug&&console.warn(`Can't find base style ${n.basedOn}`)}for(let t of e)t.cssName=this.processStyleName(t.id);return t}prodessNumberings(e){var t;for(let r of e.filter((e=>e.pStyleName))){const e=this.findStyle(r.pStyleName);(null===(t=null==e?void 0:e.paragraphProps)||void 0===t?void 0:t.numbering)&&(e.paragraphProps.numbering.level=r.level)}}processElement(e){if(e.children)for(var t of e.children)t.parent=e,t.type==n.DomType.Table?this.processTable(t):this.processElement(t)}processTable(e){for(var t of e.children)for(var r of t.children)r.cssStyle=this.copyStyleProperties(e.cellStyle,r.cssStyle,["border-left","border-right","border-top","border-bottom","padding-left","padding-right","padding-top","padding-bottom"]),this.processElement(r)}copyStyleProperties(e,t,r=null){if(!e)return t;for(var n of(null==t&&(t={}),null==r&&(r=Object.getOwnPropertyNames(e)),r))e.hasOwnProperty(n)&&!t.hasOwnProperty(n)&&(t[n]=e[n]);return t}createSection(e,t){var r=this.createElement("section",{className:e});return t&&(t.pageMargins&&(r.style.paddingLeft=t.pageMargins.left,r.style.paddingRight=t.pageMargins.right,r.style.paddingTop=t.pageMargins.top,r.style.paddingBottom=t.pageMargins.bottom),t.pageSize&&(this.options.ignoreWidth||(r.style.width=t.pageSize.width),this.options.ignoreHeight||(r.style.minHeight=t.pageSize.height)),t.columns&&t.columns.numberOfColumns&&(r.style.columnCount=`${t.columns.numberOfColumns}`,r.style.columnGap=t.columns.space,t.columns.separator&&(r.style.columnRule="1px solid black"))),r}renderSections(e){const t=[];this.processElement(e);const r=this.splitBySection(e.children);let n=null;for(let i=0,s=r.length;i<s;i++){this.currentFootnoteIds=[];const o=r[i],l=o.sectProps||e.props,u=this.createSection(this.className,l);this.renderStyleValues(e.cssStyle,u),this.options.renderHeaders&&this.renderHeaderFooter(l.headerRefs,l,t.length,n!=l,u);var a=this.createElement("article");this.renderElements(o.elements,a),u.appendChild(a),this.options.renderFootnotes&&this.renderNotes(this.currentFootnoteIds,this.footnoteMap,u),this.options.renderEndnotes&&i==s-1&&this.renderNotes(this.currentEndnoteIds,this.endnoteMap,u),this.options.renderFooters&&this.renderHeaderFooter(l.footerRefs,l,t.length,n!=l,u),t.push(u),n=l}return t}renderHeaderFooter(e,t,r,n,a){var i,s;if(e){var o=null!==(s=null!==(i=t.titlePage&&n?e.find((e=>"first"==e.type)):null)&&void 0!==i?i:r%2==1?e.find((e=>"even"==e.type)):null)&&void 0!==s?s:e.find((e=>"default"==e.type)),l=o&&this.document.findPartByRelId(o.id,this.document.documentPart);l&&(this.currentPart=l,this.usedHederFooterParts.includes(l.path)||(this.processElement(l.rootElement),this.usedHederFooterParts.push(l.path)),this.renderElements([l.rootElement],a),this.currentPart=null)}}isPageBreakElement(e){return e.type==n.DomType.Break&&("lastRenderedPageBreak"==e.break?!this.options.ignoreLastRenderedPageBreak:"page"==e.break)}splitBySection(e){var t,r={sectProps:null,elements:[]},a=[r];for(let c of e){if(c.type==n.DomType.Paragraph){const e=this.findStyle(c.styleName);(null===(t=null==e?void 0:e.paragraphProps)||void 0===t?void 0:t.pageBreakBefore)&&(r.sectProps=i,r={sectProps:null,elements:[]},a.push(r))}if(r.elements.push(c),c.type==n.DomType.Paragraph){const e=c;var i=e.sectionProps,s=-1,o=-1;if(this.options.breakPages&&e.children&&(s=e.children.findIndex((e=>{var t,r;return-1!=(o=null!==(r=null===(t=e.children)||void 0===t?void 0:t.findIndex(this.isPageBreakElement.bind(this)))&&void 0!==r?r:-1)}))),(i||-1!=s)&&(r.sectProps=i,r={sectProps:null,elements:[]},a.push(r)),-1!=s){let t=e.children[s],n=o<t.children.length-1;if(s<e.children.length-1||n){var l=c.children,u=Object.assign(Object.assign({},c),{children:l.slice(s)});if(c.children=l.slice(0,s),r.elements.push(u),n){let e=t.children,r=Object.assign(Object.assign({},t),{children:e.slice(0,o)});c.children.push(r),t.children=e.slice(o)}}}}}let c=null;for(let e=a.length-1;e>=0;e--)null==a[e].sectProps?a[e].sectProps=c:c=a[e].sectProps;return a}renderWrapper(e){return this.createElement("div",{className:`${this.className}-wrapper`},e)}renderDefaultStyle(){var e=this.className;return f(`\n.${e}-wrapper { background: gray; padding: 30px; padding-bottom: 0px; display: flex; flex-flow: column; align-items: center; } \n.${e}-wrapper>section.${e} { background: white; box-shadow: 0 0 10px rgba(0, 0, 0, 0.5); margin-bottom: 30px; }\n.${e} { color: black; hyphens: auto; }\nsection.${e} { box-sizing: border-box; display: flex; flex-flow: column nowrap; position: relative; overflow: hidden; }\nsection.${e}>article { margin-bottom: auto; z-index: 1; }\nsection.${e}>footer { z-index: 1; }\n.${e} table { border-collapse: collapse; }\n.${e} table td, .${e} table th { vertical-align: top; }\n.${e} p { margin: 0pt; min-height: 1em; }\n.${e} span { white-space: pre-wrap; overflow-wrap: break-word; }\n.${e} a { color: inherit; text-decoration: inherit; }\n`)}renderNumbering(e,t){var r="",n=[];for(var a of e){var i=`p.${this.numberingClass(a.id,a.level)}`,s="none";if(a.bullet){let e=`--${this.className}-${a.bullet.src}`.toLowerCase();r+=this.styleToString(`${i}:before`,{content:"' '",display:"inline-block",background:`var(${e})`},a.bullet.style),this.document.loadNumberingImage(a.bullet.src).then((r=>{var n=`${this.rootSelector} { ${e}: url(${r}) }`;t.appendChild(f(n))}))}else if(a.levelText){let e=this.numberingCounter(a.id,a.level);const t=e+" "+(a.start-1);a.level>0&&(r+=this.styleToString(`p.${this.numberingClass(a.id,a.level-1)}`,{"counter-reset":t})),n.push(t),r+=this.styleToString(`${i}:before`,Object.assign({content:this.levelTextToContent(a.levelText,a.suff,a.id,this.numFormatToCssValue(a.format)),"counter-increment":e},a.rStyle))}else s=this.numFormatToCssValue(a.format);r+=this.styleToString(i,Object.assign({display:"list-item","list-style-position":"inside","list-style-type":s},a.pStyle))}return n.length>0&&(r+=this.styleToString(this.rootSelector,{"counter-reset":n.join(" ")})),f(r)}renderStyles(e){var t,r="";const n=this.styleMap,i=(0,a.keyBy)(e.filter((e=>e.isDefault)),(e=>e.target));for(const a of e){var s=a.styles;if(a.linked){var o=a.linked&&n[a.linked];o?s=s.concat(o.styles):this.options.debug&&console.warn(`Can't find linked style ${a.linked}`)}for(const e of s){var l=`${null!==(t=a.target)&&void 0!==t?t:""}.${a.cssName}`;a.target!=e.target&&(l+=` ${e.target}`),i[a.target]==a&&(l=`.${this.className} ${a.target}, `+l),r+=this.styleToString(l,e.values)}}return f(r)}renderNotes(e,t,r){var n=e.map((e=>t[e])).filter((e=>e));if(n.length>0){var a=this.createElement("ol",null,this.renderElements(n));r.appendChild(a)}}renderElement(e){switch(e.type){case n.DomType.Paragraph:return this.renderParagraph(e);case n.DomType.BookmarkStart:return this.renderBookmarkStart(e);case n.DomType.BookmarkEnd:return null;case n.DomType.Run:return this.renderRun(e);case n.DomType.Table:return this.renderTable(e);case n.DomType.Row:return this.renderTableRow(e);case n.DomType.Cell:return this.renderTableCell(e);case n.DomType.Hyperlink:return this.renderHyperlink(e);case n.DomType.Drawing:return this.renderDrawing(e);case n.DomType.Image:return this.renderImage(e);case n.DomType.Text:case n.DomType.Text:return this.renderText(e);case n.DomType.DeletedText:return this.renderDeletedText(e);case n.DomType.Tab:return this.renderTab(e);case n.DomType.Symbol:return this.renderSymbol(e);case n.DomType.Break:return this.renderBreak(e);case n.DomType.Footer:return this.renderContainer(e,"footer");case n.DomType.Header:return this.renderContainer(e,"header");case n.DomType.Footnote:case n.DomType.Endnote:return this.renderContainer(e,"li");case n.DomType.FootnoteReference:return this.renderFootnoteReference(e);case n.DomType.EndnoteReference:return this.renderEndnoteReference(e);case n.DomType.NoBreakHyphen:return this.createElement("wbr");case n.DomType.VmlPicture:return this.renderVmlPicture(e);case n.DomType.VmlElement:return this.renderVmlElement(e);case n.DomType.MmlMath:return this.renderContainerNS(e,o,"math",{xmlns:o});case n.DomType.MmlMathParagraph:return this.renderContainer(e,"span");case n.DomType.MmlFraction:return this.renderContainerNS(e,o,"mfrac");case n.DomType.MmlBase:return this.renderContainerNS(e,o,e.parent.type==n.DomType.MmlMatrixRow?"mtd":"mrow");case n.DomType.MmlNumerator:case n.DomType.MmlDenominator:case n.DomType.MmlFunction:case n.DomType.MmlLimit:case n.DomType.MmlBox:return this.renderContainerNS(e,o,"mrow");case n.DomType.MmlGroupChar:return this.renderMmlGroupChar(e);case n.DomType.MmlLimitLower:return this.renderContainerNS(e,o,"munder");case n.DomType.MmlMatrix:return this.renderContainerNS(e,o,"mtable");case n.DomType.MmlMatrixRow:return this.renderContainerNS(e,o,"mtr");case n.DomType.MmlRadical:return this.renderMmlRadical(e);case n.DomType.MmlSuperscript:return this.renderContainerNS(e,o,"msup");case n.DomType.MmlSubscript:return this.renderContainerNS(e,o,"msub");case n.DomType.MmlDegree:case n.DomType.MmlSuperArgument:case n.DomType.MmlSubArgument:return this.renderContainerNS(e,o,"mn");case n.DomType.MmlFunctionName:return this.renderContainerNS(e,o,"ms");case n.DomType.MmlDelimiter:return this.renderMmlDelimiter(e);case n.DomType.MmlRun:return this.renderMmlRun(e);case n.DomType.MmlNary:return this.renderMmlNary(e);case n.DomType.MmlPreSubSuper:return this.renderMmlPreSubSuper(e);case n.DomType.MmlBar:return this.renderMmlBar(e);case n.DomType.MmlEquationArray:return this.renderMllList(e);case n.DomType.Inserted:return this.renderInserted(e);case n.DomType.Deleted:return this.renderDeleted(e)}return null}renderChildren(e,t){return this.renderElements(e.children,t)}renderElements(e,t){if(null==e)return null;var r=e.flatMap((e=>this.renderElement(e))).filter((e=>null!=e));return t&&d(t,r),r}renderContainer(e,t,r){return this.createElement(t,r,this.renderChildren(e))}renderContainerNS(e,t,r,n){return c(t,r,n,this.renderChildren(e))}renderParagraph(e){var t,r,n,a,i=this.createElement("p");const s=this.findStyle(e.styleName);null!==(t=e.tabs)&&void 0!==t||(e.tabs=null===(r=null==s?void 0:s.paragraphProps)||void 0===r?void 0:r.tabs),this.renderClass(e,i),this.renderChildren(e,i),this.renderStyleValues(e.cssStyle,i),this.renderCommonProperties(i.style,e);const o=null!==(n=e.numbering)&&void 0!==n?n:null===(a=null==s?void 0:s.paragraphProps)||void 0===a?void 0:a.numbering;return o&&i.classList.add(this.numberingClass(o.id,o.level)),i}renderRunProperties(e,t){this.renderCommonProperties(e,t)}renderCommonProperties(e,t){null!=t&&(t.color&&(e.color=t.color),t.fontSize&&(e["font-size"]=t.fontSize))}renderHyperlink(e){var t=this.createElement("a");if(this.renderChildren(e,t),this.renderStyleValues(e.cssStyle,t),e.href)t.href=e.href;else if(e.id){const r=this.document.documentPart.rels.find((t=>t.id==e.id&&"External"===t.targetMode));t.href=null==r?void 0:r.target}return t}renderDrawing(e){var t=this.createElement("div");return t.style.display="inline-block",t.style.position="relative",t.style.textIndent="0px",this.renderChildren(e,t),this.renderStyleValues(e.cssStyle,t),t}renderImage(e){let t=this.createElement("img");return this.renderStyleValues(e.cssStyle,t),this.document&&this.document.loadDocumentImage(e.src,this.currentPart).then((e=>{t.src=e})),t}renderText(e){return this.htmlDocument.createTextNode(e.text)}renderDeletedText(e){return this.options.renderEndnotes?this.htmlDocument.createTextNode(e.text):null}renderBreak(e){return"textWrapping"==e.break?this.createElement("br"):null}renderInserted(e){return this.options.renderChanges?this.renderContainer(e,"ins"):this.renderChildren(e)}renderDeleted(e){return this.options.renderChanges?this.renderContainer(e,"del"):null}renderSymbol(e){var t=this.createElement("span");return t.style.fontFamily=e.font,t.innerHTML=`&#x${e.char};`,t}renderFootnoteReference(e){var t=this.createElement("sup");return this.currentFootnoteIds.push(e.id),t.textContent=`${this.currentFootnoteIds.length}`,t}renderEndnoteReference(e){var t=this.createElement("sup");return this.currentEndnoteIds.push(e.id),t.textContent=`${this.currentEndnoteIds.length}`,t}renderTab(e){var t,r=this.createElement("span");if(r.innerHTML="&emsp;",this.options.experimental){r.className=this.tabStopClass();var a=null===(t=function(e,t){for(var r=e.parent;null!=r&&r.type!=t;)r=r.parent;return r}(e,n.DomType.Paragraph))||void 0===t?void 0:t.tabs;this.currentTabs.push({stops:a,span:r})}return r}renderBookmarkStart(e){var t=this.createElement("span");return t.id=e.name,t}renderRun(e){if(e.fieldRun)return null;const t=this.createElement("span");if(e.id&&(t.id=e.id),this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),e.verticalAlign){const r=this.createElement(e.verticalAlign);this.renderChildren(e,r),t.appendChild(r)}else this.renderChildren(e,t);return t}renderTable(e){let t=this.createElement("table");return this.tableCellPositions.push(this.currentCellPosition),this.tableVerticalMerges.push(this.currentVerticalMerge),this.currentVerticalMerge={},this.currentCellPosition={col:0,row:0},e.columns&&t.appendChild(this.renderTableColumns(e.columns)),this.renderClass(e,t),this.renderChildren(e,t),this.renderStyleValues(e.cssStyle,t),this.currentVerticalMerge=this.tableVerticalMerges.pop(),this.currentCellPosition=this.tableCellPositions.pop(),t}renderTableColumns(e){let t=this.createElement("colgroup");for(let r of e){let e=this.createElement("col");r.width&&(e.style.width=r.width),t.appendChild(e)}return t}renderTableRow(e){let t=this.createElement("tr");return this.currentCellPosition.col=0,this.renderClass(e,t),this.renderChildren(e,t),this.renderStyleValues(e.cssStyle,t),this.currentCellPosition.row++,t}renderTableCell(e){let t=this.createElement("td");const r=this.currentCellPosition.col;return e.verticalMerge?"restart"==e.verticalMerge?(this.currentVerticalMerge[r]=t,t.rowSpan=1):this.currentVerticalMerge[r]&&(this.currentVerticalMerge[r].rowSpan+=1,t.style.display="none"):this.currentVerticalMerge[r]=null,this.renderClass(e,t),this.renderChildren(e,t),this.renderStyleValues(e.cssStyle,t),e.span&&(t.colSpan=e.span),this.currentCellPosition.col+=t.colSpan,t}renderVmlPicture(e){var t=l("div");return this.renderChildren(e,t),t}renderVmlElement(e){var t,r,n=u("svg");n.setAttribute("style",e.cssStyleText);const a=this.renderVmlChildElement(e);return(null===(t=e.imageHref)||void 0===t?void 0:t.id)&&(null===(r=this.document)||void 0===r||r.loadDocumentImage(e.imageHref.id,this.currentPart).then((e=>a.setAttribute("href",e)))),n.appendChild(a),requestAnimationFrame((()=>{const e=n.firstElementChild.getBBox();n.setAttribute("width",`${Math.ceil(e.x+e.width)}`),n.setAttribute("height",`${Math.ceil(e.y+e.height)}`)})),n}renderVmlChildElement(e){const t=u(e.tagName);Object.entries(e.attrs).forEach((([e,r])=>t.setAttribute(e,r)));for(let r of e.children)r.type==n.DomType.VmlElement?t.appendChild(this.renderVmlChildElement(r)):t.appendChild(...(0,a.asArray)(this.renderElement(r)));return t}renderMmlRadical(e){var t;const r=e.children.find((e=>e.type==n.DomType.MmlBase));if(null===(t=e.props)||void 0===t?void 0:t.hideDegree)return c(o,"msqrt",null,this.renderElements([r]));const a=e.children.find((e=>e.type==n.DomType.MmlDegree));return c(o,"mroot",null,this.renderElements([r,a]))}renderMmlDelimiter(e){var t,r;const n=[];return n.push(c(o,"mo",null,[null!==(t=e.props.beginChar)&&void 0!==t?t:"("])),n.push(...this.renderElements(e.children)),n.push(c(o,"mo",null,[null!==(r=e.props.endChar)&&void 0!==r?r:")"])),c(o,"mrow",null,n)}renderMmlNary(e){var t,r;const i=[],s=(0,a.keyBy)(e.children,(e=>e.type)),l=s[n.DomType.MmlSuperArgument],u=s[n.DomType.MmlSubArgument],h=l?c(o,"mo",null,(0,a.asArray)(this.renderElement(l))):null,d=u?c(o,"mo",null,(0,a.asArray)(this.renderElement(u))):null,f=c(o,"mo",null,[null!==(r=null===(t=e.props)||void 0===t?void 0:t.char)&&void 0!==r?r:"∫"]);return h||d?i.push(c(o,"munderover",null,[f,d,h])):h?i.push(c(o,"mover",null,[f,h])):d?i.push(c(o,"munder",null,[f,d])):i.push(f),i.push(...this.renderElements(s[n.DomType.MmlBase].children)),c(o,"mrow",null,i)}renderMmlPreSubSuper(e){const t=[],r=(0,a.keyBy)(e.children,(e=>e.type)),i=r[n.DomType.MmlSuperArgument],s=r[n.DomType.MmlSubArgument],l=i?c(o,"mo",null,(0,a.asArray)(this.renderElement(i))):null,u=s?c(o,"mo",null,(0,a.asArray)(this.renderElement(s))):null,h=c(o,"mo",null);return t.push(c(o,"msubsup",null,[h,u,l])),t.push(...this.renderElements(r[n.DomType.MmlBase].children)),c(o,"mrow",null,t)}renderMmlGroupChar(e){const t="bot"===e.props.verticalJustification?"mover":"munder",r=this.renderContainerNS(e,o,t);return e.props.char&&r.appendChild(c(o,"mo",null,[e.props.char])),r}renderMmlBar(e){const t=this.renderContainerNS(e,o,"mrow");switch(e.props.position){case"top":t.style.textDecoration="overline";break;case"bottom":t.style.textDecoration="underline"}return t}renderMmlRun(e){const t=c(o,"ms");return this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),this.renderChildren(e,t),t}renderMllList(e){const t=c(o,"mtable");this.renderClass(e,t),this.renderStyleValues(e.cssStyle,t),this.renderChildren(e);for(let r of this.renderChildren(e))t.appendChild(c(o,"mtr",null,[c(o,"mtd",null,[r])]));return t}renderStyleValues(e,t){for(let r in e)r.startsWith("$")?t.setAttribute(r.slice(1),e[r]):t.style[r]=e[r]}renderClass(e,t){e.className&&(t.className=e.className),e.styleName&&t.classList.add(this.processStyleName(e.styleName))}findStyle(e){var t;return e&&(null===(t=this.styleMap)||void 0===t?void 0:t[e])}numberingClass(e,t){return`${this.className}-num-${e}-${t}`}tabStopClass(){return`${this.className}-tab-stop`}styleToString(e,t,r=null){let n=`${e} {\r\n`;for(const e in t)e.startsWith("$")||(n+=`  ${e}: ${t[e]};\r\n`);return r&&(n+=r),n+"}\r\n"}numberingCounter(e,t){return`${this.className}-num-${e}-${t}`}levelTextToContent(e,t,r,n){var a;return`"${e.replace(/%\d*/g,(e=>{let t=parseInt(e.substring(1),10)-1;return`"counter(${this.numberingCounter(r,t)}, ${n})"`}))}${null!==(a={tab:"\\9",space:"\\a0"}[t])&&void 0!==a?a:""}"`}numFormatToCssValue(e){var t;return null!==(t={none:"none",bullet:"disc",decimal:"decimal",lowerLetter:"lower-alpha",upperLetter:"upper-alpha",lowerRoman:"lower-roman",upperRoman:"upper-roman",decimalZero:"decimal-leading-zero",aiueo:"katakana",aiueoFullWidth:"katakana",chineseCounting:"simp-chinese-informal",chineseCountingThousand:"simp-chinese-informal",chineseLegalSimplified:"simp-chinese-formal",chosung:"hangul-consonant",ideographDigital:"cjk-ideographic",ideographTraditional:"cjk-heavenly-stem",ideographLegalTraditional:"trad-chinese-formal",ideographZodiac:"cjk-earthly-branch",iroha:"katakana-iroha",irohaFullWidth:"katakana-iroha",japaneseCounting:"japanese-informal",japaneseDigitalTenThousand:"cjk-decimal",japaneseLegal:"japanese-formal",thaiNumbers:"thai",koreanCounting:"korean-hangul-formal",koreanDigital:"korean-hangul-formal",koreanDigital2:"korean-hanja-informal",hebrew1:"hebrew",hebrew2:"hebrew",hindiNumbers:"devanagari",ganada:"hangul",taiwaneseCounting:"cjk-ideographic",taiwaneseCountingThousand:"cjk-ideographic",taiwaneseDigital:"cjk-decimal"}[e])&&void 0!==t?t:e}refreshTabStops(){this.options.experimental&&(clearTimeout(this.tabsTimeout),this.tabsTimeout=setTimeout((()=>{const e=(0,i.computePixelToPoint)();for(let t of this.currentTabs)(0,i.updateTabStop)(t.span,t.stops,this.defaultTabSize,e)}),500))}}},630:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.updateTabStop=t.computePixelToPoint=void 0;const r={pos:0,leader:"none",style:"left"};function n(e){return parseFloat(e)}t.computePixelToPoint=function(e=document.body){const t=document.createElement("div");t.style.width="100pt",e.appendChild(t);const r=100/t.offsetWidth;return e.removeChild(t),r},t.updateTabStop=function(e,t,a,i=.75){const s=e.closest("p"),o=e.getBoundingClientRect(),l=s.getBoundingClientRect(),u=getComputedStyle(s),c=(null==t?void 0:t.length)>0?t.map((e=>({pos:n(e.position),leader:e.leader,style:e.style}))).sort(((e,t)=>e.pos-t.pos)):[r],h=c[c.length-1],d=l.width*i,f=n(a);let p=h.pos+f;if(p<d)for(;p<d&&c.length<50;p+=f)c.push(Object.assign(Object.assign({},r),{pos:p}));const m=parseFloat(u.marginLeft),g=l.left+m,b=(o.left-g)*i,y=c.find((e=>"clear"!=e.style&&e.pos>b));if(null==y)return;let v=1;if("right"==y.style||"center"==y.style){const t=Array.from(s.querySelectorAll(`.${e.className}`)),r=t.indexOf(e)+1,n=document.createRange();n.setStart(e,1),r<t.length?n.setEndBefore(t[r]):n.setEndAfter(s);const a="center"==y.style?.5:1,o=n.getBoundingClientRect(),u=o.left+a*o.width-(l.left-m);v=y.pos-u*i}else v=y.pos-b;switch(e.innerHTML="&nbsp;",e.style.textDecoration="inherit",e.style.wordSpacing=`${v.toFixed(0)}pt`,y.leader){case"dot":case"middleDot":e.style.textDecoration="underline",e.style.textDecorationStyle="dotted";break;case"hyphen":case"heavy":case"underscore":e.style.textDecoration="underline"}}},881:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WmlEndnote=t.WmlFootnote=t.WmlBaseNote=void 0;const n=r(120);class a{}t.WmlBaseNote=a,t.WmlFootnote=class extends a{constructor(){super(...arguments),this.type=n.DomType.Footnote}},t.WmlEndnote=class extends a{constructor(){super(...arguments),this.type=n.DomType.Endnote}}},735:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EndnotesPart=t.FootnotesPart=t.BaseNotePart=void 0;const n=r(530),a=r(881);class i extends n.Part{constructor(e,t,r){super(e,t),this._documentParser=r}}t.BaseNotePart=i,t.FootnotesPart=class extends i{constructor(e,t,r){super(e,t,r)}parseXml(e){this.notes=this._documentParser.parseNotes(e,"footnote",a.WmlFootnote)}},t.EndnotesPart=class extends i{constructor(e,t,r){super(e,t,r)}parseXml(e){this.notes=this._documentParser.parseNotes(e,"endnote",a.WmlEndnote)}}},527:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NumberingPart=void 0;const n=r(530),a=r(682);class i extends n.Part{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){Object.assign(this,(0,a.parseNumberingPart)(e,this._package.xmlParser)),this.domNumberings=this._documentParser.parseNumberingFile(e)}}t.NumberingPart=i},682:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseNumberingBulletPicture=t.parseNumberingLevelOverrride=t.parseNumberingLevel=t.parseAbstractNumbering=t.parseNumbering=t.parseNumberingPart=void 0;const n=r(109),a=r(488);function i(e,t){let r={id:t.attr(e,"numId"),overrides:[]};for(let n of t.elements(e))switch(n.localName){case"abstractNumId":r.abstractId=t.attr(n,"val");break;case"lvlOverride":r.overrides.push(l(n,t))}return r}function s(e,t){let r={id:t.attr(e,"abstractNumId"),levels:[]};for(let n of t.elements(e))switch(n.localName){case"name":r.name=t.attr(n,"val");break;case"multiLevelType":r.multiLevelType=t.attr(n,"val");break;case"numStyleLink":r.numberingStyleLink=t.attr(n,"val");break;case"styleLink":r.styleLink=t.attr(n,"val");break;case"lvl":r.levels.push(o(n,t))}return r}function o(e,t){let r={level:t.intAttr(e,"ilvl")};for(let i of t.elements(e))switch(i.localName){case"start":r.start=t.attr(i,"val");break;case"lvlRestart":r.restart=t.intAttr(i,"val");break;case"numFmt":r.format=t.attr(i,"val");break;case"lvlText":r.text=t.attr(i,"val");break;case"lvlJc":r.justification=t.attr(i,"val");break;case"lvlPicBulletId":r.bulletPictureId=t.attr(i,"val");break;case"pStyle":r.paragraphStyle=t.attr(i,"val");break;case"pPr":r.paragraphProps=(0,n.parseParagraphProperties)(i,t);break;case"rPr":r.runProps=(0,a.parseRunProperties)(i,t)}return r}function l(e,t){let r={level:t.intAttr(e,"ilvl")};for(let n of t.elements(e))switch(n.localName){case"startOverride":r.start=t.intAttr(n,"val");break;case"lvl":r.numberingLevel=o(n,t)}return r}function u(e,t){var r=t.element(e,"pict"),n=r&&t.element(r,"shape"),a=n&&t.element(n,"imagedata");return a?{id:t.attr(e,"numPicBulletId"),referenceId:t.attr(a,"id"),style:t.attr(n,"style")}:null}t.parseNumberingPart=function(e,t){let r={numberings:[],abstractNumberings:[],bulletPictures:[]};for(let n of t.elements(e))switch(n.localName){case"num":r.numberings.push(i(n,t));break;case"abstractNum":r.abstractNumberings.push(s(n,t));break;case"numPicBullet":r.bulletPictures.push(u(n,t))}return r},t.parseNumbering=i,t.parseAbstractNumbering=s,t.parseNumberingLevel=o,t.parseNumberingLevelOverrride=l,t.parseNumberingBulletPicture=u},472:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.XmlParser=t.serializeXmlString=t.parseXmlString=void 0;const n=r(149);t.parseXmlString=function(e,t=!1){var r;t&&(e=e.replace(/<[?].*[?]>/,"")),e=65279===(r=e).charCodeAt(0)?r.substring(1):r;const n=(new DOMParser).parseFromString(e,"application/xml"),a=null===(i=n.getElementsByTagName("parsererror")[0])||void 0===i?void 0:i.textContent;var i;if(a)throw new Error(a);return n},t.serializeXmlString=function(e){return(new XMLSerializer).serializeToString(e)};class a{elements(e,t=null){const r=[];for(let n=0,a=e.childNodes.length;n<a;n++){let a=e.childNodes.item(n);1!=a.nodeType||null!=t&&a.localName!=t||r.push(a)}return r}element(e,t){for(let r=0,n=e.childNodes.length;r<n;r++){let n=e.childNodes.item(r);if(1==n.nodeType&&n.localName==t)return n}return null}elementAttr(e,t,r){var n=this.element(e,t);return n?this.attr(n,r):void 0}attrs(e){return Array.from(e.attributes)}attr(e,t){for(let r=0,n=e.attributes.length;r<n;r++){let n=e.attributes.item(r);if(n.localName==t)return n.value}return null}intAttr(e,t,r=null){var n=this.attr(e,t);return n?parseInt(n):r}hexAttr(e,t,r=null){var n=this.attr(e,t);return n?parseInt(n,16):r}floatAttr(e,t,r=null){var n=this.attr(e,t);return n?parseFloat(n):r}boolAttr(e,t,r=null){return(0,n.convertBoolean)(this.attr(e,t),r)}lengthAttr(e,t,r=n.LengthUsage.Dxa){return(0,n.convertLength)(this.attr(e,t),r)}}t.XmlParser=a;const i=new a;t.default=i},287:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SettingsPart=void 0;const n=r(530),a=r(846);class i extends n.Part{constructor(e,t){super(e,t)}parseXml(e){this.settings=(0,a.parseSettings)(e,this._package.xmlParser)}}t.SettingsPart=i},846:(e,t)=>{function r(e,t){var r={defaultNoteIds:[]};for(let n of t.elements(e))switch(n.localName){case"numFmt":r.nummeringFormat=t.attr(n,"val");break;case"footnote":case"endnote":r.defaultNoteIds.push(t.attr(n,"id"))}return r}Object.defineProperty(t,"__esModule",{value:!0}),t.parseNoteProperties=t.parseSettings=void 0,t.parseSettings=function(e,t){var n={};for(let a of t.elements(e))switch(a.localName){case"defaultTabStop":n.defaultTabStop=t.lengthAttr(a,"val");break;case"footnotePr":n.footnoteProps=r(a,t);break;case"endnotePr":n.endnoteProps=r(a,t);break;case"autoHyphenation":n.autoHyphenation=t.boolAttr(a,"val")}return n},t.parseNoteProperties=r},240:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StylesPart=void 0;const n=r(530);class a extends n.Part{constructor(e,t,r){super(e,t),this._documentParser=r}parseXml(e){this.styles=this._documentParser.parseStylesFile(e)}}t.StylesPart=a},893:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ThemePart=void 0;const n=r(530),a=r(55);class i extends n.Part{constructor(e,t){super(e,t)}parseXml(e){this.theme=(0,a.parseTheme)(e,this._package.xmlParser)}}t.ThemePart=i},55:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseFontInfo=t.parseFontScheme=t.parseColorScheme=t.parseTheme=t.DmlTheme=void 0;class r{}function n(e,t){var r={name:t.attr(e,"name"),colors:{}};for(let i of t.elements(e)){var n=t.element(i,"srgbClr"),a=t.element(i,"sysClr");n?r.colors[i.localName]=t.attr(n,"val"):a&&(r.colors[i.localName]=t.attr(a,"lastClr"))}return r}function a(e,t){var r={name:t.attr(e,"name")};for(let n of t.elements(e))switch(n.localName){case"majorFont":r.majorFont=i(n,t);break;case"minorFont":r.minorFont=i(n,t)}return r}function i(e,t){return{latinTypeface:t.elementAttr(e,"latin","typeface"),eaTypeface:t.elementAttr(e,"ea","typeface"),csTypeface:t.elementAttr(e,"cs","typeface")}}t.DmlTheme=r,t.parseTheme=function(e,t){var i=new r,s=t.element(e,"themeElements");for(let e of t.elements(s))switch(e.localName){case"clrScheme":i.colorScheme=n(e,t);break;case"fontScheme":i.fontScheme=a(e,t)}return i},t.parseColorScheme=n,t.parseFontScheme=a,t.parseFontInfo=i},593:(e,t)=>{function r(e){return e&&"object"==typeof e&&!Array.isArray(e)}Object.defineProperty(t,"__esModule",{value:!0}),t.asArray=t.formatCssRules=t.parseCssRules=t.mergeDeep=t.isString=t.isObject=t.blobToBase64=t.keyBy=t.resolvePath=t.splitPath=t.escapeClassName=void 0,t.escapeClassName=function(e){return null==e?void 0:e.replace(/[ .]+/g,"-").replace(/[&]+/g,"and").toLowerCase()},t.splitPath=function(e){let t=e.lastIndexOf("/")+1;return[0==t?"":e.substring(0,t),0==t?e:e.substring(t)]},t.resolvePath=function(e,t){try{const r="http://docx/";return new URL(e,r+t).toString().substring(r.length)}catch(r){return`${t}${e}`}},t.keyBy=function(e,t){return e.reduce(((e,r)=>(e[t(r)]=r,e)),{})},t.blobToBase64=function(e){return new Promise(((t,r)=>{const n=new FileReader;n.onloadend=()=>t(n.result),n.onerror=()=>r(),n.readAsDataURL(e)}))},t.isObject=r,t.isString=function(e){return"string"==typeof e||e instanceof String},t.mergeDeep=function e(t,...n){var a;if(!n.length)return t;const i=n.shift();if(r(t)&&r(i))for(const n in i)r(i[n])?e(null!==(a=t[n])&&void 0!==a?a:t[n]={},i[n]):t[n]=i[n];return e(t,...n)},t.parseCssRules=function(e){const t={};for(const r of e.split(";")){const[e,n]=r.split(":");t[e]=n}return t},t.formatCssRules=function(e){return Object.entries(e).map(((e,t)=>`${e}: ${t}`)).join(";")},t.asArray=function(e){return Array.isArray(e)?e:[e]}},320:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.parseVmlElement=t.VmlElement=void 0;const n=r(149),a=r(120),i=r(472);class s extends a.OpenXmlElementBase{constructor(){super(...arguments),this.type=a.DomType.VmlElement,this.attrs={}}}function o(e){var t;return{stroke:i.default.attr(e,"color"),"stroke-width":null!==(t=i.default.lengthAttr(e,"weight",n.LengthUsage.Emu))&&void 0!==t?t:"1px"}}function l(e){return e.split(",")}t.VmlElement=s,t.parseVmlElement=function e(t,r){var n=new s;switch(t.localName){case"rect":n.tagName="rect",Object.assign(n.attrs,{width:"100%",height:"100%"});break;case"oval":n.tagName="ellipse",Object.assign(n.attrs,{cx:"50%",cy:"50%",rx:"50%",ry:"50%"});break;case"line":n.tagName="line";break;case"shape":n.tagName="g";break;case"textbox":n.tagName="foreignObject",Object.assign(n.attrs,{width:"100%",height:"100%"});break;default:return null}for(const e of i.default.attrs(t))switch(e.localName){case"style":n.cssStyleText=e.value;break;case"fillcolor":n.attrs.fill=e.value;break;case"from":const[t,r]=l(e.value);Object.assign(n.attrs,{x1:t,y1:r});break;case"to":const[a,i]=l(e.value);Object.assign(n.attrs,{x2:a,y2:i})}for(const a of i.default.elements(t))switch(a.localName){case"stroke":Object.assign(n.attrs,o(a));break;case"fill":Object.assign(n.attrs,{});break;case"imagedata":n.tagName="image",Object.assign(n.attrs,{width:"100%",height:"100%"}),n.imageHref={id:i.default.attr(a,"id"),title:i.default.attr(a,"title")};break;case"txbxContent":n.children.push(...r.parseBodyElements(a));break;default:const t=e(a,r);t&&n.children.push(t)}return n}},213:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.deobfuscate=t.WordDocument=void 0;const n=r(461),a=r(380),i=r(522),s=r(448),o=r(593),l=r(527),u=r(240),c=r(985),h=r(665),d=r(162),f=r(893),p=r(735),m=r(287),g=r(177),b=[{type:n.RelationshipTypes.OfficeDocument,target:"word/document.xml"},{type:n.RelationshipTypes.ExtendedProperties,target:"docProps/app.xml"},{type:n.RelationshipTypes.CoreProperties,target:"docProps/core.xml"},{type:n.RelationshipTypes.CustomProperties,target:"docProps/custom.xml"}];class y{constructor(){this.parts=[],this.partsMap={}}static async load(e,t,r){var n=new y;return n._options=r,n._parser=t,n._package=await i.OpenXmlPackage.load(e,r),n.rels=await n._package.loadRelationships(),await Promise.all(b.map((e=>{var t;const r=null!==(t=n.rels.find((t=>t.type===e.type)))&&void 0!==t?t:e;return n.loadRelationshipPart(r.target,r.type)}))),n}save(e="blob"){return this._package.save(e)}async loadRelationshipPart(e,t){var r;if(this.partsMap[e])return this.partsMap[e];if(!this._package.get(e))return null;let i=null;switch(t){case n.RelationshipTypes.OfficeDocument:this.documentPart=i=new s.DocumentPart(this._package,e,this._parser);break;case n.RelationshipTypes.FontTable:this.fontTablePart=i=new a.FontTablePart(this._package,e);break;case n.RelationshipTypes.Numbering:this.numberingPart=i=new l.NumberingPart(this._package,e,this._parser);break;case n.RelationshipTypes.Styles:this.stylesPart=i=new u.StylesPart(this._package,e,this._parser);break;case n.RelationshipTypes.Theme:this.themePart=i=new f.ThemePart(this._package,e);break;case n.RelationshipTypes.Footnotes:this.footnotesPart=i=new p.FootnotesPart(this._package,e,this._parser);break;case n.RelationshipTypes.Endnotes:this.endnotesPart=i=new p.EndnotesPart(this._package,e,this._parser);break;case n.RelationshipTypes.Footer:i=new c.FooterPart(this._package,e,this._parser);break;case n.RelationshipTypes.Header:i=new c.HeaderPart(this._package,e,this._parser);break;case n.RelationshipTypes.CoreProperties:this.corePropsPart=i=new d.CorePropsPart(this._package,e);break;case n.RelationshipTypes.ExtendedProperties:this.extendedPropsPart=i=new h.ExtendedPropsPart(this._package,e);break;case n.RelationshipTypes.CustomProperties:i=new g.CustomPropsPart(this._package,e);break;case n.RelationshipTypes.Settings:this.settingsPart=i=new m.SettingsPart(this._package,e)}if(null==i)return Promise.resolve(null);if(this.partsMap[e]=i,this.parts.push(i),await i.load(),(null===(r=i.rels)||void 0===r?void 0:r.length)>0){const[e]=(0,o.splitPath)(i.path);await Promise.all(i.rels.map((t=>this.loadRelationshipPart((0,o.resolvePath)(t.target,e),t.type))))}return i}async loadDocumentImage(e,t){const r=await this.loadResource(null!=t?t:this.documentPart,e,"blob");return this.blobToURL(r)}async loadNumberingImage(e){const t=await this.loadResource(this.numberingPart,e,"blob");return this.blobToURL(t)}async loadFont(e,t){const r=await this.loadResource(this.fontTablePart,e,"uint8array");return r?this.blobToURL(new Blob([v(r,t)])):r}blobToURL(e){return e?this._options.useBase64URL?(0,o.blobToBase64)(e):URL.createObjectURL(e):null}findPartByRelId(e,t=null){var r,n=(null!==(r=t.rels)&&void 0!==r?r:this.rels).find((t=>t.id==e));const a=t?(0,o.splitPath)(t.path)[0]:"";return n?this.partsMap[(0,o.resolvePath)(n.target,a)]:null}getPathById(e,t){const r=e.rels.find((e=>e.id==t)),[n]=(0,o.splitPath)(e.path);return r?(0,o.resolvePath)(r.target,n):null}loadResource(e,t,r){const n=this.getPathById(e,t);return n?this._package.load(n,r):Promise.resolve(null)}}function v(e,t){const r=t.replace(/{|}|-/g,""),n=new Array(16);for(let e=0;e<16;e++)n[16-e-1]=parseInt(r.substr(2*e,2),16);for(let t=0;t<32;t++)e[t]=e[t]^n[t%16];return e}t.WordDocument=y,t.deobfuscate=v},583:e=>{e.exports="data:image/svg+xml,%3Csvg xmlns=%27http://www.w3.org/2000/svg%27 viewBox=%270 0 20 100%27 preserveAspectRatio=%27none%27%3E%3Cpath d=%27m0,75 l5,0 l5,25 l10,-100%27 stroke=%27black%27 fill=%27none%27 vector-effect=%27non-scaling-stroke%27/%3E%3C/svg%3E"},626:t=>{t.exports=e}},r={};function n(e){var a=r[e];if(void 0!==a)return a.exports;var i=r[e]={id:e,exports:{}};return t[e](i,i.exports,n),i.exports}return n.m=t,n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.b=document.baseURI||self.location.href,n(667)})(),e.exports=r(function(){if(Sr)return kr;function e(){if(!(this instanceof e))return new e;if(arguments.length)throw new Error("The constructor with parameters has been removed in JSZip 3.0, please check the upgrade guide.");this.files=Object.create(null),this.comment=null,this.root="",this.clone=function(){var t=new e;for(var r in this)"function"!=typeof this[r]&&(t[r]=this[r]);return t}}return Sr=1,e.prototype=Tr(),e.prototype.loadAsync=Rr(),e.support=ne(),e.defaults=Ke(),e.version="3.10.1",e.loadAsync=function(t,r){return(new e).loadAsync(t,r)},e.external=be(),kr=e}())}(c);var Or=c.exports;const Ir={ignoreLastRenderedPageBreak:!1};var zr={getData:function(e,t={}){return"string"==typeof e?function(e,t){return fetch(e,t).then((e=>200!==e.status?Promise.reject(e):e))}(e,t):Promise.resolve(e)},render:function(e,t,r={}){if(!e)return t.innerHTML="",Promise.resolve();let n;return e instanceof Blob?n=e:e instanceof Response?n=e.blob():e instanceof ArrayBuffer&&(n=new Blob([e])),Or.renderAsync(n,t,t,{...Ir,...r})},getBlob:async function(e){let t;return e instanceof Blob?t=e:e instanceof Response?t=await e.blob():e instanceof ArrayBuffer&&(t=new Blob([e])),t}};async function Fr(e,t){t&&(t instanceof ArrayBuffer&&(t=new Blob([t])),function(e,t){let r=document.createElement("a");r.download=e,r.style.display="none",r.href=t,document.body.appendChild(r),r.click(),document.body.removeChild(r)}(e,URL.createObjectURL(t)))}class Lr{container=null;wrapper=null;wrapperMain=null;options={};requestOptions={};fileData=null;constructor(e,t={},r={}){this.container=e,this.options=t,this.requestOptions=r,this.createWrapper()}createWrapper(){this.wrapper=document.createElement("div"),this.wrapper.className="vue-office-docx",this.wrapperMain=document.createElement("div"),this.wrapperMain.className="vue-office-docx-main",this.wrapper.appendChild(this.wrapperMain),this.container.appendChild(this.wrapper)}setOptions(e){this.options=e}setRequestOptions(e){this.requestOptions=e}preview(e){return new Promise(((t,r)=>{zr.getData(e,this.requestOptions).then((async e=>{this.fileData=await zr.getBlob(e),zr.render(this.fileData,this.wrapperMain,this.options).then((()=>{t()})).catch((e=>{zr.render("",this.wrapperMain,this.options),r(e)}))})).catch((e=>{zr.render("",this.wrapperMain,this.options),r(e)}))}))}save(e){Fr(e||`js-preview-docx-${(new Date).getTime()}.docx`,this.fileData)}destroy(){this.container.removeChild(this.wrapper),this.container=null,this.wrapper=null,this.wrapperMain=null,this.options=null,this.requestOptions=null}}return{init:function(e,t,r){return new Lr(e,t,r)}}}));
