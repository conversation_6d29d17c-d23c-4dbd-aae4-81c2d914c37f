<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>pdf预览</title>
    <script src="js-preview-lib/pdf.umd.js"></script>
</head>
<body>
<div id="pdf"></div>

<script>
    const myPdfPreviewer = jsPreviewPdf.init(document.getElementById('pdf'), {
        onError: (e)=>{
            console.log('发生错误', e)
        },
        onRendered: ()=>{
            console.log('渲染完成')
        }
    });
    myPdfPreviewer.preview('http://static.shanhuxueyuan.com/test.pdf')
</script>
</body>
</html>