lockfileVersion: '6.0'

settings:
  autoInstallPeers: true
  excludeLinksFromLockfile: false

dependencies:
  '@js-preview/docx':
    specifier: ^1.6.0
    version: registry.npmjs.org/@js-preview/docx@1.6.0
  '@js-preview/excel':
    specifier: ^1.7.3
    version: registry.npmjs.org/@js-preview/excel@1.7.5
  '@js-preview/pdf':
    specifier: ^2.0.1
    version: registry.npmjs.org/@js-preview/pdf@2.0.1
  '@vue-office/docx':
    specifier: ^1.6.0
    version: registry.npmjs.org/@vue-office/docx@1.6.0(vue-demi@0.14.7)(vue@2.7.16)
  '@vue-office/excel':
    specifier: ^1.7.3
    version: registry.npmjs.org/@vue-office/excel@1.7.6(vue-demi@0.14.7)(vue@2.7.16)
  '@vue-office/pdf':
    specifier: ^2.0.1
    version: registry.npmjs.org/@vue-office/pdf@2.0.1(vue-demi@0.14.7)(vue@2.7.16)
  core-js:
    specifier: ^3.8.3
    version: registry.npmjs.org/core-js@3.37.0
  element-ui:
    specifier: ^2.15.14
    version: registry.npmjs.org/element-ui@2.15.14(vue@2.7.16)
  vue:
    specifier: ^2.7.16
    version: registry.npmjs.org/vue@2.7.16
  vue-demi:
    specifier: ^0.14.6
    version: registry.npmjs.org/vue-demi@0.14.7(vue@2.7.16)
  vue-router:
    specifier: ^3.6.5
    version: registry.npmjs.org/vue-router@3.6.5(vue@2.7.16)

devDependencies:
  '@babel/core':
    specifier: ^7.12.16
    version: registry.npmjs.org/@babel/core@7.24.4
  '@babel/eslint-parser':
    specifier: ^7.12.16
    version: registry.npmjs.org/@babel/eslint-parser@7.24.1(@babel/core@7.24.4)(eslint@7.32.0)
  '@vue/cli-plugin-babel':
    specifier: ~5.0.0
    version: registry.npmjs.org/@vue/cli-plugin-babel@5.0.8(@vue/cli-service@5.0.8)(core-js@3.37.0)(vue@2.7.16)
  '@vue/cli-plugin-eslint':
    specifier: ~5.0.0
    version: registry.npmjs.org/@vue/cli-plugin-eslint@5.0.8(@vue/cli-service@5.0.8)(eslint@7.32.0)
  '@vue/cli-service':
    specifier: ~5.0.0
    version: registry.npmjs.org/@vue/cli-service@5.0.8(vue-template-compiler@2.7.16)(vue@2.7.16)
  eslint:
    specifier: ^7.32.0
    version: registry.npmjs.org/eslint@7.32.0
  eslint-plugin-vue:
    specifier: ^8.0.3
    version: registry.npmjs.org/eslint-plugin-vue@8.7.1(eslint@7.32.0)
  vue-template-compiler:
    specifier: ^2.6.14
    version: registry.npmjs.org/vue-template-compiler@2.7.16

packages:

  registry.npmjs.org/@aashutoshrathi/word-wrap@1.2.6:
    resolution: {integrity: sha512-1Yjs2SvM8TflER/OD3cOjhWWOZb58A2t7wpE2S9XfBYTiIl+XFhQG2bjy4Pu1I+EAlCNUzRDYDdFwFYUKvXcIA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@aashutoshrathi/word-wrap/-/word-wrap-1.2.6.tgz}
    name: '@aashutoshrathi/word-wrap'
    version: 1.2.6
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/@achrinza/node-ipc@9.2.8:
    resolution: {integrity: sha512-DSzEEkbMYbAUVlhy7fg+BzccoRuSQzqHbIPGxGv19OJ2WKwS3/9ChAnQcII4g+GujcHhyJ8BUuOVAx/S5uAfQg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@achrinza/node-ipc/-/node-ipc-9.2.8.tgz}
    name: '@achrinza/node-ipc'
    version: 9.2.8
    engines: {node: 8 || 9 || 10 || 11 || 12 || 13 || 14 || 15 || 16 || 17 || 18 || 19 || 20 || 21}
    dependencies:
      '@node-ipc/js-queue': registry.npmjs.org/@node-ipc/js-queue@2.0.3
      event-pubsub: registry.npmjs.org/event-pubsub@4.3.0
      js-message: registry.npmjs.org/js-message@1.0.7
    dev: true

  registry.npmjs.org/@ampproject/remapping@2.3.0:
    resolution: {integrity: sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz}
    name: '@ampproject/remapping'
    version: 2.3.0
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/gen-mapping': registry.npmjs.org/@jridgewell/gen-mapping@0.3.5
      '@jridgewell/trace-mapping': registry.npmjs.org/@jridgewell/trace-mapping@0.3.25
    dev: true

  registry.npmjs.org/@babel/code-frame@7.12.11:
    resolution: {integrity: sha512-Zt1yodBx1UcyiePMSkWnU4hPqhwq7hGi2nFL1LeA3EUl+q2LQx16MISgJ0+z7dnmgvP9QtIleuETGOiOH1RcIw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.12.11.tgz}
    name: '@babel/code-frame'
    version: 7.12.11
    dependencies:
      '@babel/highlight': registry.npmjs.org/@babel/highlight@7.24.2
    dev: true

  registry.npmjs.org/@babel/code-frame@7.24.2:
    resolution: {integrity: sha512-y5+tLQyV8pg3fsiln67BVLD1P13Eg4lh5RW9mF0zUuvLrv9uIQ4MCL+CRT+FTsBlBjcIan6PGsLcBN0m3ClUyQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.24.2.tgz}
    name: '@babel/code-frame'
    version: 7.24.2
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/highlight': registry.npmjs.org/@babel/highlight@7.24.2
      picocolors: registry.npmjs.org/picocolors@1.0.0
    dev: true

  registry.npmjs.org/@babel/compat-data@7.24.4:
    resolution: {integrity: sha512-vg8Gih2MLK+kOkHJp4gBEIkyaIi00jgWot2D9QOmmfLC8jINSOzmCLta6Bvz/JSBCqnegV0L80jhxkol5GWNfQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.24.4.tgz}
    name: '@babel/compat-data'
    version: 7.24.4
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmjs.org/@babel/core@7.24.4:
    resolution: {integrity: sha512-MBVlMXP+kkl5394RBLSxxk/iLTeVGuXTV3cIDXavPpMMqnSnt6apKgan/U8O3USWZCWZT/TbgfEpKa4uMgN4Dg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/core/-/core-7.24.4.tgz}
    name: '@babel/core'
    version: 7.24.4
    engines: {node: '>=6.9.0'}
    dependencies:
      '@ampproject/remapping': registry.npmjs.org/@ampproject/remapping@2.3.0
      '@babel/code-frame': registry.npmjs.org/@babel/code-frame@7.24.2
      '@babel/generator': registry.npmjs.org/@babel/generator@7.24.4
      '@babel/helper-compilation-targets': registry.npmjs.org/@babel/helper-compilation-targets@7.23.6
      '@babel/helper-module-transforms': registry.npmjs.org/@babel/helper-module-transforms@7.23.3(@babel/core@7.24.4)
      '@babel/helpers': registry.npmjs.org/@babel/helpers@7.24.4
      '@babel/parser': registry.npmjs.org/@babel/parser@7.24.4
      '@babel/template': registry.npmjs.org/@babel/template@7.24.0
      '@babel/traverse': registry.npmjs.org/@babel/traverse@7.24.1
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
      convert-source-map: registry.npmjs.org/convert-source-map@2.0.0
      debug: registry.npmjs.org/debug@4.3.4
      gensync: registry.npmjs.org/gensync@1.0.0-beta.2
      json5: registry.npmjs.org/json5@2.2.3
      semver: registry.npmjs.org/semver@6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/eslint-parser@7.24.1(@babel/core@7.24.4)(eslint@7.32.0):
    resolution: {integrity: sha512-d5guuzMlPeDfZIbpQ8+g1NaCNuAGBBGNECh0HVqz1sjOeVLh2CEaifuOysCH18URW6R7pqXINvf5PaR/dC6jLQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/eslint-parser/-/eslint-parser-7.24.1.tgz}
    id: registry.npmjs.org/@babel/eslint-parser/7.24.1
    name: '@babel/eslint-parser'
    version: 7.24.1
    engines: {node: ^10.13.0 || ^12.13.0 || >=14.0.0}
    peerDependencies:
      '@babel/core': ^7.11.0
      eslint: ^7.5.0 || ^8.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@nicolo-ribaudo/eslint-scope-5-internals': registry.npmjs.org/@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1
      eslint: registry.npmjs.org/eslint@7.32.0
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys@2.1.0
      semver: registry.npmjs.org/semver@6.3.1
    dev: true

  registry.npmjs.org/@babel/generator@7.24.4:
    resolution: {integrity: sha512-Xd6+v6SnjWVx/nus+y0l1sxMOTOMBkyL4+BIdbALyatQnAe/SRVjANeDPSCYaX+i1iJmuGSKf3Z+E+V/va1Hvw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/generator/-/generator-7.24.4.tgz}
    name: '@babel/generator'
    version: 7.24.4
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
      '@jridgewell/gen-mapping': registry.npmjs.org/@jridgewell/gen-mapping@0.3.5
      '@jridgewell/trace-mapping': registry.npmjs.org/@jridgewell/trace-mapping@0.3.25
      jsesc: registry.npmjs.org/jsesc@2.5.2
    dev: true

  registry.npmjs.org/@babel/helper-annotate-as-pure@7.22.5:
    resolution: {integrity: sha512-LvBTxu8bQSQkcyKOU+a1btnNFQ1dMAd0R6PyW3arXes06F6QLWLIrd681bxRPIXlrMGR3XYnW9JyML7dP3qgxg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-annotate-as-pure/-/helper-annotate-as-pure-7.22.5.tgz}
    name: '@babel/helper-annotate-as-pure'
    version: 7.22.5
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor@7.22.15:
    resolution: {integrity: sha512-QkBXwGgaoC2GtGZRoma6kv7Szfv06khvhFav67ZExau2RaXzy8MpHSMO2PNoP2XtmQphJQRHFfg77Bq731Yizw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor/-/helper-builder-binary-assignment-operator-visitor-7.22.15.tgz}
    name: '@babel/helper-builder-binary-assignment-operator-visitor'
    version: 7.22.15
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helper-compilation-targets@7.23.6:
    resolution: {integrity: sha512-9JB548GZoQVmzrFgp8o7KxdgkTGm6xs9DW0o/Pim72UDjzr5ObUQ6ZzYPqA+g9OTS2bBQoctLJrky0RDCAWRgQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.23.6.tgz}
    name: '@babel/helper-compilation-targets'
    version: 7.23.6
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/compat-data': registry.npmjs.org/@babel/compat-data@7.24.4
      '@babel/helper-validator-option': registry.npmjs.org/@babel/helper-validator-option@7.23.5
      browserslist: registry.npmjs.org/browserslist@4.23.0
      lru-cache: registry.npmjs.org/lru-cache@5.1.1
      semver: registry.npmjs.org/semver@6.3.1
    dev: true

  registry.npmjs.org/@babel/helper-create-class-features-plugin@7.24.4(@babel/core@7.24.4):
    resolution: {integrity: sha512-lG75yeuUSVu0pIcbhiYMXBXANHrpUPaOfu7ryAzskCgKUHuAxRQI5ssrtmF0X9UXldPlvT0XM/A4F44OXRt6iQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-create-class-features-plugin/-/helper-create-class-features-plugin-7.24.4.tgz}
    id: registry.npmjs.org/@babel/helper-create-class-features-plugin/7.24.4
    name: '@babel/helper-create-class-features-plugin'
    version: 7.24.4
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-annotate-as-pure': registry.npmjs.org/@babel/helper-annotate-as-pure@7.22.5
      '@babel/helper-environment-visitor': registry.npmjs.org/@babel/helper-environment-visitor@7.22.20
      '@babel/helper-function-name': registry.npmjs.org/@babel/helper-function-name@7.23.0
      '@babel/helper-member-expression-to-functions': registry.npmjs.org/@babel/helper-member-expression-to-functions@7.23.0
      '@babel/helper-optimise-call-expression': registry.npmjs.org/@babel/helper-optimise-call-expression@7.22.5
      '@babel/helper-replace-supers': registry.npmjs.org/@babel/helper-replace-supers@7.24.1(@babel/core@7.24.4)
      '@babel/helper-skip-transparent-expression-wrappers': registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers@7.22.5
      '@babel/helper-split-export-declaration': registry.npmjs.org/@babel/helper-split-export-declaration@7.22.6
      semver: registry.npmjs.org/semver@6.3.1
    dev: true

  registry.npmjs.org/@babel/helper-create-regexp-features-plugin@7.22.15(@babel/core@7.24.4):
    resolution: {integrity: sha512-29FkPLFjn4TPEa3RE7GpW+qbE8tlsu3jntNYNfcGsc49LphF1PQIiD+vMZ1z1xVOKt+93khA9tc2JBs3kBjA7w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-create-regexp-features-plugin/-/helper-create-regexp-features-plugin-7.22.15.tgz}
    id: registry.npmjs.org/@babel/helper-create-regexp-features-plugin/7.22.15
    name: '@babel/helper-create-regexp-features-plugin'
    version: 7.22.15
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-annotate-as-pure': registry.npmjs.org/@babel/helper-annotate-as-pure@7.22.5
      regexpu-core: registry.npmjs.org/regexpu-core@5.3.2
      semver: registry.npmjs.org/semver@6.3.1
    dev: true

  registry.npmjs.org/@babel/helper-define-polyfill-provider@0.6.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-o7SDgTJuvx5vLKD6SFvkydkSMBvahDKGiNJzG22IZYXhiqoe9efY7zocICBgzHV4IRg5wdgl2nEL/tulKIEIbA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-define-polyfill-provider/-/helper-define-polyfill-provider-0.6.1.tgz}
    id: registry.npmjs.org/@babel/helper-define-polyfill-provider/0.6.1
    name: '@babel/helper-define-polyfill-provider'
    version: 0.6.1
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-compilation-targets': registry.npmjs.org/@babel/helper-compilation-targets@7.23.6
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      debug: registry.npmjs.org/debug@4.3.4
      lodash.debounce: registry.npmjs.org/lodash.debounce@4.0.8
      resolve: registry.npmjs.org/resolve@1.22.8
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/helper-environment-visitor@7.22.20:
    resolution: {integrity: sha512-zfedSIzFhat/gFhWfHtgWvlec0nqB9YEIVrpuwjruLlXfUSnA8cJB0miHKwqDnQ7d32aKo2xt88/xZptwxbfhA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-environment-visitor/-/helper-environment-visitor-7.22.20.tgz}
    name: '@babel/helper-environment-visitor'
    version: 7.22.20
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmjs.org/@babel/helper-function-name@7.23.0:
    resolution: {integrity: sha512-OErEqsrxjZTJciZ4Oo+eoZqeW9UIiOcuYKRJA4ZAgV9myA+pOXhhmpfNCKjEH/auVfEYVFJ6y1Tc4r0eIApqiw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-function-name/-/helper-function-name-7.23.0.tgz}
    name: '@babel/helper-function-name'
    version: 7.23.0
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': registry.npmjs.org/@babel/template@7.24.0
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helper-hoist-variables@7.22.5:
    resolution: {integrity: sha512-wGjk9QZVzvknA6yKIUURb8zY3grXCcOZt+/7Wcy8O2uctxhplmUPkOdlgoNhmdVee2c92JXbf1xpMtVNbfoxRw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-hoist-variables/-/helper-hoist-variables-7.22.5.tgz}
    name: '@babel/helper-hoist-variables'
    version: 7.22.5
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helper-member-expression-to-functions@7.23.0:
    resolution: {integrity: sha512-6gfrPwh7OuT6gZyJZvd6WbTfrqAo7vm4xCzAXOusKqq/vWdKXphTpj5klHKNmRUU6/QRGlBsyU9mAIPaWHlqJA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-member-expression-to-functions/-/helper-member-expression-to-functions-7.23.0.tgz}
    name: '@babel/helper-member-expression-to-functions'
    version: 7.23.0
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helper-module-imports@7.22.15:
    resolution: {integrity: sha512-0pYVBnDKZO2fnSPCrgM/6WMc7eS20Fbok+0r88fp+YtWVLZrp4CkafFGIp+W0VKw4a22sgebPT99y+FDNMdP4w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.22.15.tgz}
    name: '@babel/helper-module-imports'
    version: 7.22.15
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helper-module-imports@7.24.3:
    resolution: {integrity: sha512-viKb0F9f2s0BCS22QSF308z/+1YWKV/76mwt61NBzS5izMzDPwdq1pTrzf+Li3npBWX9KdQbkeCt1jSAM7lZqg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.24.3.tgz}
    name: '@babel/helper-module-imports'
    version: 7.24.3
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helper-module-transforms@7.23.3(@babel/core@7.24.4):
    resolution: {integrity: sha512-7bBs4ED9OmswdfDzpz4MpWgSrV7FXlc3zIagvLFjS5H+Mk7Snr21vQ6QwrsoCGMfNC4e4LQPdoULEt4ykz0SRQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.23.3.tgz}
    id: registry.npmjs.org/@babel/helper-module-transforms/7.23.3
    name: '@babel/helper-module-transforms'
    version: 7.23.3
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-environment-visitor': registry.npmjs.org/@babel/helper-environment-visitor@7.22.20
      '@babel/helper-module-imports': registry.npmjs.org/@babel/helper-module-imports@7.24.3
      '@babel/helper-simple-access': registry.npmjs.org/@babel/helper-simple-access@7.22.5
      '@babel/helper-split-export-declaration': registry.npmjs.org/@babel/helper-split-export-declaration@7.22.6
      '@babel/helper-validator-identifier': registry.npmjs.org/@babel/helper-validator-identifier@7.22.20
    dev: true

  registry.npmjs.org/@babel/helper-optimise-call-expression@7.22.5:
    resolution: {integrity: sha512-HBwaojN0xFRx4yIvpwGqxiV2tUfl7401jlok564NgB9EHS1y6QT17FmKWm4ztqjeVdXLuC4fSvHc5ePpQjoTbw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-optimise-call-expression/-/helper-optimise-call-expression-7.22.5.tgz}
    name: '@babel/helper-optimise-call-expression'
    version: 7.22.5
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helper-plugin-utils@7.24.0:
    resolution: {integrity: sha512-9cUznXMG0+FxRuJfvL82QlTqIzhVW9sL0KjMPHhAOOvpQGL8QtdxnBKILjBqxlHyliz0yCa1G903ZXI/FuHy2w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.24.0.tgz}
    name: '@babel/helper-plugin-utils'
    version: 7.24.0
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmjs.org/@babel/helper-remap-async-to-generator@7.22.20(@babel/core@7.24.4):
    resolution: {integrity: sha512-pBGyV4uBqOns+0UvhsTO8qgl8hO89PmiDYv+/COyp1aeMcmfrfruz+/nCMFiYyFF/Knn0yfrC85ZzNFjembFTw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-remap-async-to-generator/-/helper-remap-async-to-generator-7.22.20.tgz}
    id: registry.npmjs.org/@babel/helper-remap-async-to-generator/7.22.20
    name: '@babel/helper-remap-async-to-generator'
    version: 7.22.20
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-annotate-as-pure': registry.npmjs.org/@babel/helper-annotate-as-pure@7.22.5
      '@babel/helper-environment-visitor': registry.npmjs.org/@babel/helper-environment-visitor@7.22.20
      '@babel/helper-wrap-function': registry.npmjs.org/@babel/helper-wrap-function@7.22.20
    dev: true

  registry.npmjs.org/@babel/helper-replace-supers@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-QCR1UqC9BzG5vZl8BMicmZ28RuUBnHhAMddD8yHFHDRH9lLTZ9uUPehX8ctVPT8l0TKblJidqcgUUKGVrePleQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-replace-supers/-/helper-replace-supers-7.24.1.tgz}
    id: registry.npmjs.org/@babel/helper-replace-supers/7.24.1
    name: '@babel/helper-replace-supers'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-environment-visitor': registry.npmjs.org/@babel/helper-environment-visitor@7.22.20
      '@babel/helper-member-expression-to-functions': registry.npmjs.org/@babel/helper-member-expression-to-functions@7.23.0
      '@babel/helper-optimise-call-expression': registry.npmjs.org/@babel/helper-optimise-call-expression@7.22.5
    dev: true

  registry.npmjs.org/@babel/helper-simple-access@7.22.5:
    resolution: {integrity: sha512-n0H99E/K+Bika3++WNL17POvo4rKWZ7lZEp1Q+fStVbUi8nxPQEBOlTmCOxW/0JsS56SKKQ+ojAe2pHKJHN35w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-simple-access/-/helper-simple-access-7.22.5.tgz}
    name: '@babel/helper-simple-access'
    version: 7.22.5
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers@7.22.5:
    resolution: {integrity: sha512-tK14r66JZKiC43p8Ki33yLBVJKlQDFoA8GYN67lWCDCqoL6EMMSuM9b+Iff2jHaM/RRFYl7K+iiru7hbRqNx8Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers/-/helper-skip-transparent-expression-wrappers-7.22.5.tgz}
    name: '@babel/helper-skip-transparent-expression-wrappers'
    version: 7.22.5
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helper-split-export-declaration@7.22.6:
    resolution: {integrity: sha512-AsUnxuLhRYsisFiaJwvp1QF+I3KjD5FOxut14q/GzovUe6orHLesW2C7d754kRm53h5gqrz6sFl6sxc4BVtE/g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-split-export-declaration/-/helper-split-export-declaration-7.22.6.tgz}
    name: '@babel/helper-split-export-declaration'
    version: 7.22.6
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helper-string-parser@7.24.1:
    resolution: {integrity: sha512-2ofRCjnnA9y+wk8b9IAREroeUP02KHp431N2mhKniy2yKIDKpbrHv9eXwm8cBeWQYcJmzv5qKCu65P47eCF7CQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.24.1.tgz}
    name: '@babel/helper-string-parser'
    version: 7.24.1
    engines: {node: '>=6.9.0'}

  registry.npmjs.org/@babel/helper-validator-identifier@7.22.20:
    resolution: {integrity: sha512-Y4OZ+ytlatR8AI+8KZfKuL5urKp7qey08ha31L8b3BwewJAoJamTzyvxPR/5D+KkdJCGPq/+8TukHBlY10FX9A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.22.20.tgz}
    name: '@babel/helper-validator-identifier'
    version: 7.22.20
    engines: {node: '>=6.9.0'}

  registry.npmjs.org/@babel/helper-validator-option@7.23.5:
    resolution: {integrity: sha512-85ttAOMLsr53VgXkTbkx8oA6YTfT4q7/HzXSLEYmjcSTJPMPQtvq1BD79Byep5xMUYbGRzEpDsjUf3dyp54IKw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.23.5.tgz}
    name: '@babel/helper-validator-option'
    version: 7.23.5
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmjs.org/@babel/helper-wrap-function@7.22.20:
    resolution: {integrity: sha512-pms/UwkOpnQe/PDAEdV/d7dVCoBbB+R4FvYoHGZz+4VPcg7RtYy2KP7S2lbuWM6FCSgob5wshfGESbC/hzNXZw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helper-wrap-function/-/helper-wrap-function-7.22.20.tgz}
    name: '@babel/helper-wrap-function'
    version: 7.22.20
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-function-name': registry.npmjs.org/@babel/helper-function-name@7.23.0
      '@babel/template': registry.npmjs.org/@babel/template@7.24.0
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/helpers@7.24.4:
    resolution: {integrity: sha512-FewdlZbSiwaVGlgT1DPANDuCHaDMiOo+D/IDYRFYjHOuv66xMSJ7fQwwODwRNAPkADIO/z1EoF/l2BCWlWABDw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/helpers/-/helpers-7.24.4.tgz}
    name: '@babel/helpers'
    version: 7.24.4
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/template': registry.npmjs.org/@babel/template@7.24.0
      '@babel/traverse': registry.npmjs.org/@babel/traverse@7.24.1
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/highlight@7.24.2:
    resolution: {integrity: sha512-Yac1ao4flkTxTteCDZLEvdxg2fZfz1v8M4QpaGypq/WPDqg3ijHYbDfs+LG5hvzSoqaSZ9/Z9lKSP3CjZjv+pA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/highlight/-/highlight-7.24.2.tgz}
    name: '@babel/highlight'
    version: 7.24.2
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-validator-identifier': registry.npmjs.org/@babel/helper-validator-identifier@7.22.20
      chalk: registry.npmjs.org/chalk@2.4.2
      js-tokens: registry.npmjs.org/js-tokens@4.0.0
      picocolors: registry.npmjs.org/picocolors@1.0.0
    dev: true

  registry.npmjs.org/@babel/parser@7.24.4:
    resolution: {integrity: sha512-zTvEBcghmeBma9QIGunWevvBAp4/Qu9Bdq+2k0Ot4fVMD6v3dsC9WOcRSKk7tRRyBM/53yKMJko9xOatGQAwSg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/parser/-/parser-7.24.4.tgz}
    name: '@babel/parser'
    version: 7.24.4
    engines: {node: '>=6.0.0'}
    hasBin: true
    dependencies:
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0

  registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.24.4(@babel/core@7.24.4):
    resolution: {integrity: sha512-qpl6vOOEEzTLLcsuqYYo8yDtrTocmu2xkGvgNebvPjT9DTtfFYGmgDqY+rBYXNlqL4s9qLDn6xkrJv4RxAPiTA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/-/plugin-bugfix-firefox-class-in-computed-class-key-7.24.4.tgz}
    id: registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key/7.24.4
    name: '@babel/plugin-bugfix-firefox-class-in-computed-class-key'
    version: 7.24.4
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-environment-visitor': registry.npmjs.org/@babel/helper-environment-visitor@7.22.20
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-y4HqEnkelJIOQGd+3g1bTeKsA5c6qM7eOn7VggGVbBc0y8MLSKHacwcIE2PplNlQSj0PqS9rrXL/nkPVK+kUNg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/-/plugin-bugfix-safari-id-destructuring-collision-in-function-expression-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression/7.24.1
    name: '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-Hj791Ii4ci8HqnaKHAlLNs+zaLXb0EzSDhiAWp5VNlyvCNymYfacs64pxTxbH1znW/NcArSmwpmG9IKE/TUVVQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/-/plugin-bugfix-v8-spread-parameters-in-optional-chaining-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining/7.24.1
    name: '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.13.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/helper-skip-transparent-expression-wrappers': registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers@7.22.5
      '@babel/plugin-transform-optional-chaining': registry.npmjs.org/@babel/plugin-transform-optional-chaining@7.24.1(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-m9m/fXsXLiHfwdgydIFnpk+7jlVbnvlK5B2EKiPdLUb6WX654ZaaEWJUjk8TftRbZpK0XibovlLWX4KIZhV6jw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/-/plugin-bugfix-v8-static-class-fields-redefine-readonly-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly/7.24.1
    name: '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-environment-visitor': registry.npmjs.org/@babel/helper-environment-visitor@7.22.20
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.24.4):
    resolution: {integrity: sha512-cumfXOF0+nzZrrN8Rf0t7M+tF6sZc7vhQwYQck9q1/5w2OExlD+b4v4RpMJFaV1Z7WcDRgO6FqvxqxGlwo+RHQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-proposal-class-properties/-/plugin-proposal-class-properties-7.18.6.tgz}
    id: registry.npmjs.org/@babel/plugin-proposal-class-properties/7.18.6
    name: '@babel/plugin-proposal-class-properties'
    version: 7.18.6
    engines: {node: '>=6.9.0'}
    deprecated: This proposal has been merged to the ECMAScript standard and thus this plugin is no longer maintained. Please use @babel/plugin-transform-class-properties instead.
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-create-class-features-plugin': registry.npmjs.org/@babel/helper-create-class-features-plugin@7.24.4(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-proposal-decorators@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-zPEvzFijn+hRvJuX2Vu3KbEBN39LN3f7tW3MQO2LsIs57B26KU+kUc82BdAktS1VCM6libzh45eKGI65lg0cpA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-proposal-decorators/-/plugin-proposal-decorators-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-proposal-decorators/7.24.1
    name: '@babel/plugin-proposal-decorators'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-create-class-features-plugin': registry.npmjs.org/@babel/helper-create-class-features-plugin@7.24.4(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-decorators': registry.npmjs.org/@babel/plugin-syntax-decorators@7.24.1(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.24.4):
    resolution: {integrity: sha512-SOSkfJDddaM7mak6cPEpswyTRnuRltl429hMraQEglW+OkovnCzsiszTmsrlY//qLFjCpQDFRvjdm2wA5pPm9w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/-/plugin-proposal-private-property-in-object-7.21.0-placeholder-for-preset-env.2.tgz}
    id: registry.npmjs.org/@babel/plugin-proposal-private-property-in-object/7.21.0-placeholder-for-preset-env.2
    name: '@babel/plugin-proposal-private-property-in-object'
    version: 7.21.0-placeholder-for-preset-env.2
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.24.4):
    resolution: {integrity: sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-async-generators/7.8.4
    name: '@babel/plugin-syntax-async-generators'
    version: 7.8.4
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.24.4):
    resolution: {integrity: sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-class-properties/7.12.13
    name: '@babel/plugin-syntax-class-properties'
    version: 7.12.13
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.24.4):
    resolution: {integrity: sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-class-static-block/7.14.5
    name: '@babel/plugin-syntax-class-static-block'
    version: 7.14.5
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-decorators@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-05RJdO/cCrtVWuAaSn1tS3bH8jbsJa/Y1uD186u6J4C/1mnHFxseeuWpsqr9anvo7TUulev7tm7GDwRV+VuhDw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-decorators/-/plugin-syntax-decorators-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-decorators/7.24.1
    name: '@babel/plugin-syntax-decorators'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.24.4):
    resolution: {integrity: sha512-5gdGbFon+PszYzqs83S3E5mpi7/y/8M9eC90MRTZfduQOYW76ig6SOSPNe41IG5LoP3FGBn2N0RjVDSQiS94kQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-dynamic-import/-/plugin-syntax-dynamic-import-7.8.3.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-dynamic-import/7.8.3
    name: '@babel/plugin-syntax-dynamic-import'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.24.4):
    resolution: {integrity: sha512-MXf5laXo6c1IbEbegDmzGPwGNTsHZmEy6QGznu5Sh2UCWvueywb2ee+CCE4zQiZstxU9BMoQO9i6zUFSY0Kj0Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-export-namespace-from/-/plugin-syntax-export-namespace-from-7.8.3.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-export-namespace-from/7.8.3
    name: '@babel/plugin-syntax-export-namespace-from'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-import-assertions@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-IuwnI5XnuF189t91XbxmXeCDz3qs6iDRO7GJ++wcfgeXNs/8FmIlKcpDSXNVyuLQxlwvskmI3Ct73wUODkJBlQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-import-assertions/-/plugin-syntax-import-assertions-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-import-assertions/7.24.1
    name: '@babel/plugin-syntax-import-assertions'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-import-attributes@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-zhQTMH0X2nVLnb04tz+s7AMuasX8U0FnpE+nHTOhSOINjWMnopoZTxtIKsd45n4GQ/HIZLyfIpoul8e2m0DnRA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-import-attributes/7.24.1
    name: '@babel/plugin-syntax-import-attributes'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.24.4):
    resolution: {integrity: sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-import-meta/7.10.4
    name: '@babel/plugin-syntax-import-meta'
    version: 7.10.4
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.24.4):
    resolution: {integrity: sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-json-strings/7.8.3
    name: '@babel/plugin-syntax-json-strings'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-2eCtxZXf+kbkMIsXS4poTvT4Yu5rXiRa+9xGVT56raghjmBTKMpFNc9R4IDiB4emao9eO22Ox7CxuJG7BgExqA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-jsx/7.24.1
    name: '@babel/plugin-syntax-jsx'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.24.4):
    resolution: {integrity: sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/7.10.4
    name: '@babel/plugin-syntax-logical-assignment-operators'
    version: 7.10.4
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.24.4):
    resolution: {integrity: sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/7.8.3
    name: '@babel/plugin-syntax-nullish-coalescing-operator'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.24.4):
    resolution: {integrity: sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-numeric-separator/7.10.4
    name: '@babel/plugin-syntax-numeric-separator'
    version: 7.10.4
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.24.4):
    resolution: {integrity: sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/7.8.3
    name: '@babel/plugin-syntax-object-rest-spread'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.24.4):
    resolution: {integrity: sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/7.8.3
    name: '@babel/plugin-syntax-optional-catch-binding'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.24.4):
    resolution: {integrity: sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-optional-chaining/7.8.3
    name: '@babel/plugin-syntax-optional-chaining'
    version: 7.8.3
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.24.4):
    resolution: {integrity: sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/7.14.5
    name: '@babel/plugin-syntax-private-property-in-object'
    version: 7.14.5
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.24.4):
    resolution: {integrity: sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-top-level-await/7.14.5
    name: '@babel/plugin-syntax-top-level-await'
    version: 7.14.5
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.24.4):
    resolution: {integrity: sha512-727YkEAPwSIQTv5im8QHz3upqp92JTWhidIC81Tdx4VJYIte/VndKf1qKrfnnhPLiPghStWfvC/iFaMCQu7Nqg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/-/plugin-syntax-unicode-sets-regex-7.18.6.tgz}
    id: registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex/7.18.6
    name: '@babel/plugin-syntax-unicode-sets-regex'
    version: 7.18.6
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-create-regexp-features-plugin': registry.npmjs.org/@babel/helper-create-regexp-features-plugin@7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-arrow-functions@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-ngT/3NkRhsaep9ck9uj2Xhv9+xB1zShY3tM3g6om4xxCELwCDN4g4Aq5dRn48+0hasAql7s2hdBOysCfNpr4fw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-arrow-functions/-/plugin-transform-arrow-functions-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-arrow-functions/7.24.1
    name: '@babel/plugin-transform-arrow-functions'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-async-generator-functions@7.24.3(@babel/core@7.24.4):
    resolution: {integrity: sha512-Qe26CMYVjpQxJ8zxM1340JFNjZaF+ISWpr1Kt/jGo+ZTUzKkfw/pphEWbRCb+lmSM6k/TOgfYLvmbHkUQ0asIg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-async-generator-functions/-/plugin-transform-async-generator-functions-7.24.3.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-async-generator-functions/7.24.3
    name: '@babel/plugin-transform-async-generator-functions'
    version: 7.24.3
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-environment-visitor': registry.npmjs.org/@babel/helper-environment-visitor@7.22.20
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/helper-remap-async-to-generator': registry.npmjs.org/@babel/helper-remap-async-to-generator@7.22.20(@babel/core@7.24.4)
      '@babel/plugin-syntax-async-generators': registry.npmjs.org/@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-async-to-generator@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-AawPptitRXp1y0n4ilKcGbRYWfbbzFWz2NqNu7dacYDtFtz0CMjG64b3LQsb3KIgnf4/obcUL78hfaOS7iCUfw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-async-to-generator/-/plugin-transform-async-to-generator-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-async-to-generator/7.24.1
    name: '@babel/plugin-transform-async-to-generator'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-module-imports': registry.npmjs.org/@babel/helper-module-imports@7.24.3
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/helper-remap-async-to-generator': registry.npmjs.org/@babel/helper-remap-async-to-generator@7.22.20(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-block-scoped-functions@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-TWWC18OShZutrv9C6mye1xwtam+uNi2bnTOCBUd5sZxyHOiWbU6ztSROofIMrK84uweEZC219POICK/sTYwfgg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/-/plugin-transform-block-scoped-functions-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-block-scoped-functions/7.24.1
    name: '@babel/plugin-transform-block-scoped-functions'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-block-scoping@7.24.4(@babel/core@7.24.4):
    resolution: {integrity: sha512-nIFUZIpGKDf9O9ttyRXpHFpKC+X3Y5mtshZONuEUYBomAKoM4y029Jr+uB1bHGPhNmK8YXHevDtKDOLmtRrp6g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-block-scoping/-/plugin-transform-block-scoping-7.24.4.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-block-scoping/7.24.4
    name: '@babel/plugin-transform-block-scoping'
    version: 7.24.4
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-class-properties@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-OMLCXi0NqvJfORTaPQBwqLXHhb93wkBKZ4aNwMl6WtehO7ar+cmp+89iPEQPqxAnxsOKTaMcs3POz3rKayJ72g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-class-properties/-/plugin-transform-class-properties-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-class-properties/7.24.1
    name: '@babel/plugin-transform-class-properties'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-create-class-features-plugin': registry.npmjs.org/@babel/helper-create-class-features-plugin@7.24.4(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-class-static-block@7.24.4(@babel/core@7.24.4):
    resolution: {integrity: sha512-B8q7Pz870Hz/q9UgP8InNpY01CSLDSCyqX7zcRuv3FcPl87A2G17lASroHWaCtbdIcbYzOZ7kWmXFKbijMSmFg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-class-static-block/-/plugin-transform-class-static-block-7.24.4.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-class-static-block/7.24.4
    name: '@babel/plugin-transform-class-static-block'
    version: 7.24.4
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.12.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-create-class-features-plugin': registry.npmjs.org/@babel/helper-create-class-features-plugin@7.24.4(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-class-static-block': registry.npmjs.org/@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-classes@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-ZTIe3W7UejJd3/3R4p7ScyyOoafetUShSf4kCqV0O7F/RiHxVj/wRaRnQlrGwflvcehNA8M42HkAiEDYZu2F1Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-classes/-/plugin-transform-classes-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-classes/7.24.1
    name: '@babel/plugin-transform-classes'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-annotate-as-pure': registry.npmjs.org/@babel/helper-annotate-as-pure@7.22.5
      '@babel/helper-compilation-targets': registry.npmjs.org/@babel/helper-compilation-targets@7.23.6
      '@babel/helper-environment-visitor': registry.npmjs.org/@babel/helper-environment-visitor@7.22.20
      '@babel/helper-function-name': registry.npmjs.org/@babel/helper-function-name@7.23.0
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/helper-replace-supers': registry.npmjs.org/@babel/helper-replace-supers@7.24.1(@babel/core@7.24.4)
      '@babel/helper-split-export-declaration': registry.npmjs.org/@babel/helper-split-export-declaration@7.22.6
      globals: registry.npmjs.org/globals@11.12.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-computed-properties@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-5pJGVIUfJpOS+pAqBQd+QMaTD2vCL/HcePooON6pDpHgRp4gNRmzyHTPIkXntwKsq3ayUFVfJaIKPw2pOkOcTw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-computed-properties/-/plugin-transform-computed-properties-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-computed-properties/7.24.1
    name: '@babel/plugin-transform-computed-properties'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/template': registry.npmjs.org/@babel/template@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-destructuring@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-ow8jciWqNxR3RYbSNVuF4U2Jx130nwnBnhRw6N6h1bOejNkABmcI5X5oz29K4alWX7vf1C+o6gtKXikzRKkVdw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-destructuring/-/plugin-transform-destructuring-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-destructuring/7.24.1
    name: '@babel/plugin-transform-destructuring'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-dotall-regex@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-p7uUxgSoZwZ2lPNMzUkqCts3xlp8n+o05ikjy7gbtFJSt9gdU88jAmtfmOxHM14noQXBxfgzf2yRWECiNVhTCw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-dotall-regex/-/plugin-transform-dotall-regex-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-dotall-regex/7.24.1
    name: '@babel/plugin-transform-dotall-regex'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-create-regexp-features-plugin': registry.npmjs.org/@babel/helper-create-regexp-features-plugin@7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-duplicate-keys@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-msyzuUnvsjsaSaocV6L7ErfNsa5nDWL1XKNnDePLgmz+WdU4w/J8+AxBMrWfi9m4IxfL5sZQKUPQKDQeeAT6lA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-duplicate-keys/-/plugin-transform-duplicate-keys-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-duplicate-keys/7.24.1
    name: '@babel/plugin-transform-duplicate-keys'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-dynamic-import@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-av2gdSTyXcJVdI+8aFZsCAtR29xJt0S5tas+Ef8NvBNmD1a+N/3ecMLeMBgfcK+xzsjdLDT6oHt+DFPyeqUbDA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-dynamic-import/-/plugin-transform-dynamic-import-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-dynamic-import/7.24.1
    name: '@babel/plugin-transform-dynamic-import'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-dynamic-import': registry.npmjs.org/@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-exponentiation-operator@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-U1yX13dVBSwS23DEAqU+Z/PkwE9/m7QQy8Y9/+Tdb8UWYaGNDYwTLi19wqIAiROr8sXVum9A/rtiH5H0boUcTw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/-/plugin-transform-exponentiation-operator-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-exponentiation-operator/7.24.1
    name: '@babel/plugin-transform-exponentiation-operator'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-builder-binary-assignment-operator-visitor': registry.npmjs.org/@babel/helper-builder-binary-assignment-operator-visitor@7.22.15
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-export-namespace-from@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-Ft38m/KFOyzKw2UaJFkWG9QnHPG/Q/2SkOrRk4pNBPg5IPZ+dOxcmkK5IyuBcxiNPyyYowPGUReyBvrvZs7IlQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-export-namespace-from/-/plugin-transform-export-namespace-from-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-export-namespace-from/7.24.1
    name: '@babel/plugin-transform-export-namespace-from'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-export-namespace-from': registry.npmjs.org/@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-for-of@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-OxBdcnF04bpdQdR3i4giHZNZQn7cm8RQKcSwA17wAAqEELo1ZOwp5FFgeptWUQXFyT9kwHo10aqqauYkRZPCAg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-for-of/-/plugin-transform-for-of-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-for-of/7.24.1
    name: '@babel/plugin-transform-for-of'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/helper-skip-transparent-expression-wrappers': registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers@7.22.5
    dev: true

  registry.npmjs.org/@babel/plugin-transform-function-name@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-BXmDZpPlh7jwicKArQASrj8n22/w6iymRnvHYYd2zO30DbE277JO20/7yXJT3QxDPtiQiOxQBbZH4TpivNXIxA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-function-name/-/plugin-transform-function-name-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-function-name/7.24.1
    name: '@babel/plugin-transform-function-name'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-compilation-targets': registry.npmjs.org/@babel/helper-compilation-targets@7.23.6
      '@babel/helper-function-name': registry.npmjs.org/@babel/helper-function-name@7.23.0
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-json-strings@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-U7RMFmRvoasscrIFy5xA4gIp8iWnWubnKkKuUGJjsuOH7GfbMkB+XZzeslx2kLdEGdOJDamEmCqOks6e8nv8DQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-json-strings/-/plugin-transform-json-strings-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-json-strings/7.24.1
    name: '@babel/plugin-transform-json-strings'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-json-strings': registry.npmjs.org/@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-literals@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-zn9pwz8U7nCqOYIiBaOxoQOtYmMODXTJnkxG4AtX8fPmnCRYWBOHD0qcpwS9e2VDSp1zNJYpdnFMIKb8jmwu6g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-literals/-/plugin-transform-literals-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-literals/7.24.1
    name: '@babel/plugin-transform-literals'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-OhN6J4Bpz+hIBqItTeWJujDOfNP+unqv/NJgyhlpSqgBTPm37KkMmZV6SYcOj+pnDbdcl1qRGV/ZiIjX9Iy34w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/-/plugin-transform-logical-assignment-operators-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators/7.24.1
    name: '@babel/plugin-transform-logical-assignment-operators'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-logical-assignment-operators': registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-member-expression-literals@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-4ojai0KysTWXzHseJKa1XPNXKRbuUrhkOPY4rEGeR+7ChlJVKxFa3H3Bz+7tWaGKgJAXUWKOGmltN+u9B3+CVg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-member-expression-literals/-/plugin-transform-member-expression-literals-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-member-expression-literals/7.24.1
    name: '@babel/plugin-transform-member-expression-literals'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-modules-amd@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-lAxNHi4HVtjnHd5Rxg3D5t99Xm6H7b04hUS7EHIXcUl2EV4yl1gWdqZrNzXnSrHveL9qMdbODlLF55mvgjAfaQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-modules-amd/-/plugin-transform-modules-amd-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-modules-amd/7.24.1
    name: '@babel/plugin-transform-modules-amd'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-module-transforms': registry.npmjs.org/@babel/helper-module-transforms@7.23.3(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-modules-commonjs@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-szog8fFTUxBfw0b98gEWPaEqF42ZUD/T3bkynW/wtgx2p/XCP55WEsb+VosKceRSd6njipdZvNogqdtI4Q0chw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-modules-commonjs/-/plugin-transform-modules-commonjs-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-modules-commonjs/7.24.1
    name: '@babel/plugin-transform-modules-commonjs'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-module-transforms': registry.npmjs.org/@babel/helper-module-transforms@7.23.3(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/helper-simple-access': registry.npmjs.org/@babel/helper-simple-access@7.22.5
    dev: true

  registry.npmjs.org/@babel/plugin-transform-modules-systemjs@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-mqQ3Zh9vFO1Tpmlt8QPnbwGHzNz3lpNEMxQb1kAemn/erstyqw1r9KeOlOfo3y6xAnFEcOv2tSyrXfmMk+/YZA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-modules-systemjs/-/plugin-transform-modules-systemjs-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-modules-systemjs/7.24.1
    name: '@babel/plugin-transform-modules-systemjs'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-hoist-variables': registry.npmjs.org/@babel/helper-hoist-variables@7.22.5
      '@babel/helper-module-transforms': registry.npmjs.org/@babel/helper-module-transforms@7.23.3(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/helper-validator-identifier': registry.npmjs.org/@babel/helper-validator-identifier@7.22.20
    dev: true

  registry.npmjs.org/@babel/plugin-transform-modules-umd@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-tuA3lpPj+5ITfcCluy6nWonSL7RvaG0AOTeAuvXqEKS34lnLzXpDb0dcP6K8jD0zWZFNDVly90AGFJPnm4fOYg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-modules-umd/-/plugin-transform-modules-umd-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-modules-umd/7.24.1
    name: '@babel/plugin-transform-modules-umd'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-module-transforms': registry.npmjs.org/@babel/helper-module-transforms@7.23.3(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex@7.22.5(@babel/core@7.24.4):
    resolution: {integrity: sha512-YgLLKmS3aUBhHaxp5hi1WJTgOUb/NCuDHzGT9z9WTt3YG+CPRhJs6nprbStx6DnWM4dh6gt7SU3sZodbZ08adQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/-/plugin-transform-named-capturing-groups-regex-7.22.5.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex/7.22.5
    name: '@babel/plugin-transform-named-capturing-groups-regex'
    version: 7.22.5
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-create-regexp-features-plugin': registry.npmjs.org/@babel/helper-create-regexp-features-plugin@7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-new-target@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-/rurytBM34hYy0HKZQyA0nHbQgQNFm4Q/BOc9Hflxi2X3twRof7NaE5W46j4kQitm7SvACVRXsa6N/tSZxvPug==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-new-target/-/plugin-transform-new-target-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-new-target/7.24.1
    name: '@babel/plugin-transform-new-target'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-iQ+caew8wRrhCikO5DrUYx0mrmdhkaELgFa+7baMcVuhxIkN7oxt06CZ51D65ugIb1UWRQ8oQe+HXAVM6qHFjw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/-/plugin-transform-nullish-coalescing-operator-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator/7.24.1
    name: '@babel/plugin-transform-nullish-coalescing-operator'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-nullish-coalescing-operator': registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-numeric-separator@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-7GAsGlK4cNL2OExJH1DzmDeKnRv/LXq0eLUSvudrehVA5Rgg4bIrqEUW29FbKMBRT0ztSqisv7kjP+XIC4ZMNw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-numeric-separator/-/plugin-transform-numeric-separator-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-numeric-separator/7.24.1
    name: '@babel/plugin-transform-numeric-separator'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-numeric-separator': registry.npmjs.org/@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-object-rest-spread@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-XjD5f0YqOtebto4HGISLNfiNMTTs6tbkFf2TOqJlYKYmbo+mN9Dnpl4SRoofiziuOWMIyq3sZEUqLo3hLITFEA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-object-rest-spread/-/plugin-transform-object-rest-spread-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-object-rest-spread/7.24.1
    name: '@babel/plugin-transform-object-rest-spread'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-compilation-targets': registry.npmjs.org/@babel/helper-compilation-targets@7.23.6
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-object-rest-spread': registry.npmjs.org/@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.24.4)
      '@babel/plugin-transform-parameters': registry.npmjs.org/@babel/plugin-transform-parameters@7.24.1(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-object-super@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-oKJqR3TeI5hSLRxudMjFQ9re9fBVUU0GICqM3J1mi8MqlhVr6hC/ZN4ttAyMuQR6EZZIY6h/exe5swqGNNIkWQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-object-super/-/plugin-transform-object-super-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-object-super/7.24.1
    name: '@babel/plugin-transform-object-super'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/helper-replace-supers': registry.npmjs.org/@babel/helper-replace-supers@7.24.1(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-optional-catch-binding@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-oBTH7oURV4Y+3EUrf6cWn1OHio3qG/PVwO5J03iSJmBg6m2EhKjkAu/xuaXaYwWW9miYtvbWv4LNf0AmR43LUA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/-/plugin-transform-optional-catch-binding-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-optional-catch-binding/7.24.1
    name: '@babel/plugin-transform-optional-catch-binding'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-optional-catch-binding': registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-optional-chaining@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-n03wmDt+987qXwAgcBlnUUivrZBPZ8z1plL0YvgQalLm+ZE5BMhGm94jhxXtA1wzv1Cu2aaOv1BM9vbVttrzSg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-optional-chaining/-/plugin-transform-optional-chaining-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-optional-chaining/7.24.1
    name: '@babel/plugin-transform-optional-chaining'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/helper-skip-transparent-expression-wrappers': registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers@7.22.5
      '@babel/plugin-syntax-optional-chaining': registry.npmjs.org/@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-parameters@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-8Jl6V24g+Uw5OGPeWNKrKqXPDw2YDjLc53ojwfMcKwlEoETKU9rU0mHUtcg9JntWI/QYzGAXNWEcVHZ+fR+XXg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-parameters/-/plugin-transform-parameters-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-parameters/7.24.1
    name: '@babel/plugin-transform-parameters'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-private-methods@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-tGvisebwBO5em4PaYNqt4fkw56K2VALsAbAakY0FjTYqJp7gfdrgr7YX76Or8/cpik0W6+tj3rZ0uHU9Oil4tw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-private-methods/-/plugin-transform-private-methods-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-private-methods/7.24.1
    name: '@babel/plugin-transform-private-methods'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-create-class-features-plugin': registry.npmjs.org/@babel/helper-create-class-features-plugin@7.24.4(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-private-property-in-object@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-pTHxDVa0BpUbvAgX3Gat+7cSciXqUcY9j2VZKTbSB6+VQGpNgNO9ailxTGHSXlqOnX1Hcx1Enme2+yv7VqP9bg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-private-property-in-object/-/plugin-transform-private-property-in-object-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-private-property-in-object/7.24.1
    name: '@babel/plugin-transform-private-property-in-object'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-annotate-as-pure': registry.npmjs.org/@babel/helper-annotate-as-pure@7.22.5
      '@babel/helper-create-class-features-plugin': registry.npmjs.org/@babel/helper-create-class-features-plugin@7.24.4(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-private-property-in-object': registry.npmjs.org/@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@babel/plugin-transform-property-literals@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-LetvD7CrHmEx0G442gOomRr66d7q8HzzGGr4PMHGr+5YIm6++Yke+jxj246rpvsbyhJwCLxcTn6zW1P1BSenqA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-property-literals/-/plugin-transform-property-literals-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-property-literals/7.24.1
    name: '@babel/plugin-transform-property-literals'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-regenerator@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-sJwZBCzIBE4t+5Q4IGLaaun5ExVMRY0lYwos/jNecjMrVCygCdph3IKv0tkP5Fc87e/1+bebAmEAGBfnRD+cnw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-regenerator/-/plugin-transform-regenerator-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-regenerator/7.24.1
    name: '@babel/plugin-transform-regenerator'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      regenerator-transform: registry.npmjs.org/regenerator-transform@0.15.2
    dev: true

  registry.npmjs.org/@babel/plugin-transform-reserved-words@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-JAclqStUfIwKN15HrsQADFgeZt+wexNQ0uLhuqvqAUFoqPMjEcFCYZBhq0LUdz6dZK/mD+rErhW71fbx8RYElg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-reserved-words/-/plugin-transform-reserved-words-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-reserved-words/7.24.1
    name: '@babel/plugin-transform-reserved-words'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-runtime@7.24.3(@babel/core@7.24.4):
    resolution: {integrity: sha512-J0BuRPNlNqlMTRJ72eVptpt9VcInbxO6iP3jaxr+1NPhC0UkKL+6oeX6VXMEYdADnuqmMmsBspt4d5w8Y/TCbQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-runtime/-/plugin-transform-runtime-7.24.3.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-runtime/7.24.3
    name: '@babel/plugin-transform-runtime'
    version: 7.24.3
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-module-imports': registry.npmjs.org/@babel/helper-module-imports@7.24.3
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      babel-plugin-polyfill-corejs2: registry.npmjs.org/babel-plugin-polyfill-corejs2@0.4.10(@babel/core@7.24.4)
      babel-plugin-polyfill-corejs3: registry.npmjs.org/babel-plugin-polyfill-corejs3@0.10.4(@babel/core@7.24.4)
      babel-plugin-polyfill-regenerator: registry.npmjs.org/babel-plugin-polyfill-regenerator@0.6.1(@babel/core@7.24.4)
      semver: registry.npmjs.org/semver@6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/plugin-transform-shorthand-properties@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-LyjVB1nsJ6gTTUKRjRWx9C1s9hE7dLfP/knKdrfeH9UPtAGjYGgxIbFfx7xyLIEWs7Xe1Gnf8EWiUqfjLhInZA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-shorthand-properties/-/plugin-transform-shorthand-properties-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-shorthand-properties/7.24.1
    name: '@babel/plugin-transform-shorthand-properties'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-spread@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-KjmcIM+fxgY+KxPVbjelJC6hrH1CgtPmTvdXAfn3/a9CnWGSTY7nH4zm5+cjmWJybdcPSsD0++QssDsjcpe47g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-spread/-/plugin-transform-spread-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-spread/7.24.1
    name: '@babel/plugin-transform-spread'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/helper-skip-transparent-expression-wrappers': registry.npmjs.org/@babel/helper-skip-transparent-expression-wrappers@7.22.5
    dev: true

  registry.npmjs.org/@babel/plugin-transform-sticky-regex@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-9v0f1bRXgPVcPrngOQvLXeGNNVLc8UjMVfebo9ka0WF3/7+aVUHmaJVT3sa0XCzEFioPfPHZiOcYG9qOsH63cw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-sticky-regex/-/plugin-transform-sticky-regex-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-sticky-regex/7.24.1
    name: '@babel/plugin-transform-sticky-regex'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-template-literals@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-WRkhROsNzriarqECASCNu/nojeXCDTE/F2HmRgOzi7NGvyfYGq1NEjKBK3ckLfRgGc6/lPAqP0vDOSw3YtG34g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-template-literals/-/plugin-transform-template-literals-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-template-literals/7.24.1
    name: '@babel/plugin-transform-template-literals'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-typeof-symbol@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-CBfU4l/A+KruSUoW+vTQthwcAdwuqbpRNB8HQKlZABwHRhsdHZ9fezp4Sn18PeAlYxTNiLMlx4xUBV3AWfg1BA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-typeof-symbol/-/plugin-transform-typeof-symbol-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-typeof-symbol/7.24.1
    name: '@babel/plugin-transform-typeof-symbol'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-unicode-escapes@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-RlkVIcWT4TLI96zM660S877E7beKlQw7Ig+wqkKBiWfj0zH5Q4h50q6er4wzZKRNSYpfo6ILJ+hrJAGSX2qcNw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-unicode-escapes/-/plugin-transform-unicode-escapes-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-unicode-escapes/7.24.1
    name: '@babel/plugin-transform-unicode-escapes'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-unicode-property-regex@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-Ss4VvlfYV5huWApFsF8/Sq0oXnGO+jB+rijFEFugTd3cwSObUSnUi88djgR5528Csl0uKlrI331kRqe56Ov2Ng==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/-/plugin-transform-unicode-property-regex-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-unicode-property-regex/7.24.1
    name: '@babel/plugin-transform-unicode-property-regex'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-create-regexp-features-plugin': registry.npmjs.org/@babel/helper-create-regexp-features-plugin@7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-unicode-regex@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-2A/94wgZgxfTsiLaQ2E36XAOdcZmGAaEEgVmxQWwZXWkGhvoHbaqXcKnU8zny4ycpu3vNqg0L/PcCiYtHtA13g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-unicode-regex/-/plugin-transform-unicode-regex-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-unicode-regex/7.24.1
    name: '@babel/plugin-transform-unicode-regex'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-create-regexp-features-plugin': registry.npmjs.org/@babel/helper-create-regexp-features-plugin@7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex@7.24.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-fqj4WuzzS+ukpgerpAoOnMfQXwUHFxXUZUE84oL2Kao2N8uSlvcpnAidKASgsNgzZHBsHWvcm8s9FPWUhAb8fA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/-/plugin-transform-unicode-sets-regex-7.24.1.tgz}
    id: registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex/7.24.1
    name: '@babel/plugin-transform-unicode-sets-regex'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-create-regexp-features-plugin': registry.npmjs.org/@babel/helper-create-regexp-features-plugin@7.22.15(@babel/core@7.24.4)
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
    dev: true

  registry.npmjs.org/@babel/preset-env@7.24.4(@babel/core@7.24.4):
    resolution: {integrity: sha512-7Kl6cSmYkak0FK/FXjSEnLJ1N9T/WA2RkMhu17gZ/dsxKJUuTYNIylahPTzqpLyJN4WhDif8X0XK1R8Wsguo/A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/preset-env/-/preset-env-7.24.4.tgz}
    id: registry.npmjs.org/@babel/preset-env/7.24.4
    name: '@babel/preset-env'
    version: 7.24.4
    engines: {node: '>=6.9.0'}
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/compat-data': registry.npmjs.org/@babel/compat-data@7.24.4
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-compilation-targets': registry.npmjs.org/@babel/helper-compilation-targets@7.23.6
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/helper-validator-option': registry.npmjs.org/@babel/helper-validator-option@7.23.5
      '@babel/plugin-bugfix-firefox-class-in-computed-class-key': registry.npmjs.org/@babel/plugin-bugfix-firefox-class-in-computed-class-key@7.24.4(@babel/core@7.24.4)
      '@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression': registry.npmjs.org/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining': registry.npmjs.org/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly': registry.npmjs.org/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-proposal-private-property-in-object': registry.npmjs.org/@babel/plugin-proposal-private-property-in-object@7.21.0-placeholder-for-preset-env.2(@babel/core@7.24.4)
      '@babel/plugin-syntax-async-generators': registry.npmjs.org/@babel/plugin-syntax-async-generators@7.8.4(@babel/core@7.24.4)
      '@babel/plugin-syntax-class-properties': registry.npmjs.org/@babel/plugin-syntax-class-properties@7.12.13(@babel/core@7.24.4)
      '@babel/plugin-syntax-class-static-block': registry.npmjs.org/@babel/plugin-syntax-class-static-block@7.14.5(@babel/core@7.24.4)
      '@babel/plugin-syntax-dynamic-import': registry.npmjs.org/@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-export-namespace-from': registry.npmjs.org/@babel/plugin-syntax-export-namespace-from@7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-import-assertions': registry.npmjs.org/@babel/plugin-syntax-import-assertions@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-syntax-import-attributes': registry.npmjs.org/@babel/plugin-syntax-import-attributes@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-syntax-import-meta': registry.npmjs.org/@babel/plugin-syntax-import-meta@7.10.4(@babel/core@7.24.4)
      '@babel/plugin-syntax-json-strings': registry.npmjs.org/@babel/plugin-syntax-json-strings@7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-logical-assignment-operators': registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators@7.10.4(@babel/core@7.24.4)
      '@babel/plugin-syntax-nullish-coalescing-operator': registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator@7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-numeric-separator': registry.npmjs.org/@babel/plugin-syntax-numeric-separator@7.10.4(@babel/core@7.24.4)
      '@babel/plugin-syntax-object-rest-spread': registry.npmjs.org/@babel/plugin-syntax-object-rest-spread@7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-optional-catch-binding': registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding@7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-optional-chaining': registry.npmjs.org/@babel/plugin-syntax-optional-chaining@7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-private-property-in-object': registry.npmjs.org/@babel/plugin-syntax-private-property-in-object@7.14.5(@babel/core@7.24.4)
      '@babel/plugin-syntax-top-level-await': registry.npmjs.org/@babel/plugin-syntax-top-level-await@7.14.5(@babel/core@7.24.4)
      '@babel/plugin-syntax-unicode-sets-regex': registry.npmjs.org/@babel/plugin-syntax-unicode-sets-regex@7.18.6(@babel/core@7.24.4)
      '@babel/plugin-transform-arrow-functions': registry.npmjs.org/@babel/plugin-transform-arrow-functions@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-async-generator-functions': registry.npmjs.org/@babel/plugin-transform-async-generator-functions@7.24.3(@babel/core@7.24.4)
      '@babel/plugin-transform-async-to-generator': registry.npmjs.org/@babel/plugin-transform-async-to-generator@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-block-scoped-functions': registry.npmjs.org/@babel/plugin-transform-block-scoped-functions@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-block-scoping': registry.npmjs.org/@babel/plugin-transform-block-scoping@7.24.4(@babel/core@7.24.4)
      '@babel/plugin-transform-class-properties': registry.npmjs.org/@babel/plugin-transform-class-properties@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-class-static-block': registry.npmjs.org/@babel/plugin-transform-class-static-block@7.24.4(@babel/core@7.24.4)
      '@babel/plugin-transform-classes': registry.npmjs.org/@babel/plugin-transform-classes@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-computed-properties': registry.npmjs.org/@babel/plugin-transform-computed-properties@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-destructuring': registry.npmjs.org/@babel/plugin-transform-destructuring@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-dotall-regex': registry.npmjs.org/@babel/plugin-transform-dotall-regex@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-duplicate-keys': registry.npmjs.org/@babel/plugin-transform-duplicate-keys@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-dynamic-import': registry.npmjs.org/@babel/plugin-transform-dynamic-import@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-exponentiation-operator': registry.npmjs.org/@babel/plugin-transform-exponentiation-operator@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-export-namespace-from': registry.npmjs.org/@babel/plugin-transform-export-namespace-from@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-for-of': registry.npmjs.org/@babel/plugin-transform-for-of@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-function-name': registry.npmjs.org/@babel/plugin-transform-function-name@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-json-strings': registry.npmjs.org/@babel/plugin-transform-json-strings@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-literals': registry.npmjs.org/@babel/plugin-transform-literals@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-logical-assignment-operators': registry.npmjs.org/@babel/plugin-transform-logical-assignment-operators@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-member-expression-literals': registry.npmjs.org/@babel/plugin-transform-member-expression-literals@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-modules-amd': registry.npmjs.org/@babel/plugin-transform-modules-amd@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-modules-commonjs': registry.npmjs.org/@babel/plugin-transform-modules-commonjs@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-modules-systemjs': registry.npmjs.org/@babel/plugin-transform-modules-systemjs@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-modules-umd': registry.npmjs.org/@babel/plugin-transform-modules-umd@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-named-capturing-groups-regex': registry.npmjs.org/@babel/plugin-transform-named-capturing-groups-regex@7.22.5(@babel/core@7.24.4)
      '@babel/plugin-transform-new-target': registry.npmjs.org/@babel/plugin-transform-new-target@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-nullish-coalescing-operator': registry.npmjs.org/@babel/plugin-transform-nullish-coalescing-operator@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-numeric-separator': registry.npmjs.org/@babel/plugin-transform-numeric-separator@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-object-rest-spread': registry.npmjs.org/@babel/plugin-transform-object-rest-spread@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-object-super': registry.npmjs.org/@babel/plugin-transform-object-super@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-optional-catch-binding': registry.npmjs.org/@babel/plugin-transform-optional-catch-binding@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-optional-chaining': registry.npmjs.org/@babel/plugin-transform-optional-chaining@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-parameters': registry.npmjs.org/@babel/plugin-transform-parameters@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-private-methods': registry.npmjs.org/@babel/plugin-transform-private-methods@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-private-property-in-object': registry.npmjs.org/@babel/plugin-transform-private-property-in-object@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-property-literals': registry.npmjs.org/@babel/plugin-transform-property-literals@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-regenerator': registry.npmjs.org/@babel/plugin-transform-regenerator@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-reserved-words': registry.npmjs.org/@babel/plugin-transform-reserved-words@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-shorthand-properties': registry.npmjs.org/@babel/plugin-transform-shorthand-properties@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-spread': registry.npmjs.org/@babel/plugin-transform-spread@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-sticky-regex': registry.npmjs.org/@babel/plugin-transform-sticky-regex@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-template-literals': registry.npmjs.org/@babel/plugin-transform-template-literals@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-typeof-symbol': registry.npmjs.org/@babel/plugin-transform-typeof-symbol@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-unicode-escapes': registry.npmjs.org/@babel/plugin-transform-unicode-escapes@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-unicode-property-regex': registry.npmjs.org/@babel/plugin-transform-unicode-property-regex@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-unicode-regex': registry.npmjs.org/@babel/plugin-transform-unicode-regex@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-unicode-sets-regex': registry.npmjs.org/@babel/plugin-transform-unicode-sets-regex@7.24.1(@babel/core@7.24.4)
      '@babel/preset-modules': registry.npmjs.org/@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.24.4)
      babel-plugin-polyfill-corejs2: registry.npmjs.org/babel-plugin-polyfill-corejs2@0.4.10(@babel/core@7.24.4)
      babel-plugin-polyfill-corejs3: registry.npmjs.org/babel-plugin-polyfill-corejs3@0.10.4(@babel/core@7.24.4)
      babel-plugin-polyfill-regenerator: registry.npmjs.org/babel-plugin-polyfill-regenerator@0.6.1(@babel/core@7.24.4)
      core-js-compat: registry.npmjs.org/core-js-compat@3.37.0
      semver: registry.npmjs.org/semver@6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/preset-modules@0.1.6-no-external-plugins(@babel/core@7.24.4):
    resolution: {integrity: sha512-HrcgcIESLm9aIR842yhJ5RWan/gebQUJ6E/E5+rf0y9o6oj7w0Br+sWuL6kEQ/o/AdfvR1Je9jG18/gnpwjEyA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/preset-modules/-/preset-modules-0.1.6-no-external-plugins.tgz}
    id: registry.npmjs.org/@babel/preset-modules/0.1.6-no-external-plugins
    name: '@babel/preset-modules'
    version: 0.1.6-no-external-plugins
    peerDependencies:
      '@babel/core': ^7.0.0-0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
      esutils: registry.npmjs.org/esutils@2.0.3
    dev: true

  registry.npmjs.org/@babel/regjsgen@0.8.0:
    resolution: {integrity: sha512-x/rqGMdzj+fWZvCOYForTghzbtqPDZ5gPwaoNGHdgDfF2QA/XZbCBp4Moo5scrkAMPhB7z26XM/AaHuIJdgauA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/regjsgen/-/regjsgen-0.8.0.tgz}
    name: '@babel/regjsgen'
    version: 0.8.0
    dev: true

  registry.npmjs.org/@babel/runtime@7.24.4:
    resolution: {integrity: sha512-dkxf7+hn8mFBwKjs9bvBlArzLVxVbS8usaPUDd5p2a9JCL9tB8OaOVN1isD4+Xyk4ns89/xeOmbQvgdK7IIVdA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/runtime/-/runtime-7.24.4.tgz}
    name: '@babel/runtime'
    version: 7.24.4
    engines: {node: '>=6.9.0'}
    dependencies:
      regenerator-runtime: registry.npmjs.org/regenerator-runtime@0.14.1
    dev: true

  registry.npmjs.org/@babel/template@7.24.0:
    resolution: {integrity: sha512-Bkf2q8lMB0AFpX0NFEqSbx1OkTHf0f+0j82mkw+ZpzBnkk7e9Ql0891vlfgi+kHwOk8tQjiQHpqh4LaSa0fKEA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/template/-/template-7.24.0.tgz}
    name: '@babel/template'
    version: 7.24.0
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': registry.npmjs.org/@babel/code-frame@7.24.2
      '@babel/parser': registry.npmjs.org/@babel/parser@7.24.4
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
    dev: true

  registry.npmjs.org/@babel/traverse@7.24.1:
    resolution: {integrity: sha512-xuU6o9m68KeqZbQuDt2TcKSxUw/mrsvavlEqQ1leZ/B+C9tk6E4sRWy97WaXgvq5E+nU3cXMxv3WKOCanVMCmQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/traverse/-/traverse-7.24.1.tgz}
    name: '@babel/traverse'
    version: 7.24.1
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/code-frame': registry.npmjs.org/@babel/code-frame@7.24.2
      '@babel/generator': registry.npmjs.org/@babel/generator@7.24.4
      '@babel/helper-environment-visitor': registry.npmjs.org/@babel/helper-environment-visitor@7.22.20
      '@babel/helper-function-name': registry.npmjs.org/@babel/helper-function-name@7.23.0
      '@babel/helper-hoist-variables': registry.npmjs.org/@babel/helper-hoist-variables@7.22.5
      '@babel/helper-split-export-declaration': registry.npmjs.org/@babel/helper-split-export-declaration@7.22.6
      '@babel/parser': registry.npmjs.org/@babel/parser@7.24.4
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
      debug: registry.npmjs.org/debug@4.3.4
      globals: registry.npmjs.org/globals@11.12.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@babel/types@7.24.0:
    resolution: {integrity: sha512-+j7a5c253RfKh8iABBhywc8NSfP5LURe7Uh4qpsh6jc+aLJguvmIUBdjSdEMQv2bENrCR5MfRdjGo7vzS/ob7w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@babel/types/-/types-7.24.0.tgz}
    name: '@babel/types'
    version: 7.24.0
    engines: {node: '>=6.9.0'}
    dependencies:
      '@babel/helper-string-parser': registry.npmjs.org/@babel/helper-string-parser@7.24.1
      '@babel/helper-validator-identifier': registry.npmjs.org/@babel/helper-validator-identifier@7.22.20
      to-fast-properties: registry.npmjs.org/to-fast-properties@2.0.0

  registry.npmjs.org/@discoveryjs/json-ext@0.5.7:
    resolution: {integrity: sha512-dBVuXR082gk3jsFp7Rd/JI4kytwGHecnCoTtXFb7DB6CNHp4rg5k1bhg0nWdLGLnOV71lmDzGQaLMy8iPLY0pw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@discoveryjs/json-ext/-/json-ext-0.5.7.tgz}
    name: '@discoveryjs/json-ext'
    version: 0.5.7
    engines: {node: '>=10.0.0'}
    dev: true

  registry.npmjs.org/@eslint/eslintrc@0.4.3:
    resolution: {integrity: sha512-J6KFFz5QCYUJq3pf0mjEcCJVERbzv71PUIDczuh9JkwGEzced6CO5ADLHB1rbf/+oPBtoPfMYNOpGDzCANlbXw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-0.4.3.tgz}
    name: '@eslint/eslintrc'
    version: 0.4.3
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      ajv: registry.npmjs.org/ajv@6.12.6
      debug: registry.npmjs.org/debug@4.3.4
      espree: registry.npmjs.org/espree@7.3.1
      globals: registry.npmjs.org/globals@13.24.0
      ignore: registry.npmjs.org/ignore@4.0.6
      import-fresh: registry.npmjs.org/import-fresh@3.3.0
      js-yaml: registry.npmjs.org/js-yaml@3.14.1
      minimatch: registry.npmjs.org/minimatch@3.1.2
      strip-json-comments: registry.npmjs.org/strip-json-comments@3.1.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@hapi/hoek@9.3.0:
    resolution: {integrity: sha512-/c6rf4UJlmHlC9b5BaNvzAcFv7HZ2QHaV0D4/HNlBdvFnvQq8RI4kYdhyPCl7Xj+oWvTWQ8ujhqS53LIgAe6KQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@hapi/hoek/-/hoek-9.3.0.tgz}
    name: '@hapi/hoek'
    version: 9.3.0
    dev: true

  registry.npmjs.org/@hapi/topo@5.1.0:
    resolution: {integrity: sha512-foQZKJig7Ob0BMAYBfcJk8d77QtOe7Wo4ox7ff1lQYoNNAb6jwcY1ncdoy2e9wQZzvNy7ODZCYJkK8kzmcAnAg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@hapi/topo/-/topo-5.1.0.tgz}
    name: '@hapi/topo'
    version: 5.1.0
    dependencies:
      '@hapi/hoek': registry.npmjs.org/@hapi/hoek@9.3.0
    dev: true

  registry.npmjs.org/@humanwhocodes/config-array@0.5.0:
    resolution: {integrity: sha512-FagtKFz74XrTl7y6HCzQpwDfXP0yhxe9lHLD1UZxjvZIcbyRz8zTFF/yYNfSfzU414eDwZ1SrO0Qvtyf+wFMQg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@humanwhocodes/config-array/-/config-array-0.5.0.tgz}
    name: '@humanwhocodes/config-array'
    version: 0.5.0
    engines: {node: '>=10.10.0'}
    dependencies:
      '@humanwhocodes/object-schema': registry.npmjs.org/@humanwhocodes/object-schema@1.2.1
      debug: registry.npmjs.org/debug@4.3.4
      minimatch: registry.npmjs.org/minimatch@3.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@humanwhocodes/object-schema@1.2.1:
    resolution: {integrity: sha512-ZnQMnLV4e7hDlUvw8H+U8ASL02SS2Gn6+9Ac3wGGLIe7+je2AeAOxPY+izIPJDfFDb7eDjev0Us8MO1iFRN8hA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@humanwhocodes/object-schema/-/object-schema-1.2.1.tgz}
    name: '@humanwhocodes/object-schema'
    version: 1.2.1
    dev: true

  registry.npmjs.org/@jridgewell/gen-mapping@0.3.5:
    resolution: {integrity: sha512-IzL8ZoEDIBRWEzlCcRhOaCupYyN5gdIK+Q6fbFdPDg6HqX6jpkItn7DFIpW9LQzXG6Df9sA7+OKnq0qlz/GaQg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.5.tgz}
    name: '@jridgewell/gen-mapping'
    version: 0.3.5
    engines: {node: '>=6.0.0'}
    dependencies:
      '@jridgewell/set-array': registry.npmjs.org/@jridgewell/set-array@1.2.1
      '@jridgewell/sourcemap-codec': registry.npmjs.org/@jridgewell/sourcemap-codec@1.4.15
      '@jridgewell/trace-mapping': registry.npmjs.org/@jridgewell/trace-mapping@0.3.25
    dev: true

  registry.npmjs.org/@jridgewell/resolve-uri@3.1.2:
    resolution: {integrity: sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz}
    name: '@jridgewell/resolve-uri'
    version: 3.1.2
    engines: {node: '>=6.0.0'}
    dev: true

  registry.npmjs.org/@jridgewell/set-array@1.2.1:
    resolution: {integrity: sha512-R8gLRTZeyp03ymzP/6Lil/28tGeGEzhx1q2k703KGWRAI1VdvPIXdG70VJc2pAMw3NA6JKL5hhFu1sJX0Mnn/A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@jridgewell/set-array/-/set-array-1.2.1.tgz}
    name: '@jridgewell/set-array'
    version: 1.2.1
    engines: {node: '>=6.0.0'}
    dev: true

  registry.npmjs.org/@jridgewell/source-map@0.3.6:
    resolution: {integrity: sha512-1ZJTZebgqllO79ue2bm3rIGud/bOe0pP5BjSRCRxxYkEZS8STV7zN84UBbiYu7jy+eCKSnVIUgoWWE/tt+shMQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@jridgewell/source-map/-/source-map-0.3.6.tgz}
    name: '@jridgewell/source-map'
    version: 0.3.6
    dependencies:
      '@jridgewell/gen-mapping': registry.npmjs.org/@jridgewell/gen-mapping@0.3.5
      '@jridgewell/trace-mapping': registry.npmjs.org/@jridgewell/trace-mapping@0.3.25
    dev: true

  registry.npmjs.org/@jridgewell/sourcemap-codec@1.4.15:
    resolution: {integrity: sha512-eF2rxCRulEKXHTRiDrDy6erMYWqNw4LPdQ8UQA4huuxaQsVeRPFl2oM8oDGxMFhJUWZf9McpLtJasDDZb/Bpeg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.4.15.tgz}
    name: '@jridgewell/sourcemap-codec'
    version: 1.4.15
    dev: true

  registry.npmjs.org/@jridgewell/trace-mapping@0.3.25:
    resolution: {integrity: sha512-vNk6aEwybGtawWmy/PzwnGDOjCkLWSD2wqvjGGAgOAwCGWySYXfYoxt00IJkTF+8Lb57DwOb3Aa0o9CApepiYQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.25.tgz}
    name: '@jridgewell/trace-mapping'
    version: 0.3.25
    dependencies:
      '@jridgewell/resolve-uri': registry.npmjs.org/@jridgewell/resolve-uri@3.1.2
      '@jridgewell/sourcemap-codec': registry.npmjs.org/@jridgewell/sourcemap-codec@1.4.15
    dev: true

  registry.npmjs.org/@js-preview/docx@1.6.0:
    resolution: {integrity: sha512-ACK9lk2ySe1MIlBv1HwA6NUZH4qenMQ8sozbQVz+Y5Vty0MqPz6Nd8NGJHSmP86IUOsn1eCBkIaD3K5pHnZBUw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@js-preview/docx/-/docx-1.6.0.tgz}
    name: '@js-preview/docx'
    version: 1.6.0
    dev: false

  registry.npmjs.org/@js-preview/excel@1.7.5:
    resolution: {integrity: sha512-P2v/rKwqkk45HuQClm5JChcD7+rGRXfsafYlyKAnFiARl8Xh2CNER6YOHpC2xCeRqdAGy1QBAckQvComj17KCA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@js-preview/excel/-/excel-1.7.5.tgz}
    name: '@js-preview/excel'
    version: 1.7.5
    dev: false

  registry.npmjs.org/@js-preview/pdf@2.0.1:
    resolution: {integrity: sha512-cPj02nnKHMDwTUzAUqELCkbFkEusPQ1aADUv+Wt84tIkbtIqEf6+ZZZIh2/0G9p1wksYFso3OJzWo9qfYiCA9g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@js-preview/pdf/-/pdf-2.0.1.tgz}
    name: '@js-preview/pdf'
    version: 2.0.1
    dev: false

  registry.npmjs.org/@leichtgewicht/ip-codec@2.0.5:
    resolution: {integrity: sha512-Vo+PSpZG2/fmgmiNzYK9qWRh8h/CHrwD0mo1h1DzL4yzHNSfWYujGTYsWGreD000gcgmZ7K4Ys6Tx9TxtsKdDw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@leichtgewicht/ip-codec/-/ip-codec-2.0.5.tgz}
    name: '@leichtgewicht/ip-codec'
    version: 2.0.5
    dev: true

  registry.npmjs.org/@nicolo-ribaudo/eslint-scope-5-internals@5.1.1-v1:
    resolution: {integrity: sha512-54/JRvkLIzzDWshCWfuhadfrfZVPiElY8Fcgmg1HroEly/EDSszzhBAsarCux+D/kOslTRquNzuyGSmUSTTHGg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@nicolo-ribaudo/eslint-scope-5-internals/-/eslint-scope-5-internals-5.1.1-v1.tgz}
    name: '@nicolo-ribaudo/eslint-scope-5-internals'
    version: 5.1.1-v1
    dependencies:
      eslint-scope: registry.npmjs.org/eslint-scope@5.1.1
    dev: true

  registry.npmjs.org/@node-ipc/js-queue@2.0.3:
    resolution: {integrity: sha512-fL1wpr8hhD5gT2dA1qifeVaoDFlQR5es8tFuKqjHX+kdOtdNHnxkVZbtIrR2rxnMFvehkjaZRNV2H/gPXlb0hw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@node-ipc/js-queue/-/js-queue-2.0.3.tgz}
    name: '@node-ipc/js-queue'
    version: 2.0.3
    engines: {node: '>=1.0.0'}
    dependencies:
      easy-stack: registry.npmjs.org/easy-stack@1.0.1
    dev: true

  registry.npmjs.org/@nodelib/fs.scandir@2.1.5:
    resolution: {integrity: sha512-vq24Bq3ym5HEQm2NKCr3yXDwjc7vTsEThRDnkp2DK9p1uqLR+DHurm/NOTo0KG7HYHU7eppKZj3MyqYuMBf62g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@nodelib/fs.scandir/-/fs.scandir-2.1.5.tgz}
    name: '@nodelib/fs.scandir'
    version: 2.1.5
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.stat': registry.npmjs.org/@nodelib/fs.stat@2.0.5
      run-parallel: registry.npmjs.org/run-parallel@1.2.0
    dev: true

  registry.npmjs.org/@nodelib/fs.stat@2.0.5:
    resolution: {integrity: sha512-RkhPPp2zrqDAQA/2jNhnztcPAlv64XdhIp7a7454A5ovI7Bukxgt7MX7udwAu3zg1DcpPU0rz3VV1SeaqvY4+A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@nodelib/fs.stat/-/fs.stat-2.0.5.tgz}
    name: '@nodelib/fs.stat'
    version: 2.0.5
    engines: {node: '>= 8'}
    dev: true

  registry.npmjs.org/@nodelib/fs.walk@1.2.8:
    resolution: {integrity: sha512-oGB+UxlgWcgQkgwo8GcEGwemoTFt3FIO9ababBmaGwXIoBKZ+GTy0pP185beGg7Llih/NSHSV2XAs1lnznocSg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@nodelib/fs.walk/-/fs.walk-1.2.8.tgz}
    name: '@nodelib/fs.walk'
    version: 1.2.8
    engines: {node: '>= 8'}
    dependencies:
      '@nodelib/fs.scandir': registry.npmjs.org/@nodelib/fs.scandir@2.1.5
      fastq: registry.npmjs.org/fastq@1.17.1
    dev: true

  registry.npmjs.org/@polka/url@1.0.0-next.25:
    resolution: {integrity: sha512-j7P6Rgr3mmtdkeDGTe0E/aYyWEWVtc5yFXtHCRHs28/jptDEWfaVOc5T7cblqy1XKPPfCxJc/8DwQ5YgLOZOVQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@polka/url/-/url-1.0.0-next.25.tgz}
    name: '@polka/url'
    version: 1.0.0-next.25
    dev: true

  registry.npmjs.org/@sideway/address@4.1.5:
    resolution: {integrity: sha512-IqO/DUQHUkPeixNQ8n0JA6102hT9CmaljNTPmQ1u8MEhBo/R4Q8eKLN/vGZxuebwOroDB4cbpjheD4+/sKFK4Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@sideway/address/-/address-4.1.5.tgz}
    name: '@sideway/address'
    version: 4.1.5
    dependencies:
      '@hapi/hoek': registry.npmjs.org/@hapi/hoek@9.3.0
    dev: true

  registry.npmjs.org/@sideway/formula@3.0.1:
    resolution: {integrity: sha512-/poHZJJVjx3L+zVD6g9KgHfYnb443oi7wLu/XKojDviHy6HOEOA6z1Trk5aR1dGcmPenJEgb2sK2I80LeS3MIg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@sideway/formula/-/formula-3.0.1.tgz}
    name: '@sideway/formula'
    version: 3.0.1
    dev: true

  registry.npmjs.org/@sideway/pinpoint@2.0.0:
    resolution: {integrity: sha512-RNiOoTPkptFtSVzQevY/yWtZwf/RxyVnPy/OcA9HBM3MlGDnBEYL5B41H0MTn0Uec8Hi+2qUtTfG2WWZBmMejQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@sideway/pinpoint/-/pinpoint-2.0.0.tgz}
    name: '@sideway/pinpoint'
    version: 2.0.0
    dev: true

  registry.npmjs.org/@soda/friendly-errors-webpack-plugin@1.8.1(webpack@5.91.0):
    resolution: {integrity: sha512-h2ooWqP8XuFqTXT+NyAFbrArzfQA7R6HTezADrvD9Re8fxMLTPPniLdqVTdDaO0eIoLaAwKT+d6w+5GeTk7Vbg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@soda/friendly-errors-webpack-plugin/-/friendly-errors-webpack-plugin-1.8.1.tgz}
    id: registry.npmjs.org/@soda/friendly-errors-webpack-plugin/1.8.1
    name: '@soda/friendly-errors-webpack-plugin'
    version: 1.8.1
    engines: {node: '>=8.0.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      chalk: registry.npmjs.org/chalk@3.0.0
      error-stack-parser: registry.npmjs.org/error-stack-parser@2.1.4
      string-width: registry.npmjs.org/string-width@4.2.3
      strip-ansi: registry.npmjs.org/strip-ansi@6.0.1
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/@soda/get-current-script@1.0.2:
    resolution: {integrity: sha512-T7VNNlYVM1SgQ+VsMYhnDkcGmWhQdL0bDyGm5TlQ3GBXnJscEClUUOKduWTmm2zCnvNLC1hc3JpuXjs/nFOc5w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@soda/get-current-script/-/get-current-script-1.0.2.tgz}
    name: '@soda/get-current-script'
    version: 1.0.2
    dev: true

  registry.npmjs.org/@trysound/sax@0.2.0:
    resolution: {integrity: sha512-L7z9BgrNEcYyUYtF+HaEfiS5ebkh9jXqbszz7pC0hRBPaatV0XjSD3+eHrpqFemQfgwiFF0QPIarnIihIDn7OA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@trysound/sax/-/sax-0.2.0.tgz}
    name: '@trysound/sax'
    version: 0.2.0
    engines: {node: '>=10.13.0'}
    dev: true

  registry.npmjs.org/@types/body-parser@1.19.5:
    resolution: {integrity: sha512-fB3Zu92ucau0iQ0JMCFQE7b/dv8Ot07NI3KaZIkIUNXq82k4eBAqUaneXfleGY9JWskeS9y+u0nXMyspcuQrCg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/body-parser/-/body-parser-1.19.5.tgz}
    name: '@types/body-parser'
    version: 1.19.5
    dependencies:
      '@types/connect': registry.npmjs.org/@types/connect@3.4.38
      '@types/node': registry.npmjs.org/@types/node@20.12.7
    dev: true

  registry.npmjs.org/@types/bonjour@3.5.13:
    resolution: {integrity: sha512-z9fJ5Im06zvUL548KvYNecEVlA7cVDkGUi6kZusb04mpyEFKCIZJvloCcmpmLaIahDpOQGHaHmG6imtPMmPXGQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/bonjour/-/bonjour-3.5.13.tgz}
    name: '@types/bonjour'
    version: 3.5.13
    dependencies:
      '@types/node': registry.npmjs.org/@types/node@20.12.7
    dev: true

  registry.npmjs.org/@types/connect-history-api-fallback@1.5.4:
    resolution: {integrity: sha512-n6Cr2xS1h4uAulPRdlw6Jl6s1oG8KrVilPN2yUITEs+K48EzMJJ3W1xy8K5eWuFvjp3R74AOIGSmp2UfBJ8HFw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/connect-history-api-fallback/-/connect-history-api-fallback-1.5.4.tgz}
    name: '@types/connect-history-api-fallback'
    version: 1.5.4
    dependencies:
      '@types/express-serve-static-core': registry.npmjs.org/@types/express-serve-static-core@4.19.0
      '@types/node': registry.npmjs.org/@types/node@20.12.7
    dev: true

  registry.npmjs.org/@types/connect@3.4.38:
    resolution: {integrity: sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz}
    name: '@types/connect'
    version: 3.4.38
    dependencies:
      '@types/node': registry.npmjs.org/@types/node@20.12.7
    dev: true

  registry.npmjs.org/@types/eslint-scope@3.7.7:
    resolution: {integrity: sha512-MzMFlSLBqNF2gcHWO0G1vP/YQyfvrxZ0bF+u7mzUdZ1/xK4A4sru+nraZz5i3iEIk1l1uyicaDVTB4QbbEkAYg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/eslint-scope/-/eslint-scope-3.7.7.tgz}
    name: '@types/eslint-scope'
    version: 3.7.7
    dependencies:
      '@types/eslint': registry.npmjs.org/@types/eslint@8.56.9
      '@types/estree': registry.npmjs.org/@types/estree@1.0.5
    dev: true

  registry.npmjs.org/@types/eslint@8.56.9:
    resolution: {integrity: sha512-W4W3KcqzjJ0sHg2vAq9vfml6OhsJ53TcUjUqfzzZf/EChUtwspszj/S0pzMxnfRcO55/iGq47dscXw71Fxc4Zg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/eslint/-/eslint-8.56.9.tgz}
    name: '@types/eslint'
    version: 8.56.9
    dependencies:
      '@types/estree': registry.npmjs.org/@types/estree@1.0.5
      '@types/json-schema': registry.npmjs.org/@types/json-schema@7.0.15
    dev: true

  registry.npmjs.org/@types/estree@1.0.5:
    resolution: {integrity: sha512-/kYRxGDLWzHOB7q+wtSUQlFrtcdUccpfy+X+9iMBpHK8QLLhx2wIPYuS5DYtR9Wa/YlZAbIovy7qVdB1Aq6Lyw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/estree/-/estree-1.0.5.tgz}
    name: '@types/estree'
    version: 1.0.5
    dev: true

  registry.npmjs.org/@types/express-serve-static-core@4.19.0:
    resolution: {integrity: sha512-bGyep3JqPCRry1wq+O5n7oiBgGWmeIJXPjXXCo8EK0u8duZGSYar7cGqd3ML2JUsLGeB7fmc06KYo9fLGWqPvQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/express-serve-static-core/-/express-serve-static-core-4.19.0.tgz}
    name: '@types/express-serve-static-core'
    version: 4.19.0
    dependencies:
      '@types/node': registry.npmjs.org/@types/node@20.12.7
      '@types/qs': registry.npmjs.org/@types/qs@6.9.15
      '@types/range-parser': registry.npmjs.org/@types/range-parser@1.2.7
      '@types/send': registry.npmjs.org/@types/send@0.17.4
    dev: true

  registry.npmjs.org/@types/express@4.17.21:
    resolution: {integrity: sha512-ejlPM315qwLpaQlQDTjPdsUFSc6ZsP4AN6AlWnogPjQ7CVi7PYF3YVz+CY3jE2pwYf7E/7HlDAN0rV2GxTG0HQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/express/-/express-4.17.21.tgz}
    name: '@types/express'
    version: 4.17.21
    dependencies:
      '@types/body-parser': registry.npmjs.org/@types/body-parser@1.19.5
      '@types/express-serve-static-core': registry.npmjs.org/@types/express-serve-static-core@4.19.0
      '@types/qs': registry.npmjs.org/@types/qs@6.9.15
      '@types/serve-static': registry.npmjs.org/@types/serve-static@1.15.7
    dev: true

  registry.npmjs.org/@types/html-minifier-terser@6.1.0:
    resolution: {integrity: sha512-oh/6byDPnL1zeNXFrDXFLyZjkr1MsBG667IM792caf1L2UPOOMf65NFzjUH/ltyfwjAGfs1rsX1eftK0jC/KIg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz}
    name: '@types/html-minifier-terser'
    version: 6.1.0
    dev: true

  registry.npmjs.org/@types/http-errors@2.0.4:
    resolution: {integrity: sha512-D0CFMMtydbJAegzOyHjtiKPLlvnm3iTZyZRSZoLq2mRhDdmLfIWOCYPfQJ4cu2erKghU++QvjcUjp/5h7hESpA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/http-errors/-/http-errors-2.0.4.tgz}
    name: '@types/http-errors'
    version: 2.0.4
    dev: true

  registry.npmjs.org/@types/http-proxy@1.17.14:
    resolution: {integrity: sha512-SSrD0c1OQzlFX7pGu1eXxSEjemej64aaNPRhhVYUGqXh0BtldAAx37MG8btcumvpgKyZp1F5Gn3JkktdxiFv6w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/http-proxy/-/http-proxy-1.17.14.tgz}
    name: '@types/http-proxy'
    version: 1.17.14
    dependencies:
      '@types/node': registry.npmjs.org/@types/node@20.12.7
    dev: true

  registry.npmjs.org/@types/json-schema@7.0.15:
    resolution: {integrity: sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz}
    name: '@types/json-schema'
    version: 7.0.15
    dev: true

  registry.npmjs.org/@types/mime@1.3.5:
    resolution: {integrity: sha512-/pyBZWSLD2n0dcHE3hq8s8ZvcETHtEuF+3E7XVt0Ig2nvsVQXdghHVcEkIWjy9A0wKfTn97a/PSDYohKIlnP/w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/mime/-/mime-1.3.5.tgz}
    name: '@types/mime'
    version: 1.3.5
    dev: true

  registry.npmjs.org/@types/minimist@1.2.5:
    resolution: {integrity: sha512-hov8bUuiLiyFPGyFPE1lwWhmzYbirOXQNNo40+y3zow8aFVTeyn3VWL0VFFfdNddA8S4Vf0Tc062rzyNr7Paag==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/minimist/-/minimist-1.2.5.tgz}
    name: '@types/minimist'
    version: 1.2.5
    dev: true

  registry.npmjs.org/@types/node-forge@1.3.11:
    resolution: {integrity: sha512-FQx220y22OKNTqaByeBGqHWYz4cl94tpcxeFdvBo3wjG6XPBuZ0BNgNZRV5J5TFmmcsJ4IzsLkmGRiQbnYsBEQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/node-forge/-/node-forge-1.3.11.tgz}
    name: '@types/node-forge'
    version: 1.3.11
    dependencies:
      '@types/node': registry.npmjs.org/@types/node@20.12.7
    dev: true

  registry.npmjs.org/@types/node@20.12.7:
    resolution: {integrity: sha512-wq0cICSkRLVaf3UGLMGItu/PtdY7oaXaI/RVU+xliKVOtRna3PRY57ZDfztpDL0n11vfymMUnXv8QwYCO7L1wg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/node/-/node-20.12.7.tgz}
    name: '@types/node'
    version: 20.12.7
    dependencies:
      undici-types: registry.npmjs.org/undici-types@5.26.5
    dev: true

  registry.npmjs.org/@types/normalize-package-data@2.4.4:
    resolution: {integrity: sha512-37i+OaWTh9qeK4LSHPsyRC7NahnGotNuZvjLSgcPzblpHB3rrCJxAOgI5gCdKm7coonsaX1Of0ILiTcnZjbfxA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/normalize-package-data/-/normalize-package-data-2.4.4.tgz}
    name: '@types/normalize-package-data'
    version: 2.4.4
    dev: true

  registry.npmjs.org/@types/parse-json@4.0.2:
    resolution: {integrity: sha512-dISoDXWWQwUquiKsyZ4Ng+HX2KsPL7LyHKHQwgGFEA3IaKac4Obd+h2a/a6waisAoepJlBcx9paWqjA8/HVjCw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/parse-json/-/parse-json-4.0.2.tgz}
    name: '@types/parse-json'
    version: 4.0.2
    dev: true

  registry.npmjs.org/@types/qs@6.9.15:
    resolution: {integrity: sha512-uXHQKES6DQKKCLh441Xv/dwxOq1TVS3JPUMlEqoEglvlhR6Mxnlew/Xq/LRVHpLyk7iK3zODe1qYHIMltO7XGg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/qs/-/qs-6.9.15.tgz}
    name: '@types/qs'
    version: 6.9.15
    dev: true

  registry.npmjs.org/@types/range-parser@1.2.7:
    resolution: {integrity: sha512-hKormJbkJqzQGhziax5PItDUTMAM9uE2XXQmM37dyd4hVM+5aVl7oVxMVUiVQn2oCQFN/LKCZdvSM0pFRqbSmQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/range-parser/-/range-parser-1.2.7.tgz}
    name: '@types/range-parser'
    version: 1.2.7
    dev: true

  registry.npmjs.org/@types/retry@0.12.0:
    resolution: {integrity: sha512-wWKOClTTiizcZhXnPY4wikVAwmdYHp8q6DmC+EJUzAMsycb7HB32Kh9RN4+0gExjmPmZSAQjgURXIGATPegAvA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/retry/-/retry-0.12.0.tgz}
    name: '@types/retry'
    version: 0.12.0
    dev: true

  registry.npmjs.org/@types/send@0.17.4:
    resolution: {integrity: sha512-x2EM6TJOybec7c52BX0ZspPodMsQUd5L6PRwOunVyVUhXiBSKf3AezDL8Dgvgt5o0UfKNfuA0eMLr2wLT4AiBA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/send/-/send-0.17.4.tgz}
    name: '@types/send'
    version: 0.17.4
    dependencies:
      '@types/mime': registry.npmjs.org/@types/mime@1.3.5
      '@types/node': registry.npmjs.org/@types/node@20.12.7
    dev: true

  registry.npmjs.org/@types/serve-index@1.9.4:
    resolution: {integrity: sha512-qLpGZ/c2fhSs5gnYsQxtDEq3Oy8SXPClIXkW5ghvAvsNuVSA8k+gCONcUCS/UjLEYvYps+e8uBtfgXgvhwfNug==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/serve-index/-/serve-index-1.9.4.tgz}
    name: '@types/serve-index'
    version: 1.9.4
    dependencies:
      '@types/express': registry.npmjs.org/@types/express@4.17.21
    dev: true

  registry.npmjs.org/@types/serve-static@1.15.7:
    resolution: {integrity: sha512-W8Ym+h8nhuRwaKPaDw34QUkwsGi6Rc4yYqvKFo5rm2FUEhCFbzVWrxXUxuKK8TASjWsysJY0nsmNCGhCOIsrOw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/serve-static/-/serve-static-1.15.7.tgz}
    name: '@types/serve-static'
    version: 1.15.7
    dependencies:
      '@types/http-errors': registry.npmjs.org/@types/http-errors@2.0.4
      '@types/node': registry.npmjs.org/@types/node@20.12.7
      '@types/send': registry.npmjs.org/@types/send@0.17.4
    dev: true

  registry.npmjs.org/@types/sockjs@0.3.36:
    resolution: {integrity: sha512-MK9V6NzAS1+Ud7JV9lJLFqW85VbC9dq3LmwZCuBe4wBDgKC0Kj/jd8Xl+nSviU+Qc3+m7umHHyHg//2KSa0a0Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/sockjs/-/sockjs-0.3.36.tgz}
    name: '@types/sockjs'
    version: 0.3.36
    dependencies:
      '@types/node': registry.npmjs.org/@types/node@20.12.7
    dev: true

  registry.npmjs.org/@types/ws@8.5.10:
    resolution: {integrity: sha512-vmQSUcfalpIq0R9q7uTo2lXs6eGIpt9wtnLdMv9LVpIjCA/+ufZRozlVoVelIYixx1ugCBKDhn89vnsEGOCx9A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@types/ws/-/ws-8.5.10.tgz}
    name: '@types/ws'
    version: 8.5.10
    dependencies:
      '@types/node': registry.npmjs.org/@types/node@20.12.7
    dev: true

  registry.npmjs.org/@vue-office/docx@1.6.0(vue-demi@0.14.7)(vue@2.7.16):
    resolution: {integrity: sha512-OKEapOPq223uszA8mRSOWPhdfBchJa6sIqP46eMrMMe5RSUrG9m3QE/o0JBIaMgxDrtNd7aI9CvF2kDvb7G2hw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue-office/docx/-/docx-1.6.0.tgz}
    id: registry.npmjs.org/@vue-office/docx/1.6.0
    name: '@vue-office/docx'
    version: 1.6.0
    requiresBuild: true
    peerDependencies:
      '@vue/composition-api': ^1.7.1
      vue: ^2.0.0 || >=3.0.0
      vue-demi: ^0.14.6
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
    dependencies:
      vue: registry.npmjs.org/vue@2.7.16
      vue-demi: registry.npmjs.org/vue-demi@0.14.7(vue@2.7.16)
    dev: false

  registry.npmjs.org/@vue-office/excel@1.7.6(vue-demi@0.14.7)(vue@2.7.16):
    resolution: {integrity: sha512-HbhzudczlmPu5/p6ES4lAQDN6L9kQ0zoZOjDzCYmHxKj9O+IwAjzSwNIEAE1e0lolHQofZvvjtWDMZ/LNvKgTA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue-office/excel/-/excel-1.7.6.tgz}
    id: registry.npmjs.org/@vue-office/excel/1.7.6
    name: '@vue-office/excel'
    version: 1.7.6
    requiresBuild: true
    peerDependencies:
      '@vue/composition-api': ^1.7.1
      vue: ^2.0.0 || >=3.0.0
      vue-demi: ^0.14.6
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
    dependencies:
      vue: registry.npmjs.org/vue@2.7.16
      vue-demi: registry.npmjs.org/vue-demi@0.14.7(vue@2.7.16)
    dev: false

  registry.npmjs.org/@vue-office/pdf@2.0.1(vue-demi@0.14.7)(vue@2.7.16):
    resolution: {integrity: sha512-lddmgwqbApKoKmdhZhrzmg8gfv/MRq9eG6TLhqYDEENCLJ5TKpxtcOrEi/Kq/ZcZawkXP5WYAUdqGxP8FP4W7g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue-office/pdf/-/pdf-2.0.1.tgz}
    id: registry.npmjs.org/@vue-office/pdf/2.0.1
    name: '@vue-office/pdf'
    version: 2.0.1
    requiresBuild: true
    peerDependencies:
      '@vue/composition-api': ^1.7.1
      vue: ^2.0.0 || >=3.0.0
      vue-demi: ^0.14.6
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
    dependencies:
      vue: registry.npmjs.org/vue@2.7.16
      vue-demi: registry.npmjs.org/vue-demi@0.14.7(vue@2.7.16)
    dev: false

  registry.npmjs.org/@vue/babel-helper-vue-jsx-merge-props@1.4.0:
    resolution: {integrity: sha512-JkqXfCkUDp4PIlFdDQ0TdXoIejMtTHP67/pvxlgeY+u5k3LEdKuWZ3LK6xkxo52uDoABIVyRwqVkfLQJhk7VBA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-1.4.0.tgz}
    name: '@vue/babel-helper-vue-jsx-merge-props'
    version: 1.4.0
    dev: true

  registry.npmjs.org/@vue/babel-helper-vue-transform-on@1.2.2:
    resolution: {integrity: sha512-nOttamHUR3YzdEqdM/XXDyCSdxMA9VizUKoroLX6yTyRtggzQMHXcmwh8a7ZErcJttIBIc9s68a1B8GZ+Dmvsw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-helper-vue-transform-on/-/babel-helper-vue-transform-on-1.2.2.tgz}
    name: '@vue/babel-helper-vue-transform-on'
    version: 1.2.2
    dev: true

  registry.npmjs.org/@vue/babel-plugin-jsx@1.2.2(@babel/core@7.24.4):
    resolution: {integrity: sha512-nYTkZUVTu4nhP199UoORePsql0l+wj7v/oyQjtThUVhJl1U+6qHuoVhIvR3bf7eVKjbCK+Cs2AWd7mi9Mpz9rA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-plugin-jsx/-/babel-plugin-jsx-1.2.2.tgz}
    id: registry.npmjs.org/@vue/babel-plugin-jsx/1.2.2
    name: '@vue/babel-plugin-jsx'
    version: 1.2.2
    peerDependencies:
      '@babel/core': ^7.0.0-0
    peerDependenciesMeta:
      '@babel/core':
        optional: true
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-module-imports': registry.npmjs.org/@babel/helper-module-imports@7.22.15
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/plugin-syntax-jsx': registry.npmjs.org/@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.4)
      '@babel/template': registry.npmjs.org/@babel/template@7.24.0
      '@babel/traverse': registry.npmjs.org/@babel/traverse@7.24.1
      '@babel/types': registry.npmjs.org/@babel/types@7.24.0
      '@vue/babel-helper-vue-transform-on': registry.npmjs.org/@vue/babel-helper-vue-transform-on@1.2.2
      '@vue/babel-plugin-resolve-type': registry.npmjs.org/@vue/babel-plugin-resolve-type@1.2.2(@babel/core@7.24.4)
      camelcase: registry.npmjs.org/camelcase@6.3.0
      html-tags: registry.npmjs.org/html-tags@3.3.1
      svg-tags: registry.npmjs.org/svg-tags@1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@vue/babel-plugin-resolve-type@1.2.2(@babel/core@7.24.4):
    resolution: {integrity: sha512-EntyroPwNg5IPVdUJupqs0CFzuf6lUrVvCspmv2J1FITLeGnUCuoGNNk78dgCusxEiYj6RMkTJflGSxk5aIC4A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-plugin-resolve-type/-/babel-plugin-resolve-type-1.2.2.tgz}
    id: registry.npmjs.org/@vue/babel-plugin-resolve-type/1.2.2
    name: '@vue/babel-plugin-resolve-type'
    version: 1.2.2
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/code-frame': registry.npmjs.org/@babel/code-frame@7.24.2
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-module-imports': registry.npmjs.org/@babel/helper-module-imports@7.22.15
      '@babel/helper-plugin-utils': registry.npmjs.org/@babel/helper-plugin-utils@7.24.0
      '@babel/parser': registry.npmjs.org/@babel/parser@7.24.4
      '@vue/compiler-sfc': registry.npmjs.org/@vue/compiler-sfc@3.4.23
    dev: true

  registry.npmjs.org/@vue/babel-plugin-transform-vue-jsx@1.4.0(@babel/core@7.24.4):
    resolution: {integrity: sha512-Fmastxw4MMx0vlgLS4XBX0XiBbUFzoMGeVXuMV08wyOfXdikAFqBTuYPR0tlk+XskL19EzHc39SgjrPGY23JnA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-plugin-transform-vue-jsx/-/babel-plugin-transform-vue-jsx-1.4.0.tgz}
    id: registry.npmjs.org/@vue/babel-plugin-transform-vue-jsx/1.4.0
    name: '@vue/babel-plugin-transform-vue-jsx'
    version: 1.4.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-module-imports': registry.npmjs.org/@babel/helper-module-imports@7.24.3
      '@babel/plugin-syntax-jsx': registry.npmjs.org/@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.4)
      '@vue/babel-helper-vue-jsx-merge-props': registry.npmjs.org/@vue/babel-helper-vue-jsx-merge-props@1.4.0
      html-tags: registry.npmjs.org/html-tags@2.0.0
      lodash.kebabcase: registry.npmjs.org/lodash.kebabcase@4.1.1
      svg-tags: registry.npmjs.org/svg-tags@1.0.0
    dev: true

  registry.npmjs.org/@vue/babel-preset-app@5.0.8(@babel/core@7.24.4)(core-js@3.37.0)(vue@2.7.16):
    resolution: {integrity: sha512-yl+5qhpjd8e1G4cMXfORkkBlvtPCIgmRf3IYCWYDKIQ7m+PPa5iTm4feiNmCMD6yGqQWMhhK/7M3oWGL9boKwg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-preset-app/-/babel-preset-app-5.0.8.tgz}
    id: registry.npmjs.org/@vue/babel-preset-app/5.0.8
    name: '@vue/babel-preset-app'
    version: 5.0.8
    peerDependencies:
      '@babel/core': '*'
      core-js: ^3
      vue: ^2 || ^3.2.13
    peerDependenciesMeta:
      core-js:
        optional: true
      vue:
        optional: true
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-compilation-targets': registry.npmjs.org/@babel/helper-compilation-targets@7.23.6
      '@babel/helper-module-imports': registry.npmjs.org/@babel/helper-module-imports@7.24.3
      '@babel/plugin-proposal-class-properties': registry.npmjs.org/@babel/plugin-proposal-class-properties@7.18.6(@babel/core@7.24.4)
      '@babel/plugin-proposal-decorators': registry.npmjs.org/@babel/plugin-proposal-decorators@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-syntax-dynamic-import': registry.npmjs.org/@babel/plugin-syntax-dynamic-import@7.8.3(@babel/core@7.24.4)
      '@babel/plugin-syntax-jsx': registry.npmjs.org/@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.4)
      '@babel/plugin-transform-runtime': registry.npmjs.org/@babel/plugin-transform-runtime@7.24.3(@babel/core@7.24.4)
      '@babel/preset-env': registry.npmjs.org/@babel/preset-env@7.24.4(@babel/core@7.24.4)
      '@babel/runtime': registry.npmjs.org/@babel/runtime@7.24.4
      '@vue/babel-plugin-jsx': registry.npmjs.org/@vue/babel-plugin-jsx@1.2.2(@babel/core@7.24.4)
      '@vue/babel-preset-jsx': registry.npmjs.org/@vue/babel-preset-jsx@1.4.0(@babel/core@7.24.4)(vue@2.7.16)
      babel-plugin-dynamic-import-node: registry.npmjs.org/babel-plugin-dynamic-import-node@2.3.3
      core-js: registry.npmjs.org/core-js@3.37.0
      core-js-compat: registry.npmjs.org/core-js-compat@3.37.0
      semver: registry.npmjs.org/semver@7.6.0
      vue: registry.npmjs.org/vue@2.7.16
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/@vue/babel-preset-jsx@1.4.0(@babel/core@7.24.4)(vue@2.7.16):
    resolution: {integrity: sha512-QmfRpssBOPZWL5xw7fOuHNifCQcNQC1PrOo/4fu6xlhlKJJKSA3HqX92Nvgyx8fqHZTUGMPHmFA+IDqwXlqkSA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-preset-jsx/-/babel-preset-jsx-1.4.0.tgz}
    id: registry.npmjs.org/@vue/babel-preset-jsx/1.4.0
    name: '@vue/babel-preset-jsx'
    version: 1.4.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
      vue: '*'
    peerDependenciesMeta:
      vue:
        optional: true
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@vue/babel-helper-vue-jsx-merge-props': registry.npmjs.org/@vue/babel-helper-vue-jsx-merge-props@1.4.0
      '@vue/babel-plugin-transform-vue-jsx': registry.npmjs.org/@vue/babel-plugin-transform-vue-jsx@1.4.0(@babel/core@7.24.4)
      '@vue/babel-sugar-composition-api-inject-h': registry.npmjs.org/@vue/babel-sugar-composition-api-inject-h@1.4.0(@babel/core@7.24.4)
      '@vue/babel-sugar-composition-api-render-instance': registry.npmjs.org/@vue/babel-sugar-composition-api-render-instance@1.4.0(@babel/core@7.24.4)
      '@vue/babel-sugar-functional-vue': registry.npmjs.org/@vue/babel-sugar-functional-vue@1.4.0(@babel/core@7.24.4)
      '@vue/babel-sugar-inject-h': registry.npmjs.org/@vue/babel-sugar-inject-h@1.4.0(@babel/core@7.24.4)
      '@vue/babel-sugar-v-model': registry.npmjs.org/@vue/babel-sugar-v-model@1.4.0(@babel/core@7.24.4)
      '@vue/babel-sugar-v-on': registry.npmjs.org/@vue/babel-sugar-v-on@1.4.0(@babel/core@7.24.4)
      vue: registry.npmjs.org/vue@2.7.16
    dev: true

  registry.npmjs.org/@vue/babel-sugar-composition-api-inject-h@1.4.0(@babel/core@7.24.4):
    resolution: {integrity: sha512-VQq6zEddJHctnG4w3TfmlVp5FzDavUSut/DwR0xVoe/mJKXyMcsIibL42wPntozITEoY90aBV0/1d2KjxHU52g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-sugar-composition-api-inject-h/-/babel-sugar-composition-api-inject-h-1.4.0.tgz}
    id: registry.npmjs.org/@vue/babel-sugar-composition-api-inject-h/1.4.0
    name: '@vue/babel-sugar-composition-api-inject-h'
    version: 1.4.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/plugin-syntax-jsx': registry.npmjs.org/@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@vue/babel-sugar-composition-api-render-instance@1.4.0(@babel/core@7.24.4):
    resolution: {integrity: sha512-6ZDAzcxvy7VcnCjNdHJ59mwK02ZFuP5CnucloidqlZwVQv5CQLijc3lGpR7MD3TWFi78J7+a8J56YxbCtHgT9Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-sugar-composition-api-render-instance/-/babel-sugar-composition-api-render-instance-1.4.0.tgz}
    id: registry.npmjs.org/@vue/babel-sugar-composition-api-render-instance/1.4.0
    name: '@vue/babel-sugar-composition-api-render-instance'
    version: 1.4.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/plugin-syntax-jsx': registry.npmjs.org/@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@vue/babel-sugar-functional-vue@1.4.0(@babel/core@7.24.4):
    resolution: {integrity: sha512-lTEB4WUFNzYt2In6JsoF9sAYVTo84wC4e+PoZWSgM6FUtqRJz7wMylaEhSRgG71YF+wfLD6cc9nqVeXN2rwBvw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-sugar-functional-vue/-/babel-sugar-functional-vue-1.4.0.tgz}
    id: registry.npmjs.org/@vue/babel-sugar-functional-vue/1.4.0
    name: '@vue/babel-sugar-functional-vue'
    version: 1.4.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/plugin-syntax-jsx': registry.npmjs.org/@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@vue/babel-sugar-inject-h@1.4.0(@babel/core@7.24.4):
    resolution: {integrity: sha512-muwWrPKli77uO2fFM7eA3G1lAGnERuSz2NgAxuOLzrsTlQl8W4G+wwbM4nB6iewlKbwKRae3nL03UaF5ffAPMA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-sugar-inject-h/-/babel-sugar-inject-h-1.4.0.tgz}
    id: registry.npmjs.org/@vue/babel-sugar-inject-h/1.4.0
    name: '@vue/babel-sugar-inject-h'
    version: 1.4.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/plugin-syntax-jsx': registry.npmjs.org/@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.4)
    dev: true

  registry.npmjs.org/@vue/babel-sugar-v-model@1.4.0(@babel/core@7.24.4):
    resolution: {integrity: sha512-0t4HGgXb7WHYLBciZzN5s0Hzqan4Ue+p/3FdQdcaHAb7s5D9WZFGoSxEZHrR1TFVZlAPu1bejTKGeAzaaG3NCQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-sugar-v-model/-/babel-sugar-v-model-1.4.0.tgz}
    id: registry.npmjs.org/@vue/babel-sugar-v-model/1.4.0
    name: '@vue/babel-sugar-v-model'
    version: 1.4.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/plugin-syntax-jsx': registry.npmjs.org/@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.4)
      '@vue/babel-helper-vue-jsx-merge-props': registry.npmjs.org/@vue/babel-helper-vue-jsx-merge-props@1.4.0
      '@vue/babel-plugin-transform-vue-jsx': registry.npmjs.org/@vue/babel-plugin-transform-vue-jsx@1.4.0(@babel/core@7.24.4)
      camelcase: registry.npmjs.org/camelcase@5.3.1
      html-tags: registry.npmjs.org/html-tags@2.0.0
      svg-tags: registry.npmjs.org/svg-tags@1.0.0
    dev: true

  registry.npmjs.org/@vue/babel-sugar-v-on@1.4.0(@babel/core@7.24.4):
    resolution: {integrity: sha512-m+zud4wKLzSKgQrWwhqRObWzmTuyzl6vOP7024lrpeJM4x2UhQtRDLgYjXAw9xBXjCwS0pP9kXjg91F9ZNo9JA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/babel-sugar-v-on/-/babel-sugar-v-on-1.4.0.tgz}
    id: registry.npmjs.org/@vue/babel-sugar-v-on/1.4.0
    name: '@vue/babel-sugar-v-on'
    version: 1.4.0
    peerDependencies:
      '@babel/core': ^7.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/plugin-syntax-jsx': registry.npmjs.org/@babel/plugin-syntax-jsx@7.24.1(@babel/core@7.24.4)
      '@vue/babel-plugin-transform-vue-jsx': registry.npmjs.org/@vue/babel-plugin-transform-vue-jsx@1.4.0(@babel/core@7.24.4)
      camelcase: registry.npmjs.org/camelcase@5.3.1
    dev: true

  registry.npmjs.org/@vue/cli-overlay@5.0.8:
    resolution: {integrity: sha512-KmtievE/B4kcXp6SuM2gzsnSd8WebkQpg3XaB6GmFh1BJGRqa1UiW9up7L/Q67uOdTigHxr5Ar2lZms4RcDjwQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/cli-overlay/-/cli-overlay-5.0.8.tgz}
    name: '@vue/cli-overlay'
    version: 5.0.8
    dev: true

  registry.npmjs.org/@vue/cli-plugin-babel@5.0.8(@vue/cli-service@5.0.8)(core-js@3.37.0)(vue@2.7.16):
    resolution: {integrity: sha512-a4qqkml3FAJ3auqB2kN2EMPocb/iu0ykeELwed+9B1c1nQ1HKgslKMHMPavYx3Cd/QAx2mBD4hwKBqZXEI/CsQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/cli-plugin-babel/-/cli-plugin-babel-5.0.8.tgz}
    id: registry.npmjs.org/@vue/cli-plugin-babel/5.0.8
    name: '@vue/cli-plugin-babel'
    version: 5.0.8
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0 || ^5.0.0-0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@vue/babel-preset-app': registry.npmjs.org/@vue/babel-preset-app@5.0.8(@babel/core@7.24.4)(core-js@3.37.0)(vue@2.7.16)
      '@vue/cli-service': registry.npmjs.org/@vue/cli-service@5.0.8(vue-template-compiler@2.7.16)(vue@2.7.16)
      '@vue/cli-shared-utils': registry.npmjs.org/@vue/cli-shared-utils@5.0.8
      babel-loader: registry.npmjs.org/babel-loader@8.3.0(@babel/core@7.24.4)(webpack@5.91.0)
      thread-loader: registry.npmjs.org/thread-loader@3.0.4(webpack@5.91.0)
      webpack: registry.npmjs.org/webpack@5.91.0
    transitivePeerDependencies:
      - '@swc/core'
      - core-js
      - encoding
      - esbuild
      - supports-color
      - uglify-js
      - vue
      - webpack-cli
    dev: true

  registry.npmjs.org/@vue/cli-plugin-eslint@5.0.8(@vue/cli-service@5.0.8)(eslint@7.32.0):
    resolution: {integrity: sha512-d11+I5ONYaAPW1KyZj9GlrV/E6HZePq5L5eAF5GgoVdu6sxr6bDgEoxzhcS1Pk2eh8rn1MxG/FyyR+eCBj/CNg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/cli-plugin-eslint/-/cli-plugin-eslint-5.0.8.tgz}
    id: registry.npmjs.org/@vue/cli-plugin-eslint/5.0.8
    name: '@vue/cli-plugin-eslint'
    version: 5.0.8
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0 || ^5.0.0-0
      eslint: '>=7.5.0'
    dependencies:
      '@vue/cli-service': registry.npmjs.org/@vue/cli-service@5.0.8(vue-template-compiler@2.7.16)(vue@2.7.16)
      '@vue/cli-shared-utils': registry.npmjs.org/@vue/cli-shared-utils@5.0.8
      eslint: registry.npmjs.org/eslint@7.32.0
      eslint-webpack-plugin: registry.npmjs.org/eslint-webpack-plugin@3.2.0(eslint@7.32.0)(webpack@5.91.0)
      globby: registry.npmjs.org/globby@11.1.0
      webpack: registry.npmjs.org/webpack@5.91.0
      yorkie: registry.npmjs.org/yorkie@2.0.0
    transitivePeerDependencies:
      - '@swc/core'
      - encoding
      - esbuild
      - uglify-js
      - webpack-cli
    dev: true

  registry.npmjs.org/@vue/cli-plugin-router@5.0.8(@vue/cli-service@5.0.8):
    resolution: {integrity: sha512-Gmv4dsGdAsWPqVijz3Ux2OS2HkMrWi1ENj2cYL75nUeL+Xj5HEstSqdtfZ0b1q9NCce+BFB6QnHfTBXc/fCvMg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/cli-plugin-router/-/cli-plugin-router-5.0.8.tgz}
    id: registry.npmjs.org/@vue/cli-plugin-router/5.0.8
    name: '@vue/cli-plugin-router'
    version: 5.0.8
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0 || ^5.0.0-0
    dependencies:
      '@vue/cli-service': registry.npmjs.org/@vue/cli-service@5.0.8(vue-template-compiler@2.7.16)(vue@2.7.16)
      '@vue/cli-shared-utils': registry.npmjs.org/@vue/cli-shared-utils@5.0.8
    transitivePeerDependencies:
      - encoding
    dev: true

  registry.npmjs.org/@vue/cli-plugin-vuex@5.0.8(@vue/cli-service@5.0.8):
    resolution: {integrity: sha512-HSYWPqrunRE5ZZs8kVwiY6oWcn95qf/OQabwLfprhdpFWAGtLStShjsGED2aDpSSeGAskQETrtR/5h7VqgIlBA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/cli-plugin-vuex/-/cli-plugin-vuex-5.0.8.tgz}
    id: registry.npmjs.org/@vue/cli-plugin-vuex/5.0.8
    name: '@vue/cli-plugin-vuex'
    version: 5.0.8
    peerDependencies:
      '@vue/cli-service': ^3.0.0 || ^4.0.0 || ^5.0.0-0
    dependencies:
      '@vue/cli-service': registry.npmjs.org/@vue/cli-service@5.0.8(vue-template-compiler@2.7.16)(vue@2.7.16)
    dev: true

  registry.npmjs.org/@vue/cli-service@5.0.8(vue-template-compiler@2.7.16)(vue@2.7.16):
    resolution: {integrity: sha512-nV7tYQLe7YsTtzFrfOMIHc5N2hp5lHG2rpYr0aNja9rNljdgcPZLyQRb2YRivTHqTv7lI962UXFURcpStHgyFw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/cli-service/-/cli-service-5.0.8.tgz}
    id: registry.npmjs.org/@vue/cli-service/5.0.8
    name: '@vue/cli-service'
    version: 5.0.8
    engines: {node: ^12.0.0 || >= 14.0.0}
    hasBin: true
    peerDependencies:
      cache-loader: '*'
      less-loader: '*'
      pug-plain-loader: '*'
      raw-loader: '*'
      sass-loader: '*'
      stylus-loader: '*'
      vue-template-compiler: ^2.0.0
      webpack-sources: '*'
    peerDependenciesMeta:
      cache-loader:
        optional: true
      less-loader:
        optional: true
      pug-plain-loader:
        optional: true
      raw-loader:
        optional: true
      sass-loader:
        optional: true
      stylus-loader:
        optional: true
      vue-template-compiler:
        optional: true
      webpack-sources:
        optional: true
    dependencies:
      '@babel/helper-compilation-targets': registry.npmjs.org/@babel/helper-compilation-targets@7.23.6
      '@soda/friendly-errors-webpack-plugin': registry.npmjs.org/@soda/friendly-errors-webpack-plugin@1.8.1(webpack@5.91.0)
      '@soda/get-current-script': registry.npmjs.org/@soda/get-current-script@1.0.2
      '@types/minimist': registry.npmjs.org/@types/minimist@1.2.5
      '@vue/cli-overlay': registry.npmjs.org/@vue/cli-overlay@5.0.8
      '@vue/cli-plugin-router': registry.npmjs.org/@vue/cli-plugin-router@5.0.8(@vue/cli-service@5.0.8)
      '@vue/cli-plugin-vuex': registry.npmjs.org/@vue/cli-plugin-vuex@5.0.8(@vue/cli-service@5.0.8)
      '@vue/cli-shared-utils': registry.npmjs.org/@vue/cli-shared-utils@5.0.8
      '@vue/component-compiler-utils': registry.npmjs.org/@vue/component-compiler-utils@3.3.0
      '@vue/vue-loader-v15': registry.npmjs.org/vue-loader@15.11.1(css-loader@6.11.0)(vue-template-compiler@2.7.16)(webpack@5.91.0)
      '@vue/web-component-wrapper': registry.npmjs.org/@vue/web-component-wrapper@1.3.0
      acorn: registry.npmjs.org/acorn@8.11.3
      acorn-walk: registry.npmjs.org/acorn-walk@8.3.2
      address: registry.npmjs.org/address@1.2.2
      autoprefixer: registry.npmjs.org/autoprefixer@10.4.19(postcss@8.4.38)
      browserslist: registry.npmjs.org/browserslist@4.23.0
      case-sensitive-paths-webpack-plugin: registry.npmjs.org/case-sensitive-paths-webpack-plugin@2.4.0
      cli-highlight: registry.npmjs.org/cli-highlight@2.1.11
      clipboardy: registry.npmjs.org/clipboardy@2.3.0
      cliui: registry.npmjs.org/cliui@7.0.4
      copy-webpack-plugin: registry.npmjs.org/copy-webpack-plugin@9.1.0(webpack@5.91.0)
      css-loader: registry.npmjs.org/css-loader@6.11.0(webpack@5.91.0)
      css-minimizer-webpack-plugin: registry.npmjs.org/css-minimizer-webpack-plugin@3.4.1(webpack@5.91.0)
      cssnano: registry.npmjs.org/cssnano@5.1.15(postcss@8.4.38)
      debug: registry.npmjs.org/debug@4.3.4
      default-gateway: registry.npmjs.org/default-gateway@6.0.3
      dotenv: registry.npmjs.org/dotenv@10.0.0
      dotenv-expand: registry.npmjs.org/dotenv-expand@5.1.0
      fs-extra: registry.npmjs.org/fs-extra@9.1.0
      globby: registry.npmjs.org/globby@11.1.0
      hash-sum: registry.npmjs.org/hash-sum@2.0.0
      html-webpack-plugin: registry.npmjs.org/html-webpack-plugin@5.6.0(webpack@5.91.0)
      is-file-esm: registry.npmjs.org/is-file-esm@1.0.0
      launch-editor-middleware: registry.npmjs.org/launch-editor-middleware@2.6.1
      lodash.defaultsdeep: registry.npmjs.org/lodash.defaultsdeep@4.6.1
      lodash.mapvalues: registry.npmjs.org/lodash.mapvalues@4.6.0
      mini-css-extract-plugin: registry.npmjs.org/mini-css-extract-plugin@2.9.0(webpack@5.91.0)
      minimist: registry.npmjs.org/minimist@1.2.8
      module-alias: registry.npmjs.org/module-alias@2.2.3
      portfinder: registry.npmjs.org/portfinder@1.0.32
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-loader: registry.npmjs.org/postcss-loader@6.2.1(postcss@8.4.38)(webpack@5.91.0)
      progress-webpack-plugin: registry.npmjs.org/progress-webpack-plugin@1.0.16(webpack@5.91.0)
      ssri: registry.npmjs.org/ssri@8.0.1
      terser-webpack-plugin: registry.npmjs.org/terser-webpack-plugin@5.3.10(webpack@5.91.0)
      thread-loader: registry.npmjs.org/thread-loader@3.0.4(webpack@5.91.0)
      vue-loader: registry.npmjs.org/vue-loader@17.4.2(vue@2.7.16)(webpack@5.91.0)
      vue-style-loader: registry.npmjs.org/vue-style-loader@4.1.3
      vue-template-compiler: registry.npmjs.org/vue-template-compiler@2.7.16
      webpack: registry.npmjs.org/webpack@5.91.0
      webpack-bundle-analyzer: registry.npmjs.org/webpack-bundle-analyzer@4.10.2
      webpack-chain: registry.npmjs.org/webpack-chain@6.5.1
      webpack-dev-server: registry.npmjs.org/webpack-dev-server@4.15.2(debug@4.3.4)(webpack@5.91.0)
      webpack-merge: registry.npmjs.org/webpack-merge@5.10.0
      webpack-virtual-modules: registry.npmjs.org/webpack-virtual-modules@0.4.6
      whatwg-fetch: registry.npmjs.org/whatwg-fetch@3.6.20
    transitivePeerDependencies:
      - '@parcel/css'
      - '@rspack/core'
      - '@swc/core'
      - '@vue/compiler-sfc'
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - bufferutil
      - clean-css
      - coffee-script
      - csso
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - encoding
      - esbuild
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - prettier
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - supports-color
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - uglify-js
      - underscore
      - utf-8-validate
      - vash
      - velocityjs
      - vue
      - walrus
      - webpack-cli
      - whiskers
    dev: true

  registry.npmjs.org/@vue/cli-shared-utils@5.0.8:
    resolution: {integrity: sha512-uK2YB7bBVuQhjOJF+O52P9yFMXeJVj7ozqJkwYE9PlMHL1LMHjtCYm4cSdOebuPzyP+/9p0BimM/OqxsevIopQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/cli-shared-utils/-/cli-shared-utils-5.0.8.tgz}
    name: '@vue/cli-shared-utils'
    version: 5.0.8
    dependencies:
      '@achrinza/node-ipc': registry.npmjs.org/@achrinza/node-ipc@9.2.8
      chalk: registry.npmjs.org/chalk@4.1.2
      execa: registry.npmjs.org/execa@1.0.0
      joi: registry.npmjs.org/joi@17.12.3
      launch-editor: registry.npmjs.org/launch-editor@2.6.1
      lru-cache: registry.npmjs.org/lru-cache@6.0.0
      node-fetch: registry.npmjs.org/node-fetch@2.7.0
      open: registry.npmjs.org/open@8.4.2
      ora: registry.npmjs.org/ora@5.4.1
      read-pkg: registry.npmjs.org/read-pkg@5.2.0
      semver: registry.npmjs.org/semver@7.6.0
      strip-ansi: registry.npmjs.org/strip-ansi@6.0.1
    transitivePeerDependencies:
      - encoding
    dev: true

  registry.npmjs.org/@vue/compiler-core@3.4.23:
    resolution: {integrity: sha512-HAFmuVEwNqNdmk+w4VCQ2pkLk1Vw4XYiiyxEp3z/xvl14aLTUBw2OfVH3vBcx+FtGsynQLkkhK410Nah1N2yyQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/compiler-core/-/compiler-core-3.4.23.tgz}
    name: '@vue/compiler-core'
    version: 3.4.23
    dependencies:
      '@babel/parser': registry.npmjs.org/@babel/parser@7.24.4
      '@vue/shared': registry.npmjs.org/@vue/shared@3.4.23
      entities: registry.npmjs.org/entities@4.5.0
      estree-walker: registry.npmjs.org/estree-walker@2.0.2
      source-map-js: registry.npmjs.org/source-map-js@1.2.0
    dev: true

  registry.npmjs.org/@vue/compiler-dom@3.4.23:
    resolution: {integrity: sha512-t0b9WSTnCRrzsBGrDd1LNR5HGzYTr7LX3z6nNBG+KGvZLqrT0mY6NsMzOqlVMBKKXKVuusbbB5aOOFgTY+senw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/compiler-dom/-/compiler-dom-3.4.23.tgz}
    name: '@vue/compiler-dom'
    version: 3.4.23
    dependencies:
      '@vue/compiler-core': registry.npmjs.org/@vue/compiler-core@3.4.23
      '@vue/shared': registry.npmjs.org/@vue/shared@3.4.23
    dev: true

  registry.npmjs.org/@vue/compiler-sfc@2.7.16:
    resolution: {integrity: sha512-KWhJ9k5nXuNtygPU7+t1rX6baZeqOYLEforUPjgNDBnLicfHCoi48H87Q8XyLZOrNNsmhuwKqtpDQWjEFe6Ekg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-2.7.16.tgz}
    name: '@vue/compiler-sfc'
    version: 2.7.16
    dependencies:
      '@babel/parser': registry.npmjs.org/@babel/parser@7.24.4
      postcss: registry.npmjs.org/postcss@8.4.38
      source-map: registry.npmjs.org/source-map@0.6.1
    optionalDependencies:
      prettier: registry.npmjs.org/prettier@2.8.8

  registry.npmjs.org/@vue/compiler-sfc@3.4.23:
    resolution: {integrity: sha512-fSDTKTfzaRX1kNAUiaj8JB4AokikzStWgHooMhaxyjZerw624L+IAP/fvI4ZwMpwIh8f08PVzEnu4rg8/Npssw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/compiler-sfc/-/compiler-sfc-3.4.23.tgz}
    name: '@vue/compiler-sfc'
    version: 3.4.23
    dependencies:
      '@babel/parser': registry.npmjs.org/@babel/parser@7.24.4
      '@vue/compiler-core': registry.npmjs.org/@vue/compiler-core@3.4.23
      '@vue/compiler-dom': registry.npmjs.org/@vue/compiler-dom@3.4.23
      '@vue/compiler-ssr': registry.npmjs.org/@vue/compiler-ssr@3.4.23
      '@vue/shared': registry.npmjs.org/@vue/shared@3.4.23
      estree-walker: registry.npmjs.org/estree-walker@2.0.2
      magic-string: registry.npmjs.org/magic-string@0.30.10
      postcss: registry.npmjs.org/postcss@8.4.38
      source-map-js: registry.npmjs.org/source-map-js@1.2.0
    dev: true

  registry.npmjs.org/@vue/compiler-ssr@3.4.23:
    resolution: {integrity: sha512-hb6Uj2cYs+tfqz71Wj6h3E5t6OKvb4MVcM2Nl5i/z1nv1gjEhw+zYaNOV+Xwn+SSN/VZM0DgANw5TuJfxfezPg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/compiler-ssr/-/compiler-ssr-3.4.23.tgz}
    name: '@vue/compiler-ssr'
    version: 3.4.23
    dependencies:
      '@vue/compiler-dom': registry.npmjs.org/@vue/compiler-dom@3.4.23
      '@vue/shared': registry.npmjs.org/@vue/shared@3.4.23
    dev: true

  registry.npmjs.org/@vue/component-compiler-utils@3.3.0:
    resolution: {integrity: sha512-97sfH2mYNU+2PzGrmK2haqffDpVASuib9/w2/noxiFi31Z54hW+q3izKQXXQZSNhtiUpAI36uSuYepeBe4wpHQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/component-compiler-utils/-/component-compiler-utils-3.3.0.tgz}
    name: '@vue/component-compiler-utils'
    version: 3.3.0
    dependencies:
      consolidate: registry.npmjs.org/consolidate@0.15.1
      hash-sum: registry.npmjs.org/hash-sum@1.0.2
      lru-cache: registry.npmjs.org/lru-cache@4.1.5
      merge-source-map: registry.npmjs.org/merge-source-map@1.1.0
      postcss: registry.npmjs.org/postcss@7.0.39
      postcss-selector-parser: registry.npmjs.org/postcss-selector-parser@6.0.16
      source-map: registry.npmjs.org/source-map@0.6.1
      vue-template-es2015-compiler: registry.npmjs.org/vue-template-es2015-compiler@1.9.1
    optionalDependencies:
      prettier: registry.npmjs.org/prettier@2.8.8
    transitivePeerDependencies:
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - vash
      - velocityjs
      - walrus
      - whiskers
    dev: true

  registry.npmjs.org/@vue/shared@3.4.23:
    resolution: {integrity: sha512-wBQ0gvf+SMwsCQOyusNw/GoXPV47WGd1xB5A1Pgzy0sQ3Bi5r5xm3n+92y3gCnB3MWqnRDdvfkRGxhKtbBRNgg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/shared/-/shared-3.4.23.tgz}
    name: '@vue/shared'
    version: 3.4.23
    dev: true

  registry.npmjs.org/@vue/web-component-wrapper@1.3.0:
    resolution: {integrity: sha512-Iu8Tbg3f+emIIMmI2ycSI8QcEuAUgPTgHwesDU1eKMLE4YC/c/sFbGc70QgMq31ijRftV0R7vCm9co6rldCeOA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@vue/web-component-wrapper/-/web-component-wrapper-1.3.0.tgz}
    name: '@vue/web-component-wrapper'
    version: 1.3.0
    dev: true

  registry.npmjs.org/@webassemblyjs/ast@1.12.1:
    resolution: {integrity: sha512-EKfMUOPRRUTy5UII4qJDGPpqfwjOmZ5jeGFwid9mnoqIFK+e0vqoi1qH56JpmZSzEL53jKnNzScdmftJyG5xWg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/ast/-/ast-1.12.1.tgz}
    name: '@webassemblyjs/ast'
    version: 1.12.1
    dependencies:
      '@webassemblyjs/helper-numbers': registry.npmjs.org/@webassemblyjs/helper-numbers@1.11.6
      '@webassemblyjs/helper-wasm-bytecode': registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode@1.11.6
    dev: true

  registry.npmjs.org/@webassemblyjs/floating-point-hex-parser@1.11.6:
    resolution: {integrity: sha512-ejAj9hfRJ2XMsNHk/v6Fu2dGS+i4UaXBXGemOfQ/JfQ6mdQg/WXtwleQRLLS4OvfDhv8rYnVwH27YJLMyYsxhw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/floating-point-hex-parser/-/floating-point-hex-parser-1.11.6.tgz}
    name: '@webassemblyjs/floating-point-hex-parser'
    version: 1.11.6
    dev: true

  registry.npmjs.org/@webassemblyjs/helper-api-error@1.11.6:
    resolution: {integrity: sha512-o0YkoP4pVu4rN8aTJgAyj9hC2Sv5UlkzCHhxqWj8butaLvnpdc2jOwh4ewE6CX0txSfLn/UYaV/pheS2Txg//Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/helper-api-error/-/helper-api-error-1.11.6.tgz}
    name: '@webassemblyjs/helper-api-error'
    version: 1.11.6
    dev: true

  registry.npmjs.org/@webassemblyjs/helper-buffer@1.12.1:
    resolution: {integrity: sha512-nzJwQw99DNDKr9BVCOZcLuJJUlqkJh+kVzVl6Fmq/tI5ZtEyWT1KZMyOXltXLZJmDtvLCDgwsyrkohEtopTXCw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/helper-buffer/-/helper-buffer-1.12.1.tgz}
    name: '@webassemblyjs/helper-buffer'
    version: 1.12.1
    dev: true

  registry.npmjs.org/@webassemblyjs/helper-numbers@1.11.6:
    resolution: {integrity: sha512-vUIhZ8LZoIWHBohiEObxVm6hwP034jwmc9kuq5GdHZH0wiLVLIPcMCdpJzG4C11cHoQ25TFIQj9kaVADVX7N3g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/helper-numbers/-/helper-numbers-1.11.6.tgz}
    name: '@webassemblyjs/helper-numbers'
    version: 1.11.6
    dependencies:
      '@webassemblyjs/floating-point-hex-parser': registry.npmjs.org/@webassemblyjs/floating-point-hex-parser@1.11.6
      '@webassemblyjs/helper-api-error': registry.npmjs.org/@webassemblyjs/helper-api-error@1.11.6
      '@xtuc/long': registry.npmjs.org/@xtuc/long@4.2.2
    dev: true

  registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode@1.11.6:
    resolution: {integrity: sha512-sFFHKwcmBprO9e7Icf0+gddyWYDViL8bpPjJJl0WHxCdETktXdmtWLGVzoHbqUcY4Be1LkNfwTmXOJUFZYSJdA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode/-/helper-wasm-bytecode-1.11.6.tgz}
    name: '@webassemblyjs/helper-wasm-bytecode'
    version: 1.11.6
    dev: true

  registry.npmjs.org/@webassemblyjs/helper-wasm-section@1.12.1:
    resolution: {integrity: sha512-Jif4vfB6FJlUlSbgEMHUyk1j234GTNG9dBJ4XJdOySoj518Xj0oGsNi59cUQF4RRMS9ouBUxDDdyBVfPTypa5g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/helper-wasm-section/-/helper-wasm-section-1.12.1.tgz}
    name: '@webassemblyjs/helper-wasm-section'
    version: 1.12.1
    dependencies:
      '@webassemblyjs/ast': registry.npmjs.org/@webassemblyjs/ast@1.12.1
      '@webassemblyjs/helper-buffer': registry.npmjs.org/@webassemblyjs/helper-buffer@1.12.1
      '@webassemblyjs/helper-wasm-bytecode': registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode@1.11.6
      '@webassemblyjs/wasm-gen': registry.npmjs.org/@webassemblyjs/wasm-gen@1.12.1
    dev: true

  registry.npmjs.org/@webassemblyjs/ieee754@1.11.6:
    resolution: {integrity: sha512-LM4p2csPNvbij6U1f19v6WR56QZ8JcHg3QIJTlSwzFcmx6WSORicYj6I63f9yU1kEUtrpG+kjkiIAkevHpDXrg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/ieee754/-/ieee754-1.11.6.tgz}
    name: '@webassemblyjs/ieee754'
    version: 1.11.6
    dependencies:
      '@xtuc/ieee754': registry.npmjs.org/@xtuc/ieee754@1.2.0
    dev: true

  registry.npmjs.org/@webassemblyjs/leb128@1.11.6:
    resolution: {integrity: sha512-m7a0FhE67DQXgouf1tbN5XQcdWoNgaAuoULHIfGFIEVKA6tu/edls6XnIlkmS6FrXAquJRPni3ZZKjw6FSPjPQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/leb128/-/leb128-1.11.6.tgz}
    name: '@webassemblyjs/leb128'
    version: 1.11.6
    dependencies:
      '@xtuc/long': registry.npmjs.org/@xtuc/long@4.2.2
    dev: true

  registry.npmjs.org/@webassemblyjs/utf8@1.11.6:
    resolution: {integrity: sha512-vtXf2wTQ3+up9Zsg8sa2yWiQpzSsMyXj0qViVP6xKGCUT8p8YJ6HqI7l5eCnWx1T/FYdsv07HQs2wTFbbof/RA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/utf8/-/utf8-1.11.6.tgz}
    name: '@webassemblyjs/utf8'
    version: 1.11.6
    dev: true

  registry.npmjs.org/@webassemblyjs/wasm-edit@1.12.1:
    resolution: {integrity: sha512-1DuwbVvADvS5mGnXbE+c9NfA8QRcZ6iKquqjjmR10k6o+zzsRVesil54DKexiowcFCPdr/Q0qaMgB01+SQ1u6g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/wasm-edit/-/wasm-edit-1.12.1.tgz}
    name: '@webassemblyjs/wasm-edit'
    version: 1.12.1
    dependencies:
      '@webassemblyjs/ast': registry.npmjs.org/@webassemblyjs/ast@1.12.1
      '@webassemblyjs/helper-buffer': registry.npmjs.org/@webassemblyjs/helper-buffer@1.12.1
      '@webassemblyjs/helper-wasm-bytecode': registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode@1.11.6
      '@webassemblyjs/helper-wasm-section': registry.npmjs.org/@webassemblyjs/helper-wasm-section@1.12.1
      '@webassemblyjs/wasm-gen': registry.npmjs.org/@webassemblyjs/wasm-gen@1.12.1
      '@webassemblyjs/wasm-opt': registry.npmjs.org/@webassemblyjs/wasm-opt@1.12.1
      '@webassemblyjs/wasm-parser': registry.npmjs.org/@webassemblyjs/wasm-parser@1.12.1
      '@webassemblyjs/wast-printer': registry.npmjs.org/@webassemblyjs/wast-printer@1.12.1
    dev: true

  registry.npmjs.org/@webassemblyjs/wasm-gen@1.12.1:
    resolution: {integrity: sha512-TDq4Ojh9fcohAw6OIMXqiIcTq5KUXTGRkVxbSo1hQnSy6lAM5GSdfwWeSxpAo0YzgsgF182E/U0mDNhuA0tW7w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/wasm-gen/-/wasm-gen-1.12.1.tgz}
    name: '@webassemblyjs/wasm-gen'
    version: 1.12.1
    dependencies:
      '@webassemblyjs/ast': registry.npmjs.org/@webassemblyjs/ast@1.12.1
      '@webassemblyjs/helper-wasm-bytecode': registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode@1.11.6
      '@webassemblyjs/ieee754': registry.npmjs.org/@webassemblyjs/ieee754@1.11.6
      '@webassemblyjs/leb128': registry.npmjs.org/@webassemblyjs/leb128@1.11.6
      '@webassemblyjs/utf8': registry.npmjs.org/@webassemblyjs/utf8@1.11.6
    dev: true

  registry.npmjs.org/@webassemblyjs/wasm-opt@1.12.1:
    resolution: {integrity: sha512-Jg99j/2gG2iaz3hijw857AVYekZe2SAskcqlWIZXjji5WStnOpVoat3gQfT/Q5tb2djnCjBtMocY/Su1GfxPBg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/wasm-opt/-/wasm-opt-1.12.1.tgz}
    name: '@webassemblyjs/wasm-opt'
    version: 1.12.1
    dependencies:
      '@webassemblyjs/ast': registry.npmjs.org/@webassemblyjs/ast@1.12.1
      '@webassemblyjs/helper-buffer': registry.npmjs.org/@webassemblyjs/helper-buffer@1.12.1
      '@webassemblyjs/wasm-gen': registry.npmjs.org/@webassemblyjs/wasm-gen@1.12.1
      '@webassemblyjs/wasm-parser': registry.npmjs.org/@webassemblyjs/wasm-parser@1.12.1
    dev: true

  registry.npmjs.org/@webassemblyjs/wasm-parser@1.12.1:
    resolution: {integrity: sha512-xikIi7c2FHXysxXe3COrVUPSheuBtpcfhbpFj4gmu7KRLYOzANztwUU0IbsqvMqzuNK2+glRGWCEqZo1WCLyAQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/wasm-parser/-/wasm-parser-1.12.1.tgz}
    name: '@webassemblyjs/wasm-parser'
    version: 1.12.1
    dependencies:
      '@webassemblyjs/ast': registry.npmjs.org/@webassemblyjs/ast@1.12.1
      '@webassemblyjs/helper-api-error': registry.npmjs.org/@webassemblyjs/helper-api-error@1.11.6
      '@webassemblyjs/helper-wasm-bytecode': registry.npmjs.org/@webassemblyjs/helper-wasm-bytecode@1.11.6
      '@webassemblyjs/ieee754': registry.npmjs.org/@webassemblyjs/ieee754@1.11.6
      '@webassemblyjs/leb128': registry.npmjs.org/@webassemblyjs/leb128@1.11.6
      '@webassemblyjs/utf8': registry.npmjs.org/@webassemblyjs/utf8@1.11.6
    dev: true

  registry.npmjs.org/@webassemblyjs/wast-printer@1.12.1:
    resolution: {integrity: sha512-+X4WAlOisVWQMikjbcvY2e0rwPsKQ9F688lksZhBcPycBBuii3O7m8FACbDMWDojpAqvjIncrG8J0XHKyQfVeA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@webassemblyjs/wast-printer/-/wast-printer-1.12.1.tgz}
    name: '@webassemblyjs/wast-printer'
    version: 1.12.1
    dependencies:
      '@webassemblyjs/ast': registry.npmjs.org/@webassemblyjs/ast@1.12.1
      '@xtuc/long': registry.npmjs.org/@xtuc/long@4.2.2
    dev: true

  registry.npmjs.org/@xtuc/ieee754@1.2.0:
    resolution: {integrity: sha512-DX8nKgqcGwsc0eJSqYt5lwP4DH5FlHnmuWWBRy7X0NcaGR0ZtuyeESgMwTYVEtxmsNGY+qit4QYT/MIYTOTPeA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@xtuc/ieee754/-/ieee754-1.2.0.tgz}
    name: '@xtuc/ieee754'
    version: 1.2.0
    dev: true

  registry.npmjs.org/@xtuc/long@4.2.2:
    resolution: {integrity: sha512-NuHqBY1PB/D8xU6s/thBgOAiAP7HOYDQ32+BFZILJ8ivkUkAHQnWfn6WhL79Owj1qmUnoN/YPhktdIoucipkAQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/@xtuc/long/-/long-4.2.2.tgz}
    name: '@xtuc/long'
    version: 4.2.2
    dev: true

  registry.npmjs.org/accepts@1.3.8:
    resolution: {integrity: sha512-PYAthTa2m2VKxuvSD3DPC/Gy+U+sOA1LAuT8mkmRuvw+NACSaeXEQ+NHcVF7rONl6qcaxV3Uuemwawk+7+SJLw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/accepts/-/accepts-1.3.8.tgz}
    name: accepts
    version: 1.3.8
    engines: {node: '>= 0.6'}
    dependencies:
      mime-types: registry.npmjs.org/mime-types@2.1.35
      negotiator: registry.npmjs.org/negotiator@0.6.3
    dev: true

  registry.npmjs.org/acorn-import-assertions@1.9.0(acorn@8.11.3):
    resolution: {integrity: sha512-cmMwop9x+8KFhxvKrKfPYmN6/pKTYYHBqLa0DfvVZcKMJWNyWLnaqND7dx/qn66R7ewM1UX5XMaDVP5wlVTaVA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/acorn-import-assertions/-/acorn-import-assertions-1.9.0.tgz}
    id: registry.npmjs.org/acorn-import-assertions/1.9.0
    name: acorn-import-assertions
    version: 1.9.0
    peerDependencies:
      acorn: ^8
    dependencies:
      acorn: registry.npmjs.org/acorn@8.11.3
    dev: true

  registry.npmjs.org/acorn-jsx@5.3.2(acorn@7.4.1):
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz}
    id: registry.npmjs.org/acorn-jsx/5.3.2
    name: acorn-jsx
    version: 5.3.2
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: registry.npmjs.org/acorn@7.4.1
    dev: true

  registry.npmjs.org/acorn-jsx@5.3.2(acorn@8.11.3):
    resolution: {integrity: sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz}
    id: registry.npmjs.org/acorn-jsx/5.3.2
    name: acorn-jsx
    version: 5.3.2
    peerDependencies:
      acorn: ^6.0.0 || ^7.0.0 || ^8.0.0
    dependencies:
      acorn: registry.npmjs.org/acorn@8.11.3
    dev: true

  registry.npmjs.org/acorn-walk@8.3.2:
    resolution: {integrity: sha512-cjkyv4OtNCIeqhHrfS81QWXoCBPExR/J62oyEqepVw8WaQeSqpW2uhuLPh1m9eWhDuOo/jUXVTlifvesOWp/4A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.2.tgz}
    name: acorn-walk
    version: 8.3.2
    engines: {node: '>=0.4.0'}
    dev: true

  registry.npmjs.org/acorn@7.4.1:
    resolution: {integrity: sha512-nQyp0o1/mNdbTO1PO6kHkwSrmgZ0MT/jCCpNiwbUjGoRN4dlBhqJtoQuCnEOKzgTVwg0ZWiCoQy6SxMebQVh8A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/acorn/-/acorn-7.4.1.tgz}
    name: acorn
    version: 7.4.1
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  registry.npmjs.org/acorn@8.11.3:
    resolution: {integrity: sha512-Y9rRfJG5jcKOE0CLisYbojUjIrIEE7AGMzA/Sm4BslANhbS+cDMpgBdcPT91oJ7OuJ9hYJBx59RjbhxVnrF8Xg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/acorn/-/acorn-8.11.3.tgz}
    name: acorn
    version: 8.11.3
    engines: {node: '>=0.4.0'}
    hasBin: true
    dev: true

  registry.npmjs.org/address@1.2.2:
    resolution: {integrity: sha512-4B/qKCfeE/ODUaAUpSwfzazo5x29WD4r3vXiWsB7I2mSDAihwEqKO+g8GELZUQSSAo5e1XTYh3ZVfLyxBc12nA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/address/-/address-1.2.2.tgz}
    name: address
    version: 1.2.2
    engines: {node: '>= 10.0.0'}
    dev: true

  registry.npmjs.org/ajv-formats@2.1.1(ajv@8.12.0):
    resolution: {integrity: sha512-Wx0Kx52hxE7C18hkMEggYlEifqWZtYaRgouJor+WMdPnQyEK13vgEWyVNup7SoeeoLMsr4kf5h6dOW11I15MUA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ajv-formats/-/ajv-formats-2.1.1.tgz}
    id: registry.npmjs.org/ajv-formats/2.1.1
    name: ajv-formats
    version: 2.1.1
    peerDependencies:
      ajv: ^8.0.0
    peerDependenciesMeta:
      ajv:
        optional: true
    dependencies:
      ajv: registry.npmjs.org/ajv@8.12.0
    dev: true

  registry.npmjs.org/ajv-keywords@3.5.2(ajv@6.12.6):
    resolution: {integrity: sha512-5p6WTN0DdTGVQk6VjcEju19IgaHudalcfabD7yhDGeA6bcQnmL+CpveLJq/3hvfwd1aof6L386Ougkx6RfyMIQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-3.5.2.tgz}
    id: registry.npmjs.org/ajv-keywords/3.5.2
    name: ajv-keywords
    version: 3.5.2
    peerDependencies:
      ajv: ^6.9.1
    dependencies:
      ajv: registry.npmjs.org/ajv@6.12.6
    dev: true

  registry.npmjs.org/ajv-keywords@5.1.0(ajv@8.12.0):
    resolution: {integrity: sha512-YCS/JNFAUyr5vAuhk1DWm1CBxRHW9LbJ2ozWeemrIqpbsqKjHVxYPyi5GC0rjZIT5JxJ3virVTS8wk4i/Z+krw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ajv-keywords/-/ajv-keywords-5.1.0.tgz}
    id: registry.npmjs.org/ajv-keywords/5.1.0
    name: ajv-keywords
    version: 5.1.0
    peerDependencies:
      ajv: ^8.8.2
    dependencies:
      ajv: registry.npmjs.org/ajv@8.12.0
      fast-deep-equal: registry.npmjs.org/fast-deep-equal@3.1.3
    dev: true

  registry.npmjs.org/ajv@6.12.6:
    resolution: {integrity: sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz}
    name: ajv
    version: 6.12.6
    dependencies:
      fast-deep-equal: registry.npmjs.org/fast-deep-equal@3.1.3
      fast-json-stable-stringify: registry.npmjs.org/fast-json-stable-stringify@2.1.0
      json-schema-traverse: registry.npmjs.org/json-schema-traverse@0.4.1
      uri-js: registry.npmjs.org/uri-js@4.4.1
    dev: true

  registry.npmjs.org/ajv@8.12.0:
    resolution: {integrity: sha512-sRu1kpcO9yLtYxBKvqfTeh9KzZEwO3STyX1HT+4CaDzC6HpTGYhIhPIzj9XuKU7KYDwnaeh5hcOwjy1QuJzBPA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ajv/-/ajv-8.12.0.tgz}
    name: ajv
    version: 8.12.0
    dependencies:
      fast-deep-equal: registry.npmjs.org/fast-deep-equal@3.1.3
      json-schema-traverse: registry.npmjs.org/json-schema-traverse@1.0.0
      require-from-string: registry.npmjs.org/require-from-string@2.0.2
      uri-js: registry.npmjs.org/uri-js@4.4.1
    dev: true

  registry.npmjs.org/ansi-colors@4.1.3:
    resolution: {integrity: sha512-/6w/C21Pm1A7aZitlI5Ni/2J6FFQN8i1Cvz3kHABAAbw93v/NlvKdVOqz7CCWz/3iv/JplRSEEZ83XION15ovw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ansi-colors/-/ansi-colors-4.1.3.tgz}
    name: ansi-colors
    version: 4.1.3
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/ansi-escapes@3.2.0:
    resolution: {integrity: sha512-cBhpre4ma+U0T1oM5fXg7Dy1Jw7zzwv7lt/GoCpr+hDQJoYnKVPLL4dCvSEFMmQurOQvSrwT7SL/DAlhBI97RQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-3.2.0.tgz}
    name: ansi-escapes
    version: 3.2.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/ansi-html-community@0.0.8:
    resolution: {integrity: sha512-1APHAyr3+PCamwNw3bXCPp4HFLONZt/yIH0sZp0/469KWNTEy+qN5jQ3GVX6DMZ1UXAi34yVwtTeaG/HpBuuzw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ansi-html-community/-/ansi-html-community-0.0.8.tgz}
    name: ansi-html-community
    version: 0.0.8
    engines: {'0': node >= 0.8.0}
    hasBin: true
    dev: true

  registry.npmjs.org/ansi-regex@3.0.1:
    resolution: {integrity: sha512-+O9Jct8wf++lXxxFc4hc8LsjaSq0HFzzL7cVsw8pRDIPdjKD2mT4ytDZlLuSBZ4cLKZFXIrMGO7DbQCtMJJMKw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ansi-regex/-/ansi-regex-3.0.1.tgz}
    name: ansi-regex
    version: 3.0.1
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/ansi-regex@5.0.1:
    resolution: {integrity: sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz}
    name: ansi-regex
    version: 5.0.1
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/ansi-styles@3.2.1:
    resolution: {integrity: sha512-VT0ZI6kZRdTh8YyJw3SMbYm/u+NqfsAxEpWO0Pf9sq8/e94WxxOpPKx9FR1FlyCtOVDNOQ+8ntlqFxiRc+r5qA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ansi-styles/-/ansi-styles-3.2.1.tgz}
    name: ansi-styles
    version: 3.2.1
    engines: {node: '>=4'}
    dependencies:
      color-convert: registry.npmjs.org/color-convert@1.9.3
    dev: true

  registry.npmjs.org/ansi-styles@4.3.0:
    resolution: {integrity: sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz}
    name: ansi-styles
    version: 4.3.0
    engines: {node: '>=8'}
    dependencies:
      color-convert: registry.npmjs.org/color-convert@2.0.1
    dev: true

  registry.npmjs.org/any-promise@1.3.0:
    resolution: {integrity: sha512-7UvmKalWRt1wgjL1RrGxoSJW/0QZFIegpeGvZG9kjp8vrRu55XTHbwnqq2GpXm9uLbcuhxm3IqX9OB4MZR1b2A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/any-promise/-/any-promise-1.3.0.tgz}
    name: any-promise
    version: 1.3.0
    dev: true

  registry.npmjs.org/anymatch@3.1.3:
    resolution: {integrity: sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz}
    name: anymatch
    version: 3.1.3
    engines: {node: '>= 8'}
    dependencies:
      normalize-path: registry.npmjs.org/normalize-path@3.0.0
      picomatch: registry.npmjs.org/picomatch@2.3.1
    dev: true

  registry.npmjs.org/arch@2.2.0:
    resolution: {integrity: sha512-Of/R0wqp83cgHozfIYLbBMnej79U/SVGOOyuB3VVFv1NRM/PSFMK12x9KVtiYzJqmnU5WR2qp0Z5rHb7sWGnFQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/arch/-/arch-2.2.0.tgz}
    name: arch
    version: 2.2.0
    dev: true

  registry.npmjs.org/argparse@1.0.10:
    resolution: {integrity: sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz}
    name: argparse
    version: 1.0.10
    dependencies:
      sprintf-js: registry.npmjs.org/sprintf-js@1.0.3
    dev: true

  registry.npmjs.org/array-flatten@1.1.1:
    resolution: {integrity: sha512-PCVAQswWemu6UdxsDFFX/+gVeYqKAod3D3UVm91jHwynguOwAvYPhx8nNlM++NqRcK6CxxpUafjmhIdKiHibqg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/array-flatten/-/array-flatten-1.1.1.tgz}
    name: array-flatten
    version: 1.1.1
    dev: true

  registry.npmjs.org/array-union@2.1.0:
    resolution: {integrity: sha512-HGyxoOTYUyCM6stUe6EJgnd4EoewAI7zMdfqO+kGjnlZmBDz/cR5pf8r/cR4Wq60sL/p0IkcjUEEPwS3GFrIyw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/array-union/-/array-union-2.1.0.tgz}
    name: array-union
    version: 2.1.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/astral-regex@2.0.0:
    resolution: {integrity: sha512-Z7tMw1ytTXt5jqMcOP+OQteU1VuNK9Y02uuJtKQ1Sv69jXQKKg5cibLwGJow8yzZP+eAc18EmLGPal0bp36rvQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/astral-regex/-/astral-regex-2.0.0.tgz}
    name: astral-regex
    version: 2.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/async-validator@1.8.5:
    resolution: {integrity: sha512-tXBM+1m056MAX0E8TL2iCjg8WvSyXu0Zc8LNtYqrVeyoL3+esHRZ4SieE9fKQyyU09uONjnMEjrNBMqT0mbvmA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/async-validator/-/async-validator-1.8.5.tgz}
    name: async-validator
    version: 1.8.5
    dependencies:
      babel-runtime: registry.npmjs.org/babel-runtime@6.26.0
    dev: false

  registry.npmjs.org/async@2.6.4:
    resolution: {integrity: sha512-mzo5dfJYwAn29PeiJ0zvwTo04zj8HDJj0Mn8TD7sno7q12prdbnasKJHhkm2c1LgrhlJ0teaea8860oxi51mGA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/async/-/async-2.6.4.tgz}
    name: async
    version: 2.6.4
    dependencies:
      lodash: registry.npmjs.org/lodash@4.17.21
    dev: true

  registry.npmjs.org/at-least-node@1.0.0:
    resolution: {integrity: sha512-+q/t7Ekv1EDY2l6Gda6LLiX14rU9TV20Wa3ofeQmwPFZbOMo9DXrLbOjFaaclkXKWidIaopwAObQDqwWtGUjqg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/at-least-node/-/at-least-node-1.0.0.tgz}
    name: at-least-node
    version: 1.0.0
    engines: {node: '>= 4.0.0'}
    dev: true

  registry.npmjs.org/autoprefixer@10.4.19(postcss@8.4.38):
    resolution: {integrity: sha512-BaENR2+zBZ8xXhM4pUaKUxlVdxZ0EZhjvbopwnXmxRUfqDmwSpC2lAi/QXvx7NRdPCo1WKEcEF6mV64si1z4Ew==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/autoprefixer/-/autoprefixer-10.4.19.tgz}
    id: registry.npmjs.org/autoprefixer/10.4.19
    name: autoprefixer
    version: 10.4.19
    engines: {node: ^10 || ^12 || >=14}
    hasBin: true
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      browserslist: registry.npmjs.org/browserslist@4.23.0
      caniuse-lite: registry.npmjs.org/caniuse-lite@1.0.30001611
      fraction.js: registry.npmjs.org/fraction.js@4.3.7
      normalize-range: registry.npmjs.org/normalize-range@0.1.2
      picocolors: registry.npmjs.org/picocolors@1.0.0
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/babel-helper-vue-jsx-merge-props@2.0.3:
    resolution: {integrity: sha512-gsLiKK7Qrb7zYJNgiXKpXblxbV5ffSwR0f5whkPAaBAR4fhi6bwRZxX9wBlIc5M/v8CCkXUbXZL4N/nSE97cqg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/babel-helper-vue-jsx-merge-props/-/babel-helper-vue-jsx-merge-props-2.0.3.tgz}
    name: babel-helper-vue-jsx-merge-props
    version: 2.0.3
    dev: false

  registry.npmjs.org/babel-loader@8.3.0(@babel/core@7.24.4)(webpack@5.91.0):
    resolution: {integrity: sha512-H8SvsMF+m9t15HNLMipppzkC+Y2Yq+v3SonZyU70RBL/h1gxPkH08Ot8pEE9Z4Kd+czyWJClmFS8qzIP9OZ04Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/babel-loader/-/babel-loader-8.3.0.tgz}
    id: registry.npmjs.org/babel-loader/8.3.0
    name: babel-loader
    version: 8.3.0
    engines: {node: '>= 8.9'}
    peerDependencies:
      '@babel/core': ^7.0.0
      webpack: '>=2'
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      find-cache-dir: registry.npmjs.org/find-cache-dir@3.3.2
      loader-utils: registry.npmjs.org/loader-utils@2.0.4
      make-dir: registry.npmjs.org/make-dir@3.1.0
      schema-utils: registry.npmjs.org/schema-utils@2.7.1
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/babel-plugin-dynamic-import-node@2.3.3:
    resolution: {integrity: sha512-jZVI+s9Zg3IqA/kdi0i6UDCybUI3aSBLnglhYbSSjKlV7yF1F/5LWv8MakQmvYpnbJDS6fcBL2KzHSxNCMtWSQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/babel-plugin-dynamic-import-node/-/babel-plugin-dynamic-import-node-2.3.3.tgz}
    name: babel-plugin-dynamic-import-node
    version: 2.3.3
    dependencies:
      object.assign: registry.npmjs.org/object.assign@4.1.5
    dev: true

  registry.npmjs.org/babel-plugin-polyfill-corejs2@0.4.10(@babel/core@7.24.4):
    resolution: {integrity: sha512-rpIuu//y5OX6jVU+a5BCn1R5RSZYWAl2Nar76iwaOdycqb6JPxediskWFMMl7stfwNJR4b7eiQvh5fB5TEQJTQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/babel-plugin-polyfill-corejs2/-/babel-plugin-polyfill-corejs2-0.4.10.tgz}
    id: registry.npmjs.org/babel-plugin-polyfill-corejs2/0.4.10
    name: babel-plugin-polyfill-corejs2
    version: 0.4.10
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/compat-data': registry.npmjs.org/@babel/compat-data@7.24.4
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-define-polyfill-provider': registry.npmjs.org/@babel/helper-define-polyfill-provider@0.6.1(@babel/core@7.24.4)
      semver: registry.npmjs.org/semver@6.3.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/babel-plugin-polyfill-corejs3@0.10.4(@babel/core@7.24.4):
    resolution: {integrity: sha512-25J6I8NGfa5YkCDogHRID3fVCadIR8/pGl1/spvCkzb6lVn6SR3ojpx9nOn9iEBcUsjY24AmdKm5khcfKdylcg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/babel-plugin-polyfill-corejs3/-/babel-plugin-polyfill-corejs3-0.10.4.tgz}
    id: registry.npmjs.org/babel-plugin-polyfill-corejs3/0.10.4
    name: babel-plugin-polyfill-corejs3
    version: 0.10.4
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-define-polyfill-provider': registry.npmjs.org/@babel/helper-define-polyfill-provider@0.6.1(@babel/core@7.24.4)
      core-js-compat: registry.npmjs.org/core-js-compat@3.37.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/babel-plugin-polyfill-regenerator@0.6.1(@babel/core@7.24.4):
    resolution: {integrity: sha512-JfTApdE++cgcTWjsiCQlLyFBMbTUft9ja17saCc93lgV33h4tuCVj7tlvu//qpLwaG+3yEz7/KhahGrUMkVq9g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/babel-plugin-polyfill-regenerator/-/babel-plugin-polyfill-regenerator-0.6.1.tgz}
    id: registry.npmjs.org/babel-plugin-polyfill-regenerator/0.6.1
    name: babel-plugin-polyfill-regenerator
    version: 0.6.1
    peerDependencies:
      '@babel/core': ^7.4.0 || ^8.0.0-0 <8.0.0
    dependencies:
      '@babel/core': registry.npmjs.org/@babel/core@7.24.4
      '@babel/helper-define-polyfill-provider': registry.npmjs.org/@babel/helper-define-polyfill-provider@0.6.1(@babel/core@7.24.4)
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/babel-runtime@6.26.0:
    resolution: {integrity: sha512-ITKNuq2wKlW1fJg9sSW52eepoYgZBggvOAHC0u/CYu/qxQ9EVzThCgR69BnSXLHjy2f7SY5zaQ4yt7H9ZVxY2g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/babel-runtime/-/babel-runtime-6.26.0.tgz}
    name: babel-runtime
    version: 6.26.0
    dependencies:
      core-js: registry.npmjs.org/core-js@2.6.12
      regenerator-runtime: registry.npmjs.org/regenerator-runtime@0.11.1
    dev: false

  registry.npmjs.org/balanced-match@1.0.2:
    resolution: {integrity: sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz}
    name: balanced-match
    version: 1.0.2
    dev: true

  registry.npmjs.org/base64-js@1.5.1:
    resolution: {integrity: sha512-AKpaYlHn8t4SVbOHCy+b5+KKgvR4vrsD8vbvrbiQJps7fKDTkjkDry6ji0rUJjC0kzbNePLwzxq8iypo41qeWA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/base64-js/-/base64-js-1.5.1.tgz}
    name: base64-js
    version: 1.5.1
    dev: true

  registry.npmjs.org/batch@0.6.1:
    resolution: {integrity: sha512-x+VAiMRL6UPkx+kudNvxTl6hB2XNNCG2r+7wixVfIYwu/2HKRXimwQyaumLjMveWvT2Hkd/cAJw+QBMfJ/EKVw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/batch/-/batch-0.6.1.tgz}
    name: batch
    version: 0.6.1
    dev: true

  registry.npmjs.org/big.js@5.2.2:
    resolution: {integrity: sha512-vyL2OymJxmarO8gxMr0mhChsO9QGwhynfuu4+MHTAW6czfq9humCB7rKpUjDd9YUiDPU4mzpyupFSvOClAwbmQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/big.js/-/big.js-5.2.2.tgz}
    name: big.js
    version: 5.2.2
    dev: true

  registry.npmjs.org/binary-extensions@2.3.0:
    resolution: {integrity: sha512-Ceh+7ox5qe7LJuLHoY0feh3pHuUDHAcRUeyL2VYghZwfpkNIy/+8Ocg0a3UuSoYzavmylwuLWQOf3hl0jjMMIw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/binary-extensions/-/binary-extensions-2.3.0.tgz}
    name: binary-extensions
    version: 2.3.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/bl@4.1.0:
    resolution: {integrity: sha512-1W07cM9gS6DcLperZfFSj+bWLtaPGSOHWhPiGzXmvVJbRLdG82sH/Kn8EtW1VqWVA54AKf2h5k5BbnIbwF3h6w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/bl/-/bl-4.1.0.tgz}
    name: bl
    version: 4.1.0
    dependencies:
      buffer: registry.npmjs.org/buffer@5.7.1
      inherits: registry.npmjs.org/inherits@2.0.4
      readable-stream: registry.npmjs.org/readable-stream@3.6.2
    dev: true

  registry.npmjs.org/bluebird@3.7.2:
    resolution: {integrity: sha512-XpNj6GDQzdfW+r2Wnn7xiSAd7TM3jzkxGXBGTtWKuSXv1xUV+azxAm8jdWZN06QTQk+2N2XB9jRDkvbmQmcRtg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/bluebird/-/bluebird-3.7.2.tgz}
    name: bluebird
    version: 3.7.2
    dev: true

  registry.npmjs.org/body-parser@1.20.2:
    resolution: {integrity: sha512-ml9pReCu3M61kGlqoTm2umSXTlRTuGTx0bfYj+uIUKKYycG5NtSbeetV3faSU6R7ajOPw0g/J1PvK4qNy7s5bA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/body-parser/-/body-parser-1.20.2.tgz}
    name: body-parser
    version: 1.20.2
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dependencies:
      bytes: registry.npmjs.org/bytes@3.1.2
      content-type: registry.npmjs.org/content-type@1.0.5
      debug: registry.npmjs.org/debug@2.6.9
      depd: registry.npmjs.org/depd@2.0.0
      destroy: registry.npmjs.org/destroy@1.2.0
      http-errors: registry.npmjs.org/http-errors@2.0.0
      iconv-lite: registry.npmjs.org/iconv-lite@0.4.24
      on-finished: registry.npmjs.org/on-finished@2.4.1
      qs: registry.npmjs.org/qs@6.11.0
      raw-body: registry.npmjs.org/raw-body@2.5.2
      type-is: registry.npmjs.org/type-is@1.6.18
      unpipe: registry.npmjs.org/unpipe@1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/bonjour-service@1.2.1:
    resolution: {integrity: sha512-oSzCS2zV14bh2kji6vNe7vrpJYCHGvcZnlffFQ1MEoX/WOeQ/teD8SYWKR942OI3INjq8OMNJlbPK5LLLUxFDw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/bonjour-service/-/bonjour-service-1.2.1.tgz}
    name: bonjour-service
    version: 1.2.1
    dependencies:
      fast-deep-equal: registry.npmjs.org/fast-deep-equal@3.1.3
      multicast-dns: registry.npmjs.org/multicast-dns@7.2.5
    dev: true

  registry.npmjs.org/boolbase@1.0.0:
    resolution: {integrity: sha512-JZOSA7Mo9sNGB8+UjSgzdLtokWAky1zbztM3WRLCbZ70/3cTANmQmOdR7y2g+J0e2WXywy1yS468tY+IruqEww==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/boolbase/-/boolbase-1.0.0.tgz}
    name: boolbase
    version: 1.0.0
    dev: true

  registry.npmjs.org/brace-expansion@1.1.11:
    resolution: {integrity: sha512-iCuPHDFgrHX7H2vEI/5xpz07zSHB00TpugqhmYtVmMO6518mCuRMoOYFldEBl0g187ufozdaHgWKcYFb61qGiA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.11.tgz}
    name: brace-expansion
    version: 1.1.11
    dependencies:
      balanced-match: registry.npmjs.org/balanced-match@1.0.2
      concat-map: registry.npmjs.org/concat-map@0.0.1
    dev: true

  registry.npmjs.org/braces@3.0.2:
    resolution: {integrity: sha512-b8um+L1RzM3WDSzvhm6gIz1yfTbBt6YTlcEKAvsmqCZZFw46z626lVj9j1yEPW33H5H+lBQpZMP1k8l+78Ha0A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/braces/-/braces-3.0.2.tgz}
    name: braces
    version: 3.0.2
    engines: {node: '>=8'}
    dependencies:
      fill-range: registry.npmjs.org/fill-range@7.0.1
    dev: true

  registry.npmjs.org/browserslist@4.23.0:
    resolution: {integrity: sha512-QW8HiM1shhT2GuzkvklfjcKDiWFXHOeFCIA/huJPwHsslwcydgk7X+z2zXpEijP98UCY7HbubZt5J2Zgvf0CaQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/browserslist/-/browserslist-4.23.0.tgz}
    name: browserslist
    version: 4.23.0
    engines: {node: ^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7}
    hasBin: true
    dependencies:
      caniuse-lite: registry.npmjs.org/caniuse-lite@1.0.30001611
      electron-to-chromium: registry.npmjs.org/electron-to-chromium@1.4.740
      node-releases: registry.npmjs.org/node-releases@2.0.14
      update-browserslist-db: registry.npmjs.org/update-browserslist-db@1.0.13(browserslist@4.23.0)
    dev: true

  registry.npmjs.org/buffer-from@1.1.2:
    resolution: {integrity: sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz}
    name: buffer-from
    version: 1.1.2
    dev: true

  registry.npmjs.org/buffer@5.7.1:
    resolution: {integrity: sha512-EHcyIPBQ4BSGlvjB16k5KgAJ27CIsHY/2JBmCRReo48y9rQ3MaUzWX3KVlBa4U7MyX02HdVj0K7C3WaB3ju7FQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/buffer/-/buffer-5.7.1.tgz}
    name: buffer
    version: 5.7.1
    dependencies:
      base64-js: registry.npmjs.org/base64-js@1.5.1
      ieee754: registry.npmjs.org/ieee754@1.2.1
    dev: true

  registry.npmjs.org/bytes@3.0.0:
    resolution: {integrity: sha512-pMhOfFDPiv9t5jjIXkHosWmkSyQbvsgEVNkz0ERHbuLh2T/7j4Mqqpz523Fe8MVY89KC6Sh/QfS2sM+SjgFDcw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/bytes/-/bytes-3.0.0.tgz}
    name: bytes
    version: 3.0.0
    engines: {node: '>= 0.8'}
    dev: true

  registry.npmjs.org/bytes@3.1.2:
    resolution: {integrity: sha512-/Nf7TyzTx6S3yRJObOAV7956r8cr2+Oj8AC5dt8wSP3BQAoeX58NoHyCU8P8zGkNXStjTSi6fzO6F0pBdcYbEg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/bytes/-/bytes-3.1.2.tgz}
    name: bytes
    version: 3.1.2
    engines: {node: '>= 0.8'}
    dev: true

  registry.npmjs.org/call-bind@1.0.7:
    resolution: {integrity: sha512-GHTSNSYICQ7scH7sZ+M2rFopRoLh8t2bLSW6BbgrtLsahOIB5iyAVJf9GjWK3cYTDaMj4XdBpM1cA6pIS0Kv2w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/call-bind/-/call-bind-1.0.7.tgz}
    name: call-bind
    version: 1.0.7
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: registry.npmjs.org/es-define-property@1.0.0
      es-errors: registry.npmjs.org/es-errors@1.3.0
      function-bind: registry.npmjs.org/function-bind@1.1.2
      get-intrinsic: registry.npmjs.org/get-intrinsic@1.2.4
      set-function-length: registry.npmjs.org/set-function-length@1.2.2
    dev: true

  registry.npmjs.org/callsites@3.1.0:
    resolution: {integrity: sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz}
    name: callsites
    version: 3.1.0
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/camel-case@4.1.2:
    resolution: {integrity: sha512-gxGWBrTT1JuMx6R+o5PTXMmUnhnVzLQ9SNutD4YqKtI6ap897t3tKECYla6gCWEkplXnlNybEkZg9GEGxKFCgw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/camel-case/-/camel-case-4.1.2.tgz}
    name: camel-case
    version: 4.1.2
    dependencies:
      pascal-case: registry.npmjs.org/pascal-case@3.1.2
      tslib: registry.npmjs.org/tslib@2.6.2
    dev: true

  registry.npmjs.org/camelcase@5.3.1:
    resolution: {integrity: sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz}
    name: camelcase
    version: 5.3.1
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/camelcase@6.3.0:
    resolution: {integrity: sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz}
    name: camelcase
    version: 6.3.0
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/caniuse-api@3.0.0:
    resolution: {integrity: sha512-bsTwuIg/BZZK/vreVTYYbSWoe2F+71P7K5QGEX+pT250DZbfU1MQ5prOKpPR+LL6uWKK3KMwMCAS74QB3Um1uw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/caniuse-api/-/caniuse-api-3.0.0.tgz}
    name: caniuse-api
    version: 3.0.0
    dependencies:
      browserslist: registry.npmjs.org/browserslist@4.23.0
      caniuse-lite: registry.npmjs.org/caniuse-lite@1.0.30001611
      lodash.memoize: registry.npmjs.org/lodash.memoize@4.1.2
      lodash.uniq: registry.npmjs.org/lodash.uniq@4.5.0
    dev: true

  registry.npmjs.org/caniuse-lite@1.0.30001611:
    resolution: {integrity: sha512-19NuN1/3PjA3QI8Eki55N8my4LzfkMCRLgCVfrl/slbSAchQfV0+GwjPrK3rq37As4UCLlM/DHajbKkAqbv92Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001611.tgz}
    name: caniuse-lite
    version: 1.0.30001611
    dev: true

  registry.npmjs.org/case-sensitive-paths-webpack-plugin@2.4.0:
    resolution: {integrity: sha512-roIFONhcxog0JSSWbvVAh3OocukmSgpqOH6YpMkCvav/ySIV3JKg4Dc8vYtQjYi/UxpNE36r/9v+VqTQqgkYmw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/case-sensitive-paths-webpack-plugin/-/case-sensitive-paths-webpack-plugin-2.4.0.tgz}
    name: case-sensitive-paths-webpack-plugin
    version: 2.4.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/chalk@2.4.2:
    resolution: {integrity: sha512-Mti+f9lpJNcwF4tWV8/OrTTtF1gZi+f8FqlyAdouralcFWFQWF2+NgCHShjkCb+IFBLq9buZwE1xckQU4peSuQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/chalk/-/chalk-2.4.2.tgz}
    name: chalk
    version: 2.4.2
    engines: {node: '>=4'}
    dependencies:
      ansi-styles: registry.npmjs.org/ansi-styles@3.2.1
      escape-string-regexp: registry.npmjs.org/escape-string-regexp@1.0.5
      supports-color: registry.npmjs.org/supports-color@5.5.0
    dev: true

  registry.npmjs.org/chalk@3.0.0:
    resolution: {integrity: sha512-4D3B6Wf41KOYRFdszmDqMCGq5VV/uMAB273JILmO+3jAlh8X4qDtdtgCR3fxtbLEMzSx22QdhnDcJvu2u1fVwg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/chalk/-/chalk-3.0.0.tgz}
    name: chalk
    version: 3.0.0
    engines: {node: '>=8'}
    dependencies:
      ansi-styles: registry.npmjs.org/ansi-styles@4.3.0
      supports-color: registry.npmjs.org/supports-color@7.2.0
    dev: true

  registry.npmjs.org/chalk@4.1.2:
    resolution: {integrity: sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz}
    name: chalk
    version: 4.1.2
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: registry.npmjs.org/ansi-styles@4.3.0
      supports-color: registry.npmjs.org/supports-color@7.2.0
    dev: true

  registry.npmjs.org/chokidar@3.6.0:
    resolution: {integrity: sha512-7VT13fmjotKpGipCW9JEQAusEPE+Ei8nl6/g4FBAmIm0GOOLMua9NDDo/DWp0ZAxCr3cPq5ZpBqmPAQgDda2Pw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/chokidar/-/chokidar-3.6.0.tgz}
    name: chokidar
    version: 3.6.0
    engines: {node: '>= 8.10.0'}
    dependencies:
      anymatch: registry.npmjs.org/anymatch@3.1.3
      braces: registry.npmjs.org/braces@3.0.2
      glob-parent: registry.npmjs.org/glob-parent@5.1.2
      is-binary-path: registry.npmjs.org/is-binary-path@2.1.0
      is-glob: registry.npmjs.org/is-glob@4.0.3
      normalize-path: registry.npmjs.org/normalize-path@3.0.0
      readdirp: registry.npmjs.org/readdirp@3.6.0
    optionalDependencies:
      fsevents: registry.npmjs.org/fsevents@2.3.3
    dev: true

  registry.npmjs.org/chrome-trace-event@1.0.3:
    resolution: {integrity: sha512-p3KULyQg4S7NIHixdwbGX+nFHkoBiA4YQmyWtjb8XngSKV124nJmRysgAeujbUVb15vh+RvFUfCPqU7rXk+hZg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/chrome-trace-event/-/chrome-trace-event-1.0.3.tgz}
    name: chrome-trace-event
    version: 1.0.3
    engines: {node: '>=6.0'}
    dev: true

  registry.npmjs.org/ci-info@1.6.0:
    resolution: {integrity: sha512-vsGdkwSCDpWmP80ncATX7iea5DWQemg1UgCW5J8tqjU3lYw4FBYuj89J0CTVomA7BEfvSZd84GmHko+MxFQU2A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ci-info/-/ci-info-1.6.0.tgz}
    name: ci-info
    version: 1.6.0
    dev: true

  registry.npmjs.org/clean-css@5.3.3:
    resolution: {integrity: sha512-D5J+kHaVb/wKSFcyyV75uCn8fiY4sV38XJoe4CUyGQ+mOU/fMVYUdH1hJC+CJQ5uY3EnW27SbJYS4X8BiLrAFg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/clean-css/-/clean-css-5.3.3.tgz}
    name: clean-css
    version: 5.3.3
    engines: {node: '>= 10.0'}
    dependencies:
      source-map: registry.npmjs.org/source-map@0.6.1
    dev: true

  registry.npmjs.org/cli-cursor@2.1.0:
    resolution: {integrity: sha512-8lgKz8LmCRYZZQDpRyT2m5rKJ08TnU4tR9FFFW2rxpxR1FzWi4PQ/NfyODchAatHaUgnSPVcx/R5w6NuTBzFiw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cli-cursor/-/cli-cursor-2.1.0.tgz}
    name: cli-cursor
    version: 2.1.0
    engines: {node: '>=4'}
    dependencies:
      restore-cursor: registry.npmjs.org/restore-cursor@2.0.0
    dev: true

  registry.npmjs.org/cli-cursor@3.1.0:
    resolution: {integrity: sha512-I/zHAwsKf9FqGoXM4WWRACob9+SNukZTd94DWF57E4toouRulbCxcUh6RKUEOQlYTHJnzkPMySvPNaaSLNfLZw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cli-cursor/-/cli-cursor-3.1.0.tgz}
    name: cli-cursor
    version: 3.1.0
    engines: {node: '>=8'}
    dependencies:
      restore-cursor: registry.npmjs.org/restore-cursor@3.1.0
    dev: true

  registry.npmjs.org/cli-highlight@2.1.11:
    resolution: {integrity: sha512-9KDcoEVwyUXrjcJNvHD0NFc/hiwe/WPVYIleQh2O1N2Zro5gWJZ/K+3DGn8w8P/F6FxOgzyC5bxDyHIgCSPhGg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cli-highlight/-/cli-highlight-2.1.11.tgz}
    name: cli-highlight
    version: 2.1.11
    engines: {node: '>=8.0.0', npm: '>=5.0.0'}
    hasBin: true
    dependencies:
      chalk: registry.npmjs.org/chalk@4.1.2
      highlight.js: registry.npmjs.org/highlight.js@10.7.3
      mz: registry.npmjs.org/mz@2.7.0
      parse5: registry.npmjs.org/parse5@5.1.1
      parse5-htmlparser2-tree-adapter: registry.npmjs.org/parse5-htmlparser2-tree-adapter@6.0.1
      yargs: registry.npmjs.org/yargs@16.2.0
    dev: true

  registry.npmjs.org/cli-spinners@2.9.2:
    resolution: {integrity: sha512-ywqV+5MmyL4E7ybXgKys4DugZbX0FC6LnwrhjuykIjnK9k8OQacQ7axGKnjDXWNhns0xot3bZI5h55H8yo9cJg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cli-spinners/-/cli-spinners-2.9.2.tgz}
    name: cli-spinners
    version: 2.9.2
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/clipboardy@2.3.0:
    resolution: {integrity: sha512-mKhiIL2DrQIsuXMgBgnfEHOZOryC7kY7YO//TN6c63wlEm3NG5tz+YgY5rVi29KCmq/QQjKYvM7a19+MDOTHOQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/clipboardy/-/clipboardy-2.3.0.tgz}
    name: clipboardy
    version: 2.3.0
    engines: {node: '>=8'}
    dependencies:
      arch: registry.npmjs.org/arch@2.2.0
      execa: registry.npmjs.org/execa@1.0.0
      is-wsl: registry.npmjs.org/is-wsl@2.2.0
    dev: true

  registry.npmjs.org/cliui@7.0.4:
    resolution: {integrity: sha512-OcRE68cOsVMXp1Yvonl/fzkQOyjLSu/8bhPDfQt0e0/Eb283TKP20Fs2MqoPsr9SwA595rRCA+QMzYc9nBP+JQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cliui/-/cliui-7.0.4.tgz}
    name: cliui
    version: 7.0.4
    dependencies:
      string-width: registry.npmjs.org/string-width@4.2.3
      strip-ansi: registry.npmjs.org/strip-ansi@6.0.1
      wrap-ansi: registry.npmjs.org/wrap-ansi@7.0.0
    dev: true

  registry.npmjs.org/clone-deep@4.0.1:
    resolution: {integrity: sha512-neHB9xuzh/wk0dIHweyAXv2aPGZIVk3pLMe+/RNzINf17fe0OG96QroktYAUm7SM1PBnzTabaLboqqxDyMU+SQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/clone-deep/-/clone-deep-4.0.1.tgz}
    name: clone-deep
    version: 4.0.1
    engines: {node: '>=6'}
    dependencies:
      is-plain-object: registry.npmjs.org/is-plain-object@2.0.4
      kind-of: registry.npmjs.org/kind-of@6.0.3
      shallow-clone: registry.npmjs.org/shallow-clone@3.0.1
    dev: true

  registry.npmjs.org/clone@1.0.4:
    resolution: {integrity: sha512-JQHZ2QMW6l3aH/j6xCqQThY/9OH4D/9ls34cgkUBiEeocRTU04tHfKPBsUK1PqZCUQM7GiA0IIXJSuXHI64Kbg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/clone/-/clone-1.0.4.tgz}
    name: clone
    version: 1.0.4
    engines: {node: '>=0.8'}
    dev: true

  registry.npmjs.org/color-convert@1.9.3:
    resolution: {integrity: sha512-QfAUtd+vFdAtFQcC8CCyYt1fYWxSqAiK2cSD6zDB8N3cpsEBAvRxp9zOGg6G/SHHJYAT88/az/IuDGALsNVbGg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/color-convert/-/color-convert-1.9.3.tgz}
    name: color-convert
    version: 1.9.3
    dependencies:
      color-name: registry.npmjs.org/color-name@1.1.3
    dev: true

  registry.npmjs.org/color-convert@2.0.1:
    resolution: {integrity: sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz}
    name: color-convert
    version: 2.0.1
    engines: {node: '>=7.0.0'}
    dependencies:
      color-name: registry.npmjs.org/color-name@1.1.4
    dev: true

  registry.npmjs.org/color-name@1.1.3:
    resolution: {integrity: sha512-72fSenhMw2HZMTVHeCA9KCmpEIbzWiQsjN+BHcBbS9vr1mtt+vJjPdksIBNUmKAW8TFUDPJK5SUU3QhE9NEXDw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/color-name/-/color-name-1.1.3.tgz}
    name: color-name
    version: 1.1.3
    dev: true

  registry.npmjs.org/color-name@1.1.4:
    resolution: {integrity: sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz}
    name: color-name
    version: 1.1.4
    dev: true

  registry.npmjs.org/colord@2.9.3:
    resolution: {integrity: sha512-jeC1axXpnb0/2nn/Y1LPuLdgXBLH7aDcHu4KEKfqw3CUhX7ZpfBSlPKyqXE6btIgEzfWtrX3/tyBCaCvXvMkOw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/colord/-/colord-2.9.3.tgz}
    name: colord
    version: 2.9.3
    dev: true

  registry.npmjs.org/colorette@2.0.20:
    resolution: {integrity: sha512-IfEDxwoWIjkeXL1eXcDiow4UbKjhLdq6/EuSVR9GMN7KVH3r9gQ83e73hsz1Nd1T3ijd5xv1wcWRYO+D6kCI2w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/colorette/-/colorette-2.0.20.tgz}
    name: colorette
    version: 2.0.20
    dev: true

  registry.npmjs.org/commander@2.20.3:
    resolution: {integrity: sha512-GpVkmM8vF2vQUkj2LvZmD35JxeJOLCwJ9cUkugyk2nuhbv3+mJvpLYYt+0+USMxE+oj+ey/lJEnhZw75x/OMcQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/commander/-/commander-2.20.3.tgz}
    name: commander
    version: 2.20.3
    dev: true

  registry.npmjs.org/commander@7.2.0:
    resolution: {integrity: sha512-QrWXB+ZQSVPmIWIhtEO9H+gwHaMGYiF5ChvoJ+K9ZGHG/sVsa6yiesAD1GC/x46sET00Xlwo1u49RVVVzvcSkw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/commander/-/commander-7.2.0.tgz}
    name: commander
    version: 7.2.0
    engines: {node: '>= 10'}
    dev: true

  registry.npmjs.org/commander@8.3.0:
    resolution: {integrity: sha512-OkTL9umf+He2DZkUq8f8J9of7yL6RJKI24dVITBmNfZBmri9zYZQrKkuXiKhyfPSu8tUhnVBB1iKXevvnlR4Ww==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/commander/-/commander-8.3.0.tgz}
    name: commander
    version: 8.3.0
    engines: {node: '>= 12'}
    dev: true

  registry.npmjs.org/commondir@1.0.1:
    resolution: {integrity: sha512-W9pAhw0ja1Edb5GVdIF1mjZw/ASI0AlShXM83UUGe2DVr5TdAPEA1OA8m/g8zWp9x6On7gqufY+FatDbC3MDQg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/commondir/-/commondir-1.0.1.tgz}
    name: commondir
    version: 1.0.1
    dev: true

  registry.npmjs.org/compressible@2.0.18:
    resolution: {integrity: sha512-AF3r7P5dWxL8MxyITRMlORQNaOA2IkAFaTr4k7BUumjPtRpGDTZpl0Pb1XCO6JeDCBdp126Cgs9sMxqSjgYyRg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/compressible/-/compressible-2.0.18.tgz}
    name: compressible
    version: 2.0.18
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: registry.npmjs.org/mime-db@1.52.0
    dev: true

  registry.npmjs.org/compression@1.7.4:
    resolution: {integrity: sha512-jaSIDzP9pZVS4ZfQ+TzvtiWhdpFhE2RDHz8QJkpX9SIpLq88VueF5jJw6t+6CUQcAoA6t+x89MLrWAqpfDE8iQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/compression/-/compression-1.7.4.tgz}
    name: compression
    version: 1.7.4
    engines: {node: '>= 0.8.0'}
    dependencies:
      accepts: registry.npmjs.org/accepts@1.3.8
      bytes: registry.npmjs.org/bytes@3.0.0
      compressible: registry.npmjs.org/compressible@2.0.18
      debug: registry.npmjs.org/debug@2.6.9
      on-headers: registry.npmjs.org/on-headers@1.0.2
      safe-buffer: registry.npmjs.org/safe-buffer@5.1.2
      vary: registry.npmjs.org/vary@1.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/concat-map@0.0.1:
    resolution: {integrity: sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz}
    name: concat-map
    version: 0.0.1
    dev: true

  registry.npmjs.org/connect-history-api-fallback@2.0.0:
    resolution: {integrity: sha512-U73+6lQFmfiNPrYbXqr6kZ1i1wiRqXnp2nhMsINseWXO8lDau0LGEffJ8kQi4EjLZympVgRdvqjAgiZ1tgzDDA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/connect-history-api-fallback/-/connect-history-api-fallback-2.0.0.tgz}
    name: connect-history-api-fallback
    version: 2.0.0
    engines: {node: '>=0.8'}
    dev: true

  registry.npmjs.org/consolidate@0.15.1:
    resolution: {integrity: sha512-DW46nrsMJgy9kqAbPt5rKaCr7uFtpo4mSUvLHIUbJEjm0vo+aY5QLwBUq3FK4tRnJr/X0Psc0C4jf/h+HtXSMw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/consolidate/-/consolidate-0.15.1.tgz}
    name: consolidate
    version: 0.15.1
    engines: {node: '>= 0.10.0'}
    deprecated: Please upgrade to consolidate v1.0.0+ as it has been modernized with several long-awaited fixes implemented. Maintenance is supported by Forward Email at https://forwardemail.net ; follow/watch https://github.com/ladjs/consolidate for updates and release changelog
    peerDependencies:
      arc-templates: ^0.5.3
      atpl: '>=0.7.6'
      babel-core: ^6.26.3
      bracket-template: ^1.1.5
      coffee-script: ^1.12.7
      dot: ^1.1.3
      dust: ^0.3.0
      dustjs-helpers: ^1.7.4
      dustjs-linkedin: ^2.7.5
      eco: ^1.1.0-rc-3
      ect: ^0.5.9
      ejs: ^3.1.5
      haml-coffee: ^1.14.1
      hamlet: ^0.3.3
      hamljs: ^0.6.2
      handlebars: ^4.7.6
      hogan.js: ^3.0.2
      htmling: ^0.0.8
      jade: ^1.11.0
      jazz: ^0.0.18
      jqtpl: ~1.1.0
      just: ^0.1.8
      liquid-node: ^3.0.1
      liquor: ^0.0.5
      lodash: ^4.17.20
      marko: ^3.14.4
      mote: ^0.2.0
      mustache: ^3.0.0
      nunjucks: ^3.2.2
      plates: ~0.4.11
      pug: ^3.0.0
      qejs: ^3.0.5
      ractive: ^1.3.12
      razor-tmpl: ^1.3.1
      react: ^16.13.1
      react-dom: ^16.13.1
      slm: ^2.0.0
      squirrelly: ^5.1.0
      swig: ^1.4.2
      swig-templates: ^2.0.3
      teacup: ^2.0.0
      templayed: '>=0.2.3'
      then-jade: '*'
      then-pug: '*'
      tinyliquid: ^0.2.34
      toffee: ^0.3.6
      twig: ^1.15.2
      twing: ^5.0.2
      underscore: ^1.11.0
      vash: ^0.13.0
      velocityjs: ^2.0.1
      walrus: ^0.10.1
      whiskers: ^0.4.0
    peerDependenciesMeta:
      arc-templates:
        optional: true
      atpl:
        optional: true
      babel-core:
        optional: true
      bracket-template:
        optional: true
      coffee-script:
        optional: true
      dot:
        optional: true
      dust:
        optional: true
      dustjs-helpers:
        optional: true
      dustjs-linkedin:
        optional: true
      eco:
        optional: true
      ect:
        optional: true
      ejs:
        optional: true
      haml-coffee:
        optional: true
      hamlet:
        optional: true
      hamljs:
        optional: true
      handlebars:
        optional: true
      hogan.js:
        optional: true
      htmling:
        optional: true
      jade:
        optional: true
      jazz:
        optional: true
      jqtpl:
        optional: true
      just:
        optional: true
      liquid-node:
        optional: true
      liquor:
        optional: true
      lodash:
        optional: true
      marko:
        optional: true
      mote:
        optional: true
      mustache:
        optional: true
      nunjucks:
        optional: true
      plates:
        optional: true
      pug:
        optional: true
      qejs:
        optional: true
      ractive:
        optional: true
      razor-tmpl:
        optional: true
      react:
        optional: true
      react-dom:
        optional: true
      slm:
        optional: true
      squirrelly:
        optional: true
      swig:
        optional: true
      swig-templates:
        optional: true
      teacup:
        optional: true
      templayed:
        optional: true
      then-jade:
        optional: true
      then-pug:
        optional: true
      tinyliquid:
        optional: true
      toffee:
        optional: true
      twig:
        optional: true
      twing:
        optional: true
      underscore:
        optional: true
      vash:
        optional: true
      velocityjs:
        optional: true
      walrus:
        optional: true
      whiskers:
        optional: true
    dependencies:
      bluebird: registry.npmjs.org/bluebird@3.7.2
    dev: true

  registry.npmjs.org/content-disposition@0.5.4:
    resolution: {integrity: sha512-FveZTNuGw04cxlAiWbzi6zTAL/lhehaWbTtgluJh4/E95DqMwTmha3KZN1aAWA8cFIhHzMZUvLevkw5Rqk+tSQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/content-disposition/-/content-disposition-0.5.4.tgz}
    name: content-disposition
    version: 0.5.4
    engines: {node: '>= 0.6'}
    dependencies:
      safe-buffer: registry.npmjs.org/safe-buffer@5.2.1
    dev: true

  registry.npmjs.org/content-type@1.0.5:
    resolution: {integrity: sha512-nTjqfcBFEipKdXCv4YDQWCfmcLZKm81ldF0pAopTvyrFGVbcR6P/VAAd5G7N+0tTr8QqiU0tFadD6FK4NtJwOA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/content-type/-/content-type-1.0.5.tgz}
    name: content-type
    version: 1.0.5
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/convert-source-map@2.0.0:
    resolution: {integrity: sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz}
    name: convert-source-map
    version: 2.0.0
    dev: true

  registry.npmjs.org/cookie-signature@1.0.6:
    resolution: {integrity: sha512-QADzlaHc8icV8I7vbaJXJwod9HWYp8uCqf1xa4OfNu1T7JVxQIrUgOWtHdNDtPiywmFbiS12VjotIXLrKM3orQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cookie-signature/-/cookie-signature-1.0.6.tgz}
    name: cookie-signature
    version: 1.0.6
    dev: true

  registry.npmjs.org/cookie@0.6.0:
    resolution: {integrity: sha512-U71cyTamuh1CRNCfpGY6to28lxvNwPG4Guz/EVjgf3Jmzv0vlDp1atT9eS5dDjMYHucpHbWns6Lwf3BKz6svdw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cookie/-/cookie-0.6.0.tgz}
    name: cookie
    version: 0.6.0
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/copy-webpack-plugin@9.1.0(webpack@5.91.0):
    resolution: {integrity: sha512-rxnR7PaGigJzhqETHGmAcxKnLZSR5u1Y3/bcIv/1FnqXedcL/E2ewK7ZCNrArJKCiSv8yVXhTqetJh8inDvfsA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/copy-webpack-plugin/-/copy-webpack-plugin-9.1.0.tgz}
    id: registry.npmjs.org/copy-webpack-plugin/9.1.0
    name: copy-webpack-plugin
    version: 9.1.0
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.1.0
    dependencies:
      fast-glob: registry.npmjs.org/fast-glob@3.3.2
      glob-parent: registry.npmjs.org/glob-parent@6.0.2
      globby: registry.npmjs.org/globby@11.1.0
      normalize-path: registry.npmjs.org/normalize-path@3.0.0
      schema-utils: registry.npmjs.org/schema-utils@3.3.0
      serialize-javascript: registry.npmjs.org/serialize-javascript@6.0.2
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/core-js-compat@3.37.0:
    resolution: {integrity: sha512-vYq4L+T8aS5UuFg4UwDhc7YNRWVeVZwltad9C/jV3R2LgVOpS9BDr7l/WL6BN0dbV3k1XejPTHqqEzJgsa0frA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/core-js-compat/-/core-js-compat-3.37.0.tgz}
    name: core-js-compat
    version: 3.37.0
    dependencies:
      browserslist: registry.npmjs.org/browserslist@4.23.0
    dev: true

  registry.npmjs.org/core-js@2.6.12:
    resolution: {integrity: sha512-Kb2wC0fvsWfQrgk8HU5lW6U/Lcs8+9aaYcy4ZFc6DDlo4nZ7n70dEgE5rtR0oG6ufKDUnrwfWL1mXR5ljDatrQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/core-js/-/core-js-2.6.12.tgz}
    name: core-js
    version: 2.6.12
    deprecated: core-js@<3.23.3 is no longer maintained and not recommended for usage due to the number of issues. Because of the V8 engine whims, feature detection in old core-js versions could cause a slowdown up to 100x even if nothing is polyfilled. Some versions have web compatibility issues. Please, upgrade your dependencies to the actual version of core-js.
    requiresBuild: true
    dev: false

  registry.npmjs.org/core-js@3.37.0:
    resolution: {integrity: sha512-fu5vHevQ8ZG4og+LXug8ulUtVxjOcEYvifJr7L5Bfq9GOztVqsKd9/59hUk2ZSbCrS3BqUr3EpaYGIYzq7g3Ug==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/core-js/-/core-js-3.37.0.tgz}
    name: core-js
    version: 3.37.0
    requiresBuild: true

  registry.npmjs.org/core-util-is@1.0.3:
    resolution: {integrity: sha512-ZQBvi1DcpJ4GDqanjucZ2Hj3wEO5pZDS89BWbkcrvdxksJorwUDDZamX9ldFkp9aw2lmBDLgkObEA4DWNJ9FYQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/core-util-is/-/core-util-is-1.0.3.tgz}
    name: core-util-is
    version: 1.0.3
    dev: true

  registry.npmjs.org/cosmiconfig@7.1.0:
    resolution: {integrity: sha512-AdmX6xUzdNASswsFtmwSt7Vj8po9IuqXm0UXz7QKPuEUmPB4XyjGfaAr2PSuELMwkRMVH1EpIkX5bTZGRB3eCA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cosmiconfig/-/cosmiconfig-7.1.0.tgz}
    name: cosmiconfig
    version: 7.1.0
    engines: {node: '>=10'}
    dependencies:
      '@types/parse-json': registry.npmjs.org/@types/parse-json@4.0.2
      import-fresh: registry.npmjs.org/import-fresh@3.3.0
      parse-json: registry.npmjs.org/parse-json@5.2.0
      path-type: registry.npmjs.org/path-type@4.0.0
      yaml: registry.npmjs.org/yaml@1.10.2
    dev: true

  registry.npmjs.org/cross-spawn@5.1.0:
    resolution: {integrity: sha512-pTgQJ5KC0d2hcY8eyL1IzlBPYjTkyH72XRZPnLyKus2mBfNjQs3klqbJU2VILqZryAZUt9JOb3h/mWMy23/f5A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cross-spawn/-/cross-spawn-5.1.0.tgz}
    name: cross-spawn
    version: 5.1.0
    dependencies:
      lru-cache: registry.npmjs.org/lru-cache@4.1.5
      shebang-command: registry.npmjs.org/shebang-command@1.2.0
      which: registry.npmjs.org/which@1.3.1
    dev: true

  registry.npmjs.org/cross-spawn@6.0.5:
    resolution: {integrity: sha512-eTVLrBSt7fjbDygz805pMnstIs2VTBNkRm0qxZd+M7A5XDdxVRWO5MxGBXZhjY4cqLYLdtrGqRf8mBPmzwSpWQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cross-spawn/-/cross-spawn-6.0.5.tgz}
    name: cross-spawn
    version: 6.0.5
    engines: {node: '>=4.8'}
    dependencies:
      nice-try: registry.npmjs.org/nice-try@1.0.5
      path-key: registry.npmjs.org/path-key@2.0.1
      semver: registry.npmjs.org/semver@5.7.2
      shebang-command: registry.npmjs.org/shebang-command@1.2.0
      which: registry.npmjs.org/which@1.3.1
    dev: true

  registry.npmjs.org/cross-spawn@7.0.3:
    resolution: {integrity: sha512-iRDPJKUPVEND7dHPO8rkbOnPpyDygcDFtWjpeWNCgy8WP2rXcxXL8TskReQl6OrB2G7+UJrags1q15Fudc7G6w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.3.tgz}
    name: cross-spawn
    version: 7.0.3
    engines: {node: '>= 8'}
    dependencies:
      path-key: registry.npmjs.org/path-key@3.1.1
      shebang-command: registry.npmjs.org/shebang-command@2.0.0
      which: registry.npmjs.org/which@2.0.2
    dev: true

  registry.npmjs.org/css-declaration-sorter@6.4.1(postcss@8.4.38):
    resolution: {integrity: sha512-rtdthzxKuyq6IzqX6jEcIzQF/YqccluefyCYheovBOLhFT/drQA9zj/UbRAa9J7C0o6EG6u3E6g+vKkay7/k3g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/css-declaration-sorter/-/css-declaration-sorter-6.4.1.tgz}
    id: registry.npmjs.org/css-declaration-sorter/6.4.1
    name: css-declaration-sorter
    version: 6.4.1
    engines: {node: ^10 || ^12 || >=14}
    peerDependencies:
      postcss: ^8.0.9
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
    dev: true

  registry.npmjs.org/css-loader@6.11.0(webpack@5.91.0):
    resolution: {integrity: sha512-CTJ+AEQJjq5NzLga5pE39qdiSV56F8ywCIsqNIRF0r7BDgWsN25aazToqAFg7ZrtA/U016xudB3ffgweORxX7g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/css-loader/-/css-loader-6.11.0.tgz}
    id: registry.npmjs.org/css-loader/6.11.0
    name: css-loader
    version: 6.11.0
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true
    dependencies:
      icss-utils: registry.npmjs.org/icss-utils@5.1.0(postcss@8.4.38)
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-modules-extract-imports: registry.npmjs.org/postcss-modules-extract-imports@3.1.0(postcss@8.4.38)
      postcss-modules-local-by-default: registry.npmjs.org/postcss-modules-local-by-default@4.0.5(postcss@8.4.38)
      postcss-modules-scope: registry.npmjs.org/postcss-modules-scope@3.2.0(postcss@8.4.38)
      postcss-modules-values: registry.npmjs.org/postcss-modules-values@4.0.0(postcss@8.4.38)
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
      semver: registry.npmjs.org/semver@7.6.0
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/css-minimizer-webpack-plugin@3.4.1(webpack@5.91.0):
    resolution: {integrity: sha512-1u6D71zeIfgngN2XNRJefc/hY7Ybsxd74Jm4qngIXyUEk7fss3VUzuHxLAq/R8NAba4QU9OUSaMZlbpRc7bM4Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/css-minimizer-webpack-plugin/-/css-minimizer-webpack-plugin-3.4.1.tgz}
    id: registry.npmjs.org/css-minimizer-webpack-plugin/3.4.1
    name: css-minimizer-webpack-plugin
    version: 3.4.1
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      '@parcel/css': '*'
      clean-css: '*'
      csso: '*'
      esbuild: '*'
      webpack: ^5.0.0
    peerDependenciesMeta:
      '@parcel/css':
        optional: true
      clean-css:
        optional: true
      csso:
        optional: true
      esbuild:
        optional: true
    dependencies:
      cssnano: registry.npmjs.org/cssnano@5.1.15(postcss@8.4.38)
      jest-worker: registry.npmjs.org/jest-worker@27.5.1
      postcss: registry.npmjs.org/postcss@8.4.38
      schema-utils: registry.npmjs.org/schema-utils@4.2.0
      serialize-javascript: registry.npmjs.org/serialize-javascript@6.0.2
      source-map: registry.npmjs.org/source-map@0.6.1
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/css-select@4.3.0:
    resolution: {integrity: sha512-wPpOYtnsVontu2mODhA19JrqWxNsfdatRKd64kmpRbQgh1KtItko5sTnEpPdpSaJszTOhEMlF/RPz28qj4HqhQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/css-select/-/css-select-4.3.0.tgz}
    name: css-select
    version: 4.3.0
    dependencies:
      boolbase: registry.npmjs.org/boolbase@1.0.0
      css-what: registry.npmjs.org/css-what@6.1.0
      domhandler: registry.npmjs.org/domhandler@4.3.1
      domutils: registry.npmjs.org/domutils@2.8.0
      nth-check: registry.npmjs.org/nth-check@2.1.1
    dev: true

  registry.npmjs.org/css-tree@1.1.3:
    resolution: {integrity: sha512-tRpdppF7TRazZrjJ6v3stzv93qxRcSsFmW6cX0Zm2NVKpxE1WV1HblnghVv9TreireHkqI/VDEsfolRF1p6y7Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/css-tree/-/css-tree-1.1.3.tgz}
    name: css-tree
    version: 1.1.3
    engines: {node: '>=8.0.0'}
    dependencies:
      mdn-data: registry.npmjs.org/mdn-data@2.0.14
      source-map: registry.npmjs.org/source-map@0.6.1
    dev: true

  registry.npmjs.org/css-what@6.1.0:
    resolution: {integrity: sha512-HTUrgRJ7r4dsZKU6GjmpfRK1O76h97Z8MfS1G0FozR+oF2kG6Vfe8JE6zwrkbxigziPHinCJ+gCPjA9EaBDtRw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/css-what/-/css-what-6.1.0.tgz}
    name: css-what
    version: 6.1.0
    engines: {node: '>= 6'}
    dev: true

  registry.npmjs.org/cssesc@3.0.0:
    resolution: {integrity: sha512-/Tb/JcjK111nNScGob5MNtsntNM1aCNUDipB/TkwZFhyDrrE47SOx/18wF2bbjgc3ZzCSKW1T5nt5EbFoAz/Vg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cssesc/-/cssesc-3.0.0.tgz}
    name: cssesc
    version: 3.0.0
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  registry.npmjs.org/cssnano-preset-default@5.2.14(postcss@8.4.38):
    resolution: {integrity: sha512-t0SFesj/ZV2OTylqQVOrFgEh5uanxbO6ZAdeCrNsUQ6fVuXwYTxJPNAGvGTxHbD68ldIJNec7PyYZDBrfDQ+6A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cssnano-preset-default/-/cssnano-preset-default-5.2.14.tgz}
    id: registry.npmjs.org/cssnano-preset-default/5.2.14
    name: cssnano-preset-default
    version: 5.2.14
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      css-declaration-sorter: registry.npmjs.org/css-declaration-sorter@6.4.1(postcss@8.4.38)
      cssnano-utils: registry.npmjs.org/cssnano-utils@3.1.0(postcss@8.4.38)
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-calc: registry.npmjs.org/postcss-calc@8.2.4(postcss@8.4.38)
      postcss-colormin: registry.npmjs.org/postcss-colormin@5.3.1(postcss@8.4.38)
      postcss-convert-values: registry.npmjs.org/postcss-convert-values@5.1.3(postcss@8.4.38)
      postcss-discard-comments: registry.npmjs.org/postcss-discard-comments@5.1.2(postcss@8.4.38)
      postcss-discard-duplicates: registry.npmjs.org/postcss-discard-duplicates@5.1.0(postcss@8.4.38)
      postcss-discard-empty: registry.npmjs.org/postcss-discard-empty@5.1.1(postcss@8.4.38)
      postcss-discard-overridden: registry.npmjs.org/postcss-discard-overridden@5.1.0(postcss@8.4.38)
      postcss-merge-longhand: registry.npmjs.org/postcss-merge-longhand@5.1.7(postcss@8.4.38)
      postcss-merge-rules: registry.npmjs.org/postcss-merge-rules@5.1.4(postcss@8.4.38)
      postcss-minify-font-values: registry.npmjs.org/postcss-minify-font-values@5.1.0(postcss@8.4.38)
      postcss-minify-gradients: registry.npmjs.org/postcss-minify-gradients@5.1.1(postcss@8.4.38)
      postcss-minify-params: registry.npmjs.org/postcss-minify-params@5.1.4(postcss@8.4.38)
      postcss-minify-selectors: registry.npmjs.org/postcss-minify-selectors@5.2.1(postcss@8.4.38)
      postcss-normalize-charset: registry.npmjs.org/postcss-normalize-charset@5.1.0(postcss@8.4.38)
      postcss-normalize-display-values: registry.npmjs.org/postcss-normalize-display-values@5.1.0(postcss@8.4.38)
      postcss-normalize-positions: registry.npmjs.org/postcss-normalize-positions@5.1.1(postcss@8.4.38)
      postcss-normalize-repeat-style: registry.npmjs.org/postcss-normalize-repeat-style@5.1.1(postcss@8.4.38)
      postcss-normalize-string: registry.npmjs.org/postcss-normalize-string@5.1.0(postcss@8.4.38)
      postcss-normalize-timing-functions: registry.npmjs.org/postcss-normalize-timing-functions@5.1.0(postcss@8.4.38)
      postcss-normalize-unicode: registry.npmjs.org/postcss-normalize-unicode@5.1.1(postcss@8.4.38)
      postcss-normalize-url: registry.npmjs.org/postcss-normalize-url@5.1.0(postcss@8.4.38)
      postcss-normalize-whitespace: registry.npmjs.org/postcss-normalize-whitespace@5.1.1(postcss@8.4.38)
      postcss-ordered-values: registry.npmjs.org/postcss-ordered-values@5.1.3(postcss@8.4.38)
      postcss-reduce-initial: registry.npmjs.org/postcss-reduce-initial@5.1.2(postcss@8.4.38)
      postcss-reduce-transforms: registry.npmjs.org/postcss-reduce-transforms@5.1.0(postcss@8.4.38)
      postcss-svgo: registry.npmjs.org/postcss-svgo@5.1.0(postcss@8.4.38)
      postcss-unique-selectors: registry.npmjs.org/postcss-unique-selectors@5.1.1(postcss@8.4.38)
    dev: true

  registry.npmjs.org/cssnano-utils@3.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-JQNR19/YZhz4psLX/rQ9M83e3z2Wf/HdJbryzte4a3NSuafyp9w/I4U+hx5C2S9g41qlstH7DEWnZaaj83OuEA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cssnano-utils/-/cssnano-utils-3.1.0.tgz}
    id: registry.npmjs.org/cssnano-utils/3.1.0
    name: cssnano-utils
    version: 3.1.0
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
    dev: true

  registry.npmjs.org/cssnano@5.1.15(postcss@8.4.38):
    resolution: {integrity: sha512-j+BKgDcLDQA+eDifLx0EO4XSA56b7uut3BQFH+wbSaSTuGLuiyTa/wbRYthUXX8LC9mLg+WWKe8h+qJuwTAbHw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/cssnano/-/cssnano-5.1.15.tgz}
    id: registry.npmjs.org/cssnano/5.1.15
    name: cssnano
    version: 5.1.15
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      cssnano-preset-default: registry.npmjs.org/cssnano-preset-default@5.2.14(postcss@8.4.38)
      lilconfig: registry.npmjs.org/lilconfig@2.1.0
      postcss: registry.npmjs.org/postcss@8.4.38
      yaml: registry.npmjs.org/yaml@1.10.2
    dev: true

  registry.npmjs.org/csso@4.2.0:
    resolution: {integrity: sha512-wvlcdIbf6pwKEk7vHj8/Bkc0B4ylXZruLvOgs9doS5eOsOpuodOV2zJChSpkp+pRpYQLQMeF04nr3Z68Sta9jA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/csso/-/csso-4.2.0.tgz}
    name: csso
    version: 4.2.0
    engines: {node: '>=8.0.0'}
    dependencies:
      css-tree: registry.npmjs.org/css-tree@1.1.3
    dev: true

  registry.npmjs.org/csstype@3.1.3:
    resolution: {integrity: sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz}
    name: csstype
    version: 3.1.3

  registry.npmjs.org/de-indent@1.0.2:
    resolution: {integrity: sha512-e/1zu3xH5MQryN2zdVaF0OrdNLUbvWxzMbi+iNA6Bky7l1RoP8a2fIbRocyHclXt/arDrrR6lL3TqFD9pMQTsg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/de-indent/-/de-indent-1.0.2.tgz}
    name: de-indent
    version: 1.0.2
    dev: true

  registry.npmjs.org/debounce@1.2.1:
    resolution: {integrity: sha512-XRRe6Glud4rd/ZGQfiV1ruXSfbvfJedlV9Y6zOlP+2K04vBYiJEte6stfFkCP03aMnY5tsipamumUjL14fofug==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/debounce/-/debounce-1.2.1.tgz}
    name: debounce
    version: 1.2.1
    dev: true

  registry.npmjs.org/debug@2.6.9:
    resolution: {integrity: sha512-bC7ElrdJaJnPbAP+1EotYvqZsb3ecl5wi6Bfi6BJTUcNowp6cvspg0jXznRTKDjm/E7AdgFBVeAPVMNcKGsHMA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/debug/-/debug-2.6.9.tgz}
    name: debug
    version: 2.6.9
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: registry.npmjs.org/ms@2.0.0
    dev: true

  registry.npmjs.org/debug@3.2.7:
    resolution: {integrity: sha512-CFjzYYAi4ThfiQvizrFQevTTXHtnCqWfe7x1AhgEscTz6ZbLbfoLRLPugTQyBth6f8ZERVUSyWHFD/7Wu4t1XQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/debug/-/debug-3.2.7.tgz}
    name: debug
    version: 3.2.7
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: registry.npmjs.org/ms@2.1.3
    dev: true

  registry.npmjs.org/debug@4.3.4:
    resolution: {integrity: sha512-PRWFHuSU3eDtQJPvnNY7Jcket1j0t5OuOsFzPPzsekD52Zl8qUfFIPEiswXqIvHWGVHOgX+7G/vCNNhehwxfkQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/debug/-/debug-4.3.4.tgz}
    name: debug
    version: 4.3.4
    engines: {node: '>=6.0'}
    peerDependencies:
      supports-color: '*'
    peerDependenciesMeta:
      supports-color:
        optional: true
    dependencies:
      ms: registry.npmjs.org/ms@2.1.2
    dev: true

  registry.npmjs.org/deep-is@0.1.4:
    resolution: {integrity: sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz}
    name: deep-is
    version: 0.1.4
    dev: true

  registry.npmjs.org/deepmerge@1.5.2:
    resolution: {integrity: sha512-95k0GDqvBjZavkuvzx/YqVLv/6YYa17fz6ILMSf7neqQITCPbnfEnQvEgMPNjH4kgobe7+WIL0yJEHku+H3qtQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/deepmerge/-/deepmerge-1.5.2.tgz}
    name: deepmerge
    version: 1.5.2
    engines: {node: '>=0.10.0'}

  registry.npmjs.org/default-gateway@6.0.3:
    resolution: {integrity: sha512-fwSOJsbbNzZ/CUFpqFBqYfYNLj1NbMPm8MMCIzHjC83iSJRBEGmDUxU+WP661BaBQImeC2yHwXtz+P/O9o+XEg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/default-gateway/-/default-gateway-6.0.3.tgz}
    name: default-gateway
    version: 6.0.3
    engines: {node: '>= 10'}
    dependencies:
      execa: registry.npmjs.org/execa@5.1.1
    dev: true

  registry.npmjs.org/defaults@1.0.4:
    resolution: {integrity: sha512-eFuaLoy/Rxalv2kr+lqMlUnrDWV+3j4pljOIJgLIhI058IQfWJ7vXhyEIHu+HtC738klGALYxOKDO0bQP3tg8A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/defaults/-/defaults-1.0.4.tgz}
    name: defaults
    version: 1.0.4
    dependencies:
      clone: registry.npmjs.org/clone@1.0.4
    dev: true

  registry.npmjs.org/define-data-property@1.1.4:
    resolution: {integrity: sha512-rBMvIzlpA8v6E+SJZoo++HAYqsLrkg7MSfIinMPFhmkorw7X+dOXVJQs+QT69zGkzMyfDnIMN2Wid1+NbL3T+A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/define-data-property/-/define-data-property-1.1.4.tgz}
    name: define-data-property
    version: 1.1.4
    engines: {node: '>= 0.4'}
    dependencies:
      es-define-property: registry.npmjs.org/es-define-property@1.0.0
      es-errors: registry.npmjs.org/es-errors@1.3.0
      gopd: registry.npmjs.org/gopd@1.0.1
    dev: true

  registry.npmjs.org/define-lazy-prop@2.0.0:
    resolution: {integrity: sha512-Ds09qNh8yw3khSjiJjiUInaGX9xlqZDY7JVryGxdxV7NPeuqQfplOpQ66yJFZut3jLa5zOwkXw1g9EI2uKh4Og==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/define-lazy-prop/-/define-lazy-prop-2.0.0.tgz}
    name: define-lazy-prop
    version: 2.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/define-properties@1.2.1:
    resolution: {integrity: sha512-8QmQKqEASLd5nx0U1B1okLElbUuuttJ/AnYmRXbbbGDWh6uS208EjD4Xqq/I9wK7u0v6O08XhTWnt5XtEbR6Dg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/define-properties/-/define-properties-1.2.1.tgz}
    name: define-properties
    version: 1.2.1
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: registry.npmjs.org/define-data-property@1.1.4
      has-property-descriptors: registry.npmjs.org/has-property-descriptors@1.0.2
      object-keys: registry.npmjs.org/object-keys@1.1.1
    dev: true

  registry.npmjs.org/depd@1.1.2:
    resolution: {integrity: sha512-7emPTl6Dpo6JRXOXjLRxck+FlLRX5847cLKEn00PLAgc3g2hTZZgr+e4c2v6QpSmLeFP3n5yUo7ft6avBK/5jQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/depd/-/depd-1.1.2.tgz}
    name: depd
    version: 1.1.2
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/depd@2.0.0:
    resolution: {integrity: sha512-g7nH6P6dyDioJogAAGprGpCtVImJhpPk/roCzdb3fIh61/s/nPsfR6onyMwkCAR/OlC3yBC0lESvUoQEAssIrw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/depd/-/depd-2.0.0.tgz}
    name: depd
    version: 2.0.0
    engines: {node: '>= 0.8'}
    dev: true

  registry.npmjs.org/destroy@1.2.0:
    resolution: {integrity: sha512-2sJGJTaXIIaR1w4iJSNoN0hnMY7Gpc/n8D4qSCJw8QqFWXf7cuAgnEHxBpweaVcPevC2l3KpjYCx3NypQQgaJg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/destroy/-/destroy-1.2.0.tgz}
    name: destroy
    version: 1.2.0
    engines: {node: '>= 0.8', npm: 1.2.8000 || >= 1.4.16}
    dev: true

  registry.npmjs.org/detect-node@2.1.0:
    resolution: {integrity: sha512-T0NIuQpnTvFDATNuHN5roPwSBG83rFsuO+MXXH9/3N1eFbn4wcPjttvjMLEPWJ0RGUYgQE7cGgS3tNxbqCGM7g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/detect-node/-/detect-node-2.1.0.tgz}
    name: detect-node
    version: 2.1.0
    dev: true

  registry.npmjs.org/dir-glob@3.0.1:
    resolution: {integrity: sha512-WkrWp9GR4KXfKGYzOLmTuGVi1UWFfws377n9cc55/tb6DuqyF6pcQ5AbiHEshaDpY9v6oaSr2XCDidGmMwdzIA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/dir-glob/-/dir-glob-3.0.1.tgz}
    name: dir-glob
    version: 3.0.1
    engines: {node: '>=8'}
    dependencies:
      path-type: registry.npmjs.org/path-type@4.0.0
    dev: true

  registry.npmjs.org/dns-packet@5.6.1:
    resolution: {integrity: sha512-l4gcSouhcgIKRvyy99RNVOgxXiicE+2jZoNmaNmZ6JXiGajBOJAesk1OBlJuM5k2c+eudGdLxDqXuPCKIj6kpw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/dns-packet/-/dns-packet-5.6.1.tgz}
    name: dns-packet
    version: 5.6.1
    engines: {node: '>=6'}
    dependencies:
      '@leichtgewicht/ip-codec': registry.npmjs.org/@leichtgewicht/ip-codec@2.0.5
    dev: true

  registry.npmjs.org/doctrine@3.0.0:
    resolution: {integrity: sha512-yS+Q5i3hBf7GBkd4KG8a7eBNNWNGLTaEwwYWUijIYM7zrlYDM0BFXHjjPWlWZ1Rg7UaddZeIDmi9jF3HmqiQ2w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/doctrine/-/doctrine-3.0.0.tgz}
    name: doctrine
    version: 3.0.0
    engines: {node: '>=6.0.0'}
    dependencies:
      esutils: registry.npmjs.org/esutils@2.0.3
    dev: true

  registry.npmjs.org/dom-converter@0.2.0:
    resolution: {integrity: sha512-gd3ypIPfOMr9h5jIKq8E3sHOTCjeirnl0WK5ZdS1AW0Odt0b1PaWaHdJ4Qk4klv+YB9aJBS7mESXjFoDQPu6DA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/dom-converter/-/dom-converter-0.2.0.tgz}
    name: dom-converter
    version: 0.2.0
    dependencies:
      utila: registry.npmjs.org/utila@0.4.0
    dev: true

  registry.npmjs.org/dom-serializer@1.4.1:
    resolution: {integrity: sha512-VHwB3KfrcOOkelEG2ZOfxqLZdfkil8PtJi4P8N2MMXucZq2yLp75ClViUlOVwyoHEDjYU433Aq+5zWP61+RGag==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/dom-serializer/-/dom-serializer-1.4.1.tgz}
    name: dom-serializer
    version: 1.4.1
    dependencies:
      domelementtype: registry.npmjs.org/domelementtype@2.3.0
      domhandler: registry.npmjs.org/domhandler@4.3.1
      entities: registry.npmjs.org/entities@2.2.0
    dev: true

  registry.npmjs.org/domelementtype@2.3.0:
    resolution: {integrity: sha512-OLETBj6w0OsagBwdXnPdN0cnMfF9opN69co+7ZrbfPGrdpPVNBUj02spi6B1N7wChLQiPn4CSH/zJvXw56gmHw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/domelementtype/-/domelementtype-2.3.0.tgz}
    name: domelementtype
    version: 2.3.0
    dev: true

  registry.npmjs.org/domhandler@4.3.1:
    resolution: {integrity: sha512-GrwoxYN+uWlzO8uhUXRl0P+kHE4GtVPfYzVLcUxPL7KNdHKj66vvlhiweIHqYYXWlw+T8iLMp42Lm67ghw4WMQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/domhandler/-/domhandler-4.3.1.tgz}
    name: domhandler
    version: 4.3.1
    engines: {node: '>= 4'}
    dependencies:
      domelementtype: registry.npmjs.org/domelementtype@2.3.0
    dev: true

  registry.npmjs.org/domutils@2.8.0:
    resolution: {integrity: sha512-w96Cjofp72M5IIhpjgobBimYEfoPjx1Vx0BSX9P30WBdZW2WIKU0T1Bd0kz2eNZ9ikjKgHbEyKx8BB6H1L3h3A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/domutils/-/domutils-2.8.0.tgz}
    name: domutils
    version: 2.8.0
    dependencies:
      dom-serializer: registry.npmjs.org/dom-serializer@1.4.1
      domelementtype: registry.npmjs.org/domelementtype@2.3.0
      domhandler: registry.npmjs.org/domhandler@4.3.1
    dev: true

  registry.npmjs.org/dot-case@3.0.4:
    resolution: {integrity: sha512-Kv5nKlh6yRrdrGvxeJ2e5y2eRUpkUosIW4A2AS38zwSz27zu7ufDwQPi5Jhs3XAlGNetl3bmnGhQsMtkKJnj3w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/dot-case/-/dot-case-3.0.4.tgz}
    name: dot-case
    version: 3.0.4
    dependencies:
      no-case: registry.npmjs.org/no-case@3.0.4
      tslib: registry.npmjs.org/tslib@2.6.2
    dev: true

  registry.npmjs.org/dotenv-expand@5.1.0:
    resolution: {integrity: sha512-YXQl1DSa4/PQyRfgrv6aoNjhasp/p4qs9FjJ4q4cQk+8m4r6k4ZSiEyytKG8f8W9gi8WsQtIObNmKd+tMzNTmA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/dotenv-expand/-/dotenv-expand-5.1.0.tgz}
    name: dotenv-expand
    version: 5.1.0
    dev: true

  registry.npmjs.org/dotenv@10.0.0:
    resolution: {integrity: sha512-rlBi9d8jpv9Sf1klPjNfFAuWDjKLwTIJJ/VxtoTwIR6hnZxcEOQCZg2oIL3MWBYw5GpUDKOEnND7LXTbIpQ03Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/dotenv/-/dotenv-10.0.0.tgz}
    name: dotenv
    version: 10.0.0
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/duplexer@0.1.2:
    resolution: {integrity: sha512-jtD6YG370ZCIi/9GTaJKQxWTZD045+4R4hTk/x1UyoqadyJ9x9CgSi1RlVDQF8U2sxLLSnFkCaMihqljHIWgMg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/duplexer/-/duplexer-0.1.2.tgz}
    name: duplexer
    version: 0.1.2
    dev: true

  registry.npmjs.org/easy-stack@1.0.1:
    resolution: {integrity: sha512-wK2sCs4feiiJeFXn3zvY0p41mdU5VUgbgs1rNsc/y5ngFUijdWd+iIN8eoyuZHKB8xN6BL4PdWmzqFmxNg6V2w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/easy-stack/-/easy-stack-1.0.1.tgz}
    name: easy-stack
    version: 1.0.1
    engines: {node: '>=6.0.0'}
    dev: true

  registry.npmjs.org/ee-first@1.1.1:
    resolution: {integrity: sha512-WMwm9LhRUo+WUaRN+vRuETqG89IgZphVSNkdFgeb6sS/E4OrDIN7t48CAewSHXc6C8lefD8KKfr5vY61brQlow==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ee-first/-/ee-first-1.1.1.tgz}
    name: ee-first
    version: 1.1.1
    dev: true

  registry.npmjs.org/electron-to-chromium@1.4.740:
    resolution: {integrity: sha512-Yvg5i+iyv7Xm18BRdVPVm8lc7kgxM3r6iwqCH2zB7QZy1kZRNmd0Zqm0zcD9XoFREE5/5rwIuIAOT+/mzGcnZg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.4.740.tgz}
    name: electron-to-chromium
    version: 1.4.740
    dev: true

  registry.npmjs.org/element-ui@2.15.14(vue@2.7.16):
    resolution: {integrity: sha512-2v9fHL0ZGINotOlRIAJD5YuVB8V7WKxrE9Qy7dXhRipa035+kF7WuU/z+tEmLVPBcJ0zt8mOu1DKpWcVzBK8IA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/element-ui/-/element-ui-2.15.14.tgz}
    id: registry.npmjs.org/element-ui/2.15.14
    name: element-ui
    version: 2.15.14
    peerDependencies:
      vue: ^2.5.17
    dependencies:
      async-validator: registry.npmjs.org/async-validator@1.8.5
      babel-helper-vue-jsx-merge-props: registry.npmjs.org/babel-helper-vue-jsx-merge-props@2.0.3
      deepmerge: registry.npmjs.org/deepmerge@1.5.2
      normalize-wheel: registry.npmjs.org/normalize-wheel@1.0.1
      resize-observer-polyfill: registry.npmjs.org/resize-observer-polyfill@1.5.1
      throttle-debounce: registry.npmjs.org/throttle-debounce@1.1.0
      vue: registry.npmjs.org/vue@2.7.16
    dev: false

  registry.npmjs.org/emoji-regex@8.0.0:
    resolution: {integrity: sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz}
    name: emoji-regex
    version: 8.0.0
    dev: true

  registry.npmjs.org/emojis-list@3.0.0:
    resolution: {integrity: sha512-/kyM18EfinwXZbno9FyUGeFh87KC8HRQBQGildHZbEuRyWFOmv1U10o9BBp8XVZDVNNuQKyIGIu5ZYAAXJ0V2Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/emojis-list/-/emojis-list-3.0.0.tgz}
    name: emojis-list
    version: 3.0.0
    engines: {node: '>= 4'}
    dev: true

  registry.npmjs.org/encodeurl@1.0.2:
    resolution: {integrity: sha512-TPJXq8JqFaVYm2CWmPvnP2Iyo4ZSM7/QKcSmuMLDObfpH5fi7RUGmd/rTDf+rut/saiDiQEeVTNgAmJEdAOx0w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/encodeurl/-/encodeurl-1.0.2.tgz}
    name: encodeurl
    version: 1.0.2
    engines: {node: '>= 0.8'}
    dev: true

  registry.npmjs.org/end-of-stream@1.4.4:
    resolution: {integrity: sha512-+uw1inIHVPQoaVuHzRyXd21icM+cnt4CzD5rW+NC1wjOUSTOs+Te7FOv7AhN7vS9x/oIyhLP5PR1H+phQAHu5Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/end-of-stream/-/end-of-stream-1.4.4.tgz}
    name: end-of-stream
    version: 1.4.4
    dependencies:
      once: registry.npmjs.org/once@1.4.0
    dev: true

  registry.npmjs.org/enhanced-resolve@5.16.0:
    resolution: {integrity: sha512-O+QWCviPNSSLAD9Ucn8Awv+poAkqn3T1XY5/N7kR7rQO9yfSGWkYZDwpJ+iKF7B8rxaQKWngSqACpgzeapSyoA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.16.0.tgz}
    name: enhanced-resolve
    version: 5.16.0
    engines: {node: '>=10.13.0'}
    dependencies:
      graceful-fs: registry.npmjs.org/graceful-fs@4.2.11
      tapable: registry.npmjs.org/tapable@2.2.1
    dev: true

  registry.npmjs.org/enquirer@2.4.1:
    resolution: {integrity: sha512-rRqJg/6gd538VHvR3PSrdRBb/1Vy2YfzHqzvbhGIQpDRKIa4FgV/54b5Q1xYSxOOwKvjXweS26E0Q+nAMwp2pQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/enquirer/-/enquirer-2.4.1.tgz}
    name: enquirer
    version: 2.4.1
    engines: {node: '>=8.6'}
    dependencies:
      ansi-colors: registry.npmjs.org/ansi-colors@4.1.3
      strip-ansi: registry.npmjs.org/strip-ansi@6.0.1
    dev: true

  registry.npmjs.org/entities@2.2.0:
    resolution: {integrity: sha512-p92if5Nz619I0w+akJrLZH0MX0Pb5DX39XOwQTtXSdQQOaYH03S1uIQp4mhOZtAXrxq4ViO67YTiLBo2638o9A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/entities/-/entities-2.2.0.tgz}
    name: entities
    version: 2.2.0
    dev: true

  registry.npmjs.org/entities@4.5.0:
    resolution: {integrity: sha512-V0hjH4dGPh9Ao5p0MoRY6BVqtwCjhz6vI5LT8AJ55H+4g9/4vbHx1I54fS0XuclLhDHArPQCiMjDxjaL8fPxhw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/entities/-/entities-4.5.0.tgz}
    name: entities
    version: 4.5.0
    engines: {node: '>=0.12'}
    dev: true

  registry.npmjs.org/error-ex@1.3.2:
    resolution: {integrity: sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz}
    name: error-ex
    version: 1.3.2
    dependencies:
      is-arrayish: registry.npmjs.org/is-arrayish@0.2.1
    dev: true

  registry.npmjs.org/error-stack-parser@2.1.4:
    resolution: {integrity: sha512-Sk5V6wVazPhq5MhpO+AUxJn5x7XSXGl1R93Vn7i+zS15KDVxQijejNCrz8340/2bgLBjR9GtEG8ZVKONDjcqGQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/error-stack-parser/-/error-stack-parser-2.1.4.tgz}
    name: error-stack-parser
    version: 2.1.4
    dependencies:
      stackframe: registry.npmjs.org/stackframe@1.3.4
    dev: true

  registry.npmjs.org/es-define-property@1.0.0:
    resolution: {integrity: sha512-jxayLKShrEqqzJ0eumQbVhTYQM27CfT1T35+gCgDFoL82JLsXqTJ76zv6A0YLOgEnLUMvLzsDsGIrl8NFpT2gQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.0.tgz}
    name: es-define-property
    version: 1.0.0
    engines: {node: '>= 0.4'}
    dependencies:
      get-intrinsic: registry.npmjs.org/get-intrinsic@1.2.4
    dev: true

  registry.npmjs.org/es-errors@1.3.0:
    resolution: {integrity: sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz}
    name: es-errors
    version: 1.3.0
    engines: {node: '>= 0.4'}
    dev: true

  registry.npmjs.org/es-module-lexer@1.5.0:
    resolution: {integrity: sha512-pqrTKmwEIgafsYZAGw9kszYzmagcE/n4dbgwGWLEXg7J4QFJVQRBld8j3Q3GNez79jzxZshq0bcT962QHOghjw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/es-module-lexer/-/es-module-lexer-1.5.0.tgz}
    name: es-module-lexer
    version: 1.5.0
    dev: true

  registry.npmjs.org/escalade@3.1.2:
    resolution: {integrity: sha512-ErCHMCae19vR8vQGe50xIsVomy19rg6gFu3+r3jkEO46suLMWBksvVyoGgQV+jOfl84ZSOSlmv6Gxa89PmTGmA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/escalade/-/escalade-3.1.2.tgz}
    name: escalade
    version: 3.1.2
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/escape-html@1.0.3:
    resolution: {integrity: sha512-NiSupZ4OeuGwr68lGIeym/ksIZMJodUGOSCZ/FSnTxcrekbvqrgdUxlJOMpijaKZVjAJrWrGs/6Jy8OMuyj9ow==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/escape-html/-/escape-html-1.0.3.tgz}
    name: escape-html
    version: 1.0.3
    dev: true

  registry.npmjs.org/escape-string-regexp@1.0.5:
    resolution: {integrity: sha512-vbRorB5FUQWvla16U8R/qgaFIya2qGzwDrNmCZuYKrbdSUMG6I1ZCGQRefkRVhuOkIGVne7BQ35DSfo1qvJqFg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-1.0.5.tgz}
    name: escape-string-regexp
    version: 1.0.5
    engines: {node: '>=0.8.0'}
    dev: true

  registry.npmjs.org/escape-string-regexp@4.0.0:
    resolution: {integrity: sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz}
    name: escape-string-regexp
    version: 4.0.0
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/eslint-plugin-vue@8.7.1(eslint@7.32.0):
    resolution: {integrity: sha512-28sbtm4l4cOzoO1LtzQPxfxhQABararUb1JtqusQqObJpWX2e/gmVyeYVfepizPFne0Q5cILkYGiBoV36L12Wg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/eslint-plugin-vue/-/eslint-plugin-vue-8.7.1.tgz}
    id: registry.npmjs.org/eslint-plugin-vue/8.7.1
    name: eslint-plugin-vue
    version: 8.7.1
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: ^6.2.0 || ^7.0.0 || ^8.0.0
    dependencies:
      eslint: registry.npmjs.org/eslint@7.32.0
      eslint-utils: registry.npmjs.org/eslint-utils@3.0.0(eslint@7.32.0)
      natural-compare: registry.npmjs.org/natural-compare@1.4.0
      nth-check: registry.npmjs.org/nth-check@2.1.1
      postcss-selector-parser: registry.npmjs.org/postcss-selector-parser@6.0.16
      semver: registry.npmjs.org/semver@7.6.0
      vue-eslint-parser: registry.npmjs.org/vue-eslint-parser@8.3.0(eslint@7.32.0)
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/eslint-scope@5.1.1:
    resolution: {integrity: sha512-2NxwbF/hZ0KpepYN0cNbo+FN6XoK7GaHlQhgx/hIZl6Va0bF45RQOOwhLIy8lQDbuCiadSLCBnH2CFYquit5bw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/eslint-scope/-/eslint-scope-5.1.1.tgz}
    name: eslint-scope
    version: 5.1.1
    engines: {node: '>=8.0.0'}
    dependencies:
      esrecurse: registry.npmjs.org/esrecurse@4.3.0
      estraverse: registry.npmjs.org/estraverse@4.3.0
    dev: true

  registry.npmjs.org/eslint-scope@7.2.2:
    resolution: {integrity: sha512-dOt21O7lTMhDM+X9mB4GX+DZrZtCUJPL/wlcTqxyrx5IvO0IYtILdtrQGQp+8n5S0gwSVmOf9NQrjMOgfQZlIg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/eslint-scope/-/eslint-scope-7.2.2.tgz}
    name: eslint-scope
    version: 7.2.2
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      esrecurse: registry.npmjs.org/esrecurse@4.3.0
      estraverse: registry.npmjs.org/estraverse@5.3.0
    dev: true

  registry.npmjs.org/eslint-utils@2.1.0:
    resolution: {integrity: sha512-w94dQYoauyvlDc43XnGB8lU3Zt713vNChgt4EWwhXAP2XkBvndfxF0AgIqKOOasjPIPzj9JqgwkwbCYD0/V3Zg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/eslint-utils/-/eslint-utils-2.1.0.tgz}
    name: eslint-utils
    version: 2.1.0
    engines: {node: '>=6'}
    dependencies:
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys@1.3.0
    dev: true

  registry.npmjs.org/eslint-utils@3.0.0(eslint@7.32.0):
    resolution: {integrity: sha512-uuQC43IGctw68pJA1RgbQS8/NP7rch6Cwd4j3ZBtgo4/8Flj4eGE7ZYSZRN3iq5pVUv6GPdW5Z1RFleo84uLDA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/eslint-utils/-/eslint-utils-3.0.0.tgz}
    id: registry.npmjs.org/eslint-utils/3.0.0
    name: eslint-utils
    version: 3.0.0
    engines: {node: ^10.0.0 || ^12.0.0 || >= 14.0.0}
    peerDependencies:
      eslint: '>=5'
    dependencies:
      eslint: registry.npmjs.org/eslint@7.32.0
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys@2.1.0
    dev: true

  registry.npmjs.org/eslint-visitor-keys@1.3.0:
    resolution: {integrity: sha512-6J72N8UNa462wa/KFODt/PJ3IU60SDpC3QXC1Hjc1BXXpfL2C9R5+AU7jhe0F6GREqVMh4Juu+NY7xn+6dipUQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-1.3.0.tgz}
    name: eslint-visitor-keys
    version: 1.3.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/eslint-visitor-keys@2.1.0:
    resolution: {integrity: sha512-0rSmRBzXgDzIsD6mGdJgevzgezI534Cer5L/vyMX0kHzT/jiB43jRhd9YUlMGYLQy2zprNmoT8qasCGtY+QaKw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-2.1.0.tgz}
    name: eslint-visitor-keys
    version: 2.1.0
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/eslint-visitor-keys@3.4.3:
    resolution: {integrity: sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz}
    name: eslint-visitor-keys
    version: 3.4.3
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dev: true

  registry.npmjs.org/eslint-webpack-plugin@3.2.0(eslint@7.32.0)(webpack@5.91.0):
    resolution: {integrity: sha512-avrKcGncpPbPSUHX6B3stNGzkKFto3eL+DKM4+VyMrVnhPc3vRczVlCq3uhuFOdRvDHTVXuzwk1ZKUrqDQHQ9w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/eslint-webpack-plugin/-/eslint-webpack-plugin-3.2.0.tgz}
    id: registry.npmjs.org/eslint-webpack-plugin/3.2.0
    name: eslint-webpack-plugin
    version: 3.2.0
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      eslint: ^7.0.0 || ^8.0.0
      webpack: ^5.0.0
    dependencies:
      '@types/eslint': registry.npmjs.org/@types/eslint@8.56.9
      eslint: registry.npmjs.org/eslint@7.32.0
      jest-worker: registry.npmjs.org/jest-worker@28.1.3
      micromatch: registry.npmjs.org/micromatch@4.0.5
      normalize-path: registry.npmjs.org/normalize-path@3.0.0
      schema-utils: registry.npmjs.org/schema-utils@4.2.0
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/eslint@7.32.0:
    resolution: {integrity: sha512-VHZ8gX+EDfz+97jGcgyGCyRia/dPOd6Xh9yPv8Bl1+SoaIwD+a/vlrOmGRUyOYu7MwUhc7CxqeaDZU13S4+EpA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/eslint/-/eslint-7.32.0.tgz}
    name: eslint
    version: 7.32.0
    engines: {node: ^10.12.0 || >=12.0.0}
    hasBin: true
    dependencies:
      '@babel/code-frame': registry.npmjs.org/@babel/code-frame@7.12.11
      '@eslint/eslintrc': registry.npmjs.org/@eslint/eslintrc@0.4.3
      '@humanwhocodes/config-array': registry.npmjs.org/@humanwhocodes/config-array@0.5.0
      ajv: registry.npmjs.org/ajv@6.12.6
      chalk: registry.npmjs.org/chalk@4.1.2
      cross-spawn: registry.npmjs.org/cross-spawn@7.0.3
      debug: registry.npmjs.org/debug@4.3.4
      doctrine: registry.npmjs.org/doctrine@3.0.0
      enquirer: registry.npmjs.org/enquirer@2.4.1
      escape-string-regexp: registry.npmjs.org/escape-string-regexp@4.0.0
      eslint-scope: registry.npmjs.org/eslint-scope@5.1.1
      eslint-utils: registry.npmjs.org/eslint-utils@2.1.0
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys@2.1.0
      espree: registry.npmjs.org/espree@7.3.1
      esquery: registry.npmjs.org/esquery@1.5.0
      esutils: registry.npmjs.org/esutils@2.0.3
      fast-deep-equal: registry.npmjs.org/fast-deep-equal@3.1.3
      file-entry-cache: registry.npmjs.org/file-entry-cache@6.0.1
      functional-red-black-tree: registry.npmjs.org/functional-red-black-tree@1.0.1
      glob-parent: registry.npmjs.org/glob-parent@5.1.2
      globals: registry.npmjs.org/globals@13.24.0
      ignore: registry.npmjs.org/ignore@4.0.6
      import-fresh: registry.npmjs.org/import-fresh@3.3.0
      imurmurhash: registry.npmjs.org/imurmurhash@0.1.4
      is-glob: registry.npmjs.org/is-glob@4.0.3
      js-yaml: registry.npmjs.org/js-yaml@3.14.1
      json-stable-stringify-without-jsonify: registry.npmjs.org/json-stable-stringify-without-jsonify@1.0.1
      levn: registry.npmjs.org/levn@0.4.1
      lodash.merge: registry.npmjs.org/lodash.merge@4.6.2
      minimatch: registry.npmjs.org/minimatch@3.1.2
      natural-compare: registry.npmjs.org/natural-compare@1.4.0
      optionator: registry.npmjs.org/optionator@0.9.3
      progress: registry.npmjs.org/progress@2.0.3
      regexpp: registry.npmjs.org/regexpp@3.2.0
      semver: registry.npmjs.org/semver@7.6.0
      strip-ansi: registry.npmjs.org/strip-ansi@6.0.1
      strip-json-comments: registry.npmjs.org/strip-json-comments@3.1.1
      table: registry.npmjs.org/table@6.8.2
      text-table: registry.npmjs.org/text-table@0.2.0
      v8-compile-cache: registry.npmjs.org/v8-compile-cache@2.4.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/espree@7.3.1:
    resolution: {integrity: sha512-v3JCNCE64umkFpmkFGqzVKsOT0tN1Zr+ueqLZfpV1Ob8e+CEgPWa+OxCoGH3tnhimMKIaBm4m/vaRpJ/krRz2g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/espree/-/espree-7.3.1.tgz}
    name: espree
    version: 7.3.1
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      acorn: registry.npmjs.org/acorn@7.4.1
      acorn-jsx: registry.npmjs.org/acorn-jsx@5.3.2(acorn@7.4.1)
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys@1.3.0
    dev: true

  registry.npmjs.org/espree@9.6.1:
    resolution: {integrity: sha512-oruZaFkjorTpF32kDSI5/75ViwGeZginGGy2NoOSg3Q9bnwlnmDm4HLnkl0RE3n+njDXR037aY1+x58Z/zFdwQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/espree/-/espree-9.6.1.tgz}
    name: espree
    version: 9.6.1
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    dependencies:
      acorn: registry.npmjs.org/acorn@8.11.3
      acorn-jsx: registry.npmjs.org/acorn-jsx@5.3.2(acorn@8.11.3)
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys@3.4.3
    dev: true

  registry.npmjs.org/esprima@4.0.1:
    resolution: {integrity: sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz}
    name: esprima
    version: 4.0.1
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  registry.npmjs.org/esquery@1.5.0:
    resolution: {integrity: sha512-YQLXUplAwJgCydQ78IMJywZCceoqk1oH01OERdSAJc/7U2AylwjhSCLDEtqwg811idIS/9fIU5GjG73IgjKMVg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/esquery/-/esquery-1.5.0.tgz}
    name: esquery
    version: 1.5.0
    engines: {node: '>=0.10'}
    dependencies:
      estraverse: registry.npmjs.org/estraverse@5.3.0
    dev: true

  registry.npmjs.org/esrecurse@4.3.0:
    resolution: {integrity: sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz}
    name: esrecurse
    version: 4.3.0
    engines: {node: '>=4.0'}
    dependencies:
      estraverse: registry.npmjs.org/estraverse@5.3.0
    dev: true

  registry.npmjs.org/estraverse@4.3.0:
    resolution: {integrity: sha512-39nnKffWz8xN1BU/2c79n9nB9HDzo0niYUqx6xyqUnyoAnQyyWpOTdZEeiCch8BBu515t4wp9ZmgVfVhn9EBpw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/estraverse/-/estraverse-4.3.0.tgz}
    name: estraverse
    version: 4.3.0
    engines: {node: '>=4.0'}
    dev: true

  registry.npmjs.org/estraverse@5.3.0:
    resolution: {integrity: sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz}
    name: estraverse
    version: 5.3.0
    engines: {node: '>=4.0'}
    dev: true

  registry.npmjs.org/estree-walker@2.0.2:
    resolution: {integrity: sha512-Rfkk/Mp/DL7JVje3u18FxFujQlTNR2q6QfMSMB7AvCBx91NGj/ba3kCfza0f6dVDbw7YlRf/nDrn7pQrCCyQ/w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/estree-walker/-/estree-walker-2.0.2.tgz}
    name: estree-walker
    version: 2.0.2
    dev: true

  registry.npmjs.org/esutils@2.0.3:
    resolution: {integrity: sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz}
    name: esutils
    version: 2.0.3
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/etag@1.8.1:
    resolution: {integrity: sha512-aIL5Fx7mawVa300al2BnEE4iNvo1qETxLrPI/o05L7z6go7fCw1J6EQmbK4FmJ2AS7kgVF/KEZWufBfdClMcPg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/etag/-/etag-1.8.1.tgz}
    name: etag
    version: 1.8.1
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/event-pubsub@4.3.0:
    resolution: {integrity: sha512-z7IyloorXvKbFx9Bpie2+vMJKKx1fH1EN5yiTfp8CiLOTptSYy1g8H4yDpGlEdshL1PBiFtBHepF2cNsqeEeFQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/event-pubsub/-/event-pubsub-4.3.0.tgz}
    name: event-pubsub
    version: 4.3.0
    engines: {node: '>=4.0.0'}
    dev: true

  registry.npmjs.org/eventemitter3@4.0.7:
    resolution: {integrity: sha512-8guHBZCwKnFhYdHr2ysuRWErTwhoN2X8XELRlrRwpmfeY2jjuUN4taQMsULKUVo1K4DvZl+0pgfyoysHxvmvEw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/eventemitter3/-/eventemitter3-4.0.7.tgz}
    name: eventemitter3
    version: 4.0.7
    dev: true

  registry.npmjs.org/events@3.3.0:
    resolution: {integrity: sha512-mQw+2fkQbALzQ7V0MY0IqdnXNOeTtP4r0lN9z7AAawCXgqea7bDii20AYrIBrFd/Hx0M2Ocz6S111CaFkUcb0Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/events/-/events-3.3.0.tgz}
    name: events
    version: 3.3.0
    engines: {node: '>=0.8.x'}
    dev: true

  registry.npmjs.org/execa@0.8.0:
    resolution: {integrity: sha512-zDWS+Rb1E8BlqqhALSt9kUhss8Qq4nN3iof3gsOdyINksElaPyNBtKUMTR62qhvgVWR0CqCX7sdnKe4MnUbFEA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/execa/-/execa-0.8.0.tgz}
    name: execa
    version: 0.8.0
    engines: {node: '>=4'}
    dependencies:
      cross-spawn: registry.npmjs.org/cross-spawn@5.1.0
      get-stream: registry.npmjs.org/get-stream@3.0.0
      is-stream: registry.npmjs.org/is-stream@1.1.0
      npm-run-path: registry.npmjs.org/npm-run-path@2.0.2
      p-finally: registry.npmjs.org/p-finally@1.0.0
      signal-exit: registry.npmjs.org/signal-exit@3.0.7
      strip-eof: registry.npmjs.org/strip-eof@1.0.0
    dev: true

  registry.npmjs.org/execa@1.0.0:
    resolution: {integrity: sha512-adbxcyWV46qiHyvSp50TKt05tB4tK3HcmF7/nxfAdhnox83seTDbwnaqKO4sXRy7roHAIFqJP/Rw/AuEbX61LA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/execa/-/execa-1.0.0.tgz}
    name: execa
    version: 1.0.0
    engines: {node: '>=6'}
    dependencies:
      cross-spawn: registry.npmjs.org/cross-spawn@6.0.5
      get-stream: registry.npmjs.org/get-stream@4.1.0
      is-stream: registry.npmjs.org/is-stream@1.1.0
      npm-run-path: registry.npmjs.org/npm-run-path@2.0.2
      p-finally: registry.npmjs.org/p-finally@1.0.0
      signal-exit: registry.npmjs.org/signal-exit@3.0.7
      strip-eof: registry.npmjs.org/strip-eof@1.0.0
    dev: true

  registry.npmjs.org/execa@5.1.1:
    resolution: {integrity: sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/execa/-/execa-5.1.1.tgz}
    name: execa
    version: 5.1.1
    engines: {node: '>=10'}
    dependencies:
      cross-spawn: registry.npmjs.org/cross-spawn@7.0.3
      get-stream: registry.npmjs.org/get-stream@6.0.1
      human-signals: registry.npmjs.org/human-signals@2.1.0
      is-stream: registry.npmjs.org/is-stream@2.0.1
      merge-stream: registry.npmjs.org/merge-stream@2.0.0
      npm-run-path: registry.npmjs.org/npm-run-path@4.0.1
      onetime: registry.npmjs.org/onetime@5.1.2
      signal-exit: registry.npmjs.org/signal-exit@3.0.7
      strip-final-newline: registry.npmjs.org/strip-final-newline@2.0.0
    dev: true

  registry.npmjs.org/express@4.19.2:
    resolution: {integrity: sha512-5T6nhjsT+EOMzuck8JjBHARTHfMht0POzlA60WV2pMD3gyXw2LZnZ+ueGdNxG+0calOJcWKbpFcuzLZ91YWq9Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/express/-/express-4.19.2.tgz}
    name: express
    version: 4.19.2
    engines: {node: '>= 0.10.0'}
    dependencies:
      accepts: registry.npmjs.org/accepts@1.3.8
      array-flatten: registry.npmjs.org/array-flatten@1.1.1
      body-parser: registry.npmjs.org/body-parser@1.20.2
      content-disposition: registry.npmjs.org/content-disposition@0.5.4
      content-type: registry.npmjs.org/content-type@1.0.5
      cookie: registry.npmjs.org/cookie@0.6.0
      cookie-signature: registry.npmjs.org/cookie-signature@1.0.6
      debug: registry.npmjs.org/debug@2.6.9
      depd: registry.npmjs.org/depd@2.0.0
      encodeurl: registry.npmjs.org/encodeurl@1.0.2
      escape-html: registry.npmjs.org/escape-html@1.0.3
      etag: registry.npmjs.org/etag@1.8.1
      finalhandler: registry.npmjs.org/finalhandler@1.2.0
      fresh: registry.npmjs.org/fresh@0.5.2
      http-errors: registry.npmjs.org/http-errors@2.0.0
      merge-descriptors: registry.npmjs.org/merge-descriptors@1.0.1
      methods: registry.npmjs.org/methods@1.1.2
      on-finished: registry.npmjs.org/on-finished@2.4.1
      parseurl: registry.npmjs.org/parseurl@1.3.3
      path-to-regexp: registry.npmjs.org/path-to-regexp@0.1.7
      proxy-addr: registry.npmjs.org/proxy-addr@2.0.7
      qs: registry.npmjs.org/qs@6.11.0
      range-parser: registry.npmjs.org/range-parser@1.2.1
      safe-buffer: registry.npmjs.org/safe-buffer@5.2.1
      send: registry.npmjs.org/send@0.18.0
      serve-static: registry.npmjs.org/serve-static@1.15.0
      setprototypeof: registry.npmjs.org/setprototypeof@1.2.0
      statuses: registry.npmjs.org/statuses@2.0.1
      type-is: registry.npmjs.org/type-is@1.6.18
      utils-merge: registry.npmjs.org/utils-merge@1.0.1
      vary: registry.npmjs.org/vary@1.1.2
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/fast-deep-equal@3.1.3:
    resolution: {integrity: sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz}
    name: fast-deep-equal
    version: 3.1.3
    dev: true

  registry.npmjs.org/fast-glob@3.3.2:
    resolution: {integrity: sha512-oX2ruAFQwf/Orj8m737Y5adxDQO0LAB7/S5MnxCdTNDd4p6BsyIVsv9JQsATbTSq8KHRpLwIHbVlUNatxd+1Ow==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fast-glob/-/fast-glob-3.3.2.tgz}
    name: fast-glob
    version: 3.3.2
    engines: {node: '>=8.6.0'}
    dependencies:
      '@nodelib/fs.stat': registry.npmjs.org/@nodelib/fs.stat@2.0.5
      '@nodelib/fs.walk': registry.npmjs.org/@nodelib/fs.walk@1.2.8
      glob-parent: registry.npmjs.org/glob-parent@5.1.2
      merge2: registry.npmjs.org/merge2@1.4.1
      micromatch: registry.npmjs.org/micromatch@4.0.5
    dev: true

  registry.npmjs.org/fast-json-stable-stringify@2.1.0:
    resolution: {integrity: sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz}
    name: fast-json-stable-stringify
    version: 2.1.0
    dev: true

  registry.npmjs.org/fast-levenshtein@2.0.6:
    resolution: {integrity: sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz}
    name: fast-levenshtein
    version: 2.0.6
    dev: true

  registry.npmjs.org/fastq@1.17.1:
    resolution: {integrity: sha512-sRVD3lWVIXWg6By68ZN7vho9a1pQcN/WBFaAAsDDFzlJjvoGx0P8z7V1t72grFJfJhu3YPZBuu25f7Kaw2jN1w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fastq/-/fastq-1.17.1.tgz}
    name: fastq
    version: 1.17.1
    dependencies:
      reusify: registry.npmjs.org/reusify@1.0.4
    dev: true

  registry.npmjs.org/faye-websocket@0.11.4:
    resolution: {integrity: sha512-CzbClwlXAuiRQAlUyfqPgvPoNKTckTPGfwZV4ZdAhVcP2lh9KUxJg2b5GkE7XbjKQ3YJnQ9z6D9ntLAlB+tP8g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/faye-websocket/-/faye-websocket-0.11.4.tgz}
    name: faye-websocket
    version: 0.11.4
    engines: {node: '>=0.8.0'}
    dependencies:
      websocket-driver: registry.npmjs.org/websocket-driver@0.7.4
    dev: true

  registry.npmjs.org/figures@2.0.0:
    resolution: {integrity: sha512-Oa2M9atig69ZkfwiApY8F2Yy+tzMbazyvqv21R0NsSC8floSOC09BbT1ITWAdoMGQvJ/aZnR1KMwdx9tvHnTNA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/figures/-/figures-2.0.0.tgz}
    name: figures
    version: 2.0.0
    engines: {node: '>=4'}
    dependencies:
      escape-string-regexp: registry.npmjs.org/escape-string-regexp@1.0.5
    dev: true

  registry.npmjs.org/file-entry-cache@6.0.1:
    resolution: {integrity: sha512-7Gps/XWymbLk2QLYK4NzpMOrYjMhdIxXuIvy2QBsLE6ljuodKvdkWs/cpyJJ3CVIVpH0Oi1Hvg1ovbMzLdFBBg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-6.0.1.tgz}
    name: file-entry-cache
    version: 6.0.1
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flat-cache: registry.npmjs.org/flat-cache@3.2.0
    dev: true

  registry.npmjs.org/fill-range@7.0.1:
    resolution: {integrity: sha512-qOo9F+dMUmC2Lcb4BbVvnKJxTPjCm+RRpe4gDuGrzkL7mEVl/djYSu2OdQ2Pa302N4oqkSg9ir6jaLWJ2USVpQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fill-range/-/fill-range-7.0.1.tgz}
    name: fill-range
    version: 7.0.1
    engines: {node: '>=8'}
    dependencies:
      to-regex-range: registry.npmjs.org/to-regex-range@5.0.1
    dev: true

  registry.npmjs.org/finalhandler@1.2.0:
    resolution: {integrity: sha512-5uXcUVftlQMFnWC9qu/svkWv3GTd2PfUhK/3PLkYNAe7FbqJMt3515HaxE6eRL74GdsriiwujiawdaB1BpEISg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/finalhandler/-/finalhandler-1.2.0.tgz}
    name: finalhandler
    version: 1.2.0
    engines: {node: '>= 0.8'}
    dependencies:
      debug: registry.npmjs.org/debug@2.6.9
      encodeurl: registry.npmjs.org/encodeurl@1.0.2
      escape-html: registry.npmjs.org/escape-html@1.0.3
      on-finished: registry.npmjs.org/on-finished@2.4.1
      parseurl: registry.npmjs.org/parseurl@1.3.3
      statuses: registry.npmjs.org/statuses@2.0.1
      unpipe: registry.npmjs.org/unpipe@1.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/find-cache-dir@3.3.2:
    resolution: {integrity: sha512-wXZV5emFEjrridIgED11OoUKLxiYjAcqot/NJdAkOhlJ+vGzwhOAfcG5OX1jP+S0PcjEn8bdMJv+g2jwQ3Onig==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/find-cache-dir/-/find-cache-dir-3.3.2.tgz}
    name: find-cache-dir
    version: 3.3.2
    engines: {node: '>=8'}
    dependencies:
      commondir: registry.npmjs.org/commondir@1.0.1
      make-dir: registry.npmjs.org/make-dir@3.1.0
      pkg-dir: registry.npmjs.org/pkg-dir@4.2.0
    dev: true

  registry.npmjs.org/find-up@4.1.0:
    resolution: {integrity: sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz}
    name: find-up
    version: 4.1.0
    engines: {node: '>=8'}
    dependencies:
      locate-path: registry.npmjs.org/locate-path@5.0.0
      path-exists: registry.npmjs.org/path-exists@4.0.0
    dev: true

  registry.npmjs.org/flat-cache@3.2.0:
    resolution: {integrity: sha512-CYcENa+FtcUKLmhhqyctpclsq7QF38pKjZHsGNiSQF5r4FtoKDWabFDl3hzaEQMvT1LHEysw5twgLvpYYb4vbw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/flat-cache/-/flat-cache-3.2.0.tgz}
    name: flat-cache
    version: 3.2.0
    engines: {node: ^10.12.0 || >=12.0.0}
    dependencies:
      flatted: registry.npmjs.org/flatted@3.3.1
      keyv: registry.npmjs.org/keyv@4.5.4
      rimraf: registry.npmjs.org/rimraf@3.0.2
    dev: true

  registry.npmjs.org/flat@5.0.2:
    resolution: {integrity: sha512-b6suED+5/3rTpUBdG1gupIl8MPFCAMA0QXwmljLhvCUKcUvdE4gWky9zpuGCcXHOsz4J9wPGNWq6OKpmIzz3hQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/flat/-/flat-5.0.2.tgz}
    name: flat
    version: 5.0.2
    hasBin: true
    dev: true

  registry.npmjs.org/flatted@3.3.1:
    resolution: {integrity: sha512-X8cqMLLie7KsNUDSdzeN8FYK9rEt4Dt67OsG/DNGnYTSDBG4uFAJFBnUeiV+zCVAvwFy56IjM9sH51jVaEhNxw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/flatted/-/flatted-3.3.1.tgz}
    name: flatted
    version: 3.3.1
    dev: true

  registry.npmjs.org/follow-redirects@1.15.6(debug@4.3.4):
    resolution: {integrity: sha512-wWN62YITEaOpSK584EZXJafH1AGpO8RVgElfkuXbTOrPX4fIfOyEpW/CsiNd8JdYrAoOvafRTOEnvsO++qCqFA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.6.tgz}
    id: registry.npmjs.org/follow-redirects/1.15.6
    name: follow-redirects
    version: 1.15.6
    engines: {node: '>=4.0'}
    peerDependencies:
      debug: '*'
    peerDependenciesMeta:
      debug:
        optional: true
    dependencies:
      debug: registry.npmjs.org/debug@4.3.4
    dev: true

  registry.npmjs.org/forwarded@0.2.0:
    resolution: {integrity: sha512-buRG0fpBtRHSTCOASe6hD258tEubFoRLb4ZNA6NxMVHNw2gOcwHo9wyablzMzOA5z9xA9L1KNjk/Nt6MT9aYow==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/forwarded/-/forwarded-0.2.0.tgz}
    name: forwarded
    version: 0.2.0
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/fraction.js@4.3.7:
    resolution: {integrity: sha512-ZsDfxO51wGAXREY55a7la9LScWpwv9RxIrYABrlvOFBlH/ShPnrtsXeuUIfXKKOVicNxQ+o8JTbJvjS4M89yew==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fraction.js/-/fraction.js-4.3.7.tgz}
    name: fraction.js
    version: 4.3.7
    dev: true

  registry.npmjs.org/fresh@0.5.2:
    resolution: {integrity: sha512-zJ2mQYM18rEFOudeV4GShTGIQ7RbzA7ozbU9I/XBpm7kqgMywgmylMwXHxZJmkVoYkna9d2pVXVXPdYTP9ej8Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fresh/-/fresh-0.5.2.tgz}
    name: fresh
    version: 0.5.2
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/fs-extra@9.1.0:
    resolution: {integrity: sha512-hcg3ZmepS30/7BSFqRvoo3DOMQu7IjqxO5nCDt+zM9XWjb33Wg7ziNT+Qvqbuc3+gWpzO02JubVyk2G4Zvo1OQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fs-extra/-/fs-extra-9.1.0.tgz}
    name: fs-extra
    version: 9.1.0
    engines: {node: '>=10'}
    dependencies:
      at-least-node: registry.npmjs.org/at-least-node@1.0.0
      graceful-fs: registry.npmjs.org/graceful-fs@4.2.11
      jsonfile: registry.npmjs.org/jsonfile@6.1.0
      universalify: registry.npmjs.org/universalify@2.0.1
    dev: true

  registry.npmjs.org/fs-monkey@1.0.5:
    resolution: {integrity: sha512-8uMbBjrhzW76TYgEV27Y5E//W2f/lTFmx78P2w19FZSxarhI/798APGQyuGCwmkNxgwGRhrLfvWyLBvNtuOmew==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fs-monkey/-/fs-monkey-1.0.5.tgz}
    name: fs-monkey
    version: 1.0.5
    dev: true

  registry.npmjs.org/fs.realpath@1.0.0:
    resolution: {integrity: sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz}
    name: fs.realpath
    version: 1.0.0
    dev: true

  registry.npmjs.org/fsevents@2.3.3:
    resolution: {integrity: sha512-5xoDfX+fL7faATnagmWPpbFtwh/R77WmMMqqHGS65C3vvB0YHrgF+B1YmZ3441tMj5n63k0212XNoJwzlhffQw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/fsevents/-/fsevents-2.3.3.tgz}
    name: fsevents
    version: 2.3.3
    engines: {node: ^8.16.0 || ^10.6.0 || >=11.0.0}
    os: [darwin]
    requiresBuild: true
    dev: true
    optional: true

  registry.npmjs.org/function-bind@1.1.2:
    resolution: {integrity: sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz}
    name: function-bind
    version: 1.1.2
    dev: true

  registry.npmjs.org/functional-red-black-tree@1.0.1:
    resolution: {integrity: sha512-dsKNQNdj6xA3T+QlADDA7mOSlX0qiMINjn0cgr+eGHGsbSHzTabcIogz2+p/iqP1Xs6EP/sS2SbqH+brGTbq0g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/functional-red-black-tree/-/functional-red-black-tree-1.0.1.tgz}
    name: functional-red-black-tree
    version: 1.0.1
    dev: true

  registry.npmjs.org/gensync@1.0.0-beta.2:
    resolution: {integrity: sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz}
    name: gensync
    version: 1.0.0-beta.2
    engines: {node: '>=6.9.0'}
    dev: true

  registry.npmjs.org/get-caller-file@2.0.5:
    resolution: {integrity: sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz}
    name: get-caller-file
    version: 2.0.5
    engines: {node: 6.* || 8.* || >= 10.*}
    dev: true

  registry.npmjs.org/get-intrinsic@1.2.4:
    resolution: {integrity: sha512-5uYhsJH8VJBTv7oslg4BznJYhDoRI6waYCxMmCdnTrcCrHA/fCFKoTFz2JKKE0HdDFUF7/oQuhzumXJK7paBRQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.2.4.tgz}
    name: get-intrinsic
    version: 1.2.4
    engines: {node: '>= 0.4'}
    dependencies:
      es-errors: registry.npmjs.org/es-errors@1.3.0
      function-bind: registry.npmjs.org/function-bind@1.1.2
      has-proto: registry.npmjs.org/has-proto@1.0.3
      has-symbols: registry.npmjs.org/has-symbols@1.0.3
      hasown: registry.npmjs.org/hasown@2.0.2
    dev: true

  registry.npmjs.org/get-stream@3.0.0:
    resolution: {integrity: sha512-GlhdIUuVakc8SJ6kK0zAFbiGzRFzNnY4jUuEbV9UROo4Y+0Ny4fjvcZFVTeDA4odpFyOQzaw6hXukJSq/f28sQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/get-stream/-/get-stream-3.0.0.tgz}
    name: get-stream
    version: 3.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/get-stream@4.1.0:
    resolution: {integrity: sha512-GMat4EJ5161kIy2HevLlr4luNjBgvmj413KaQA7jt4V8B4RDsfpHk7WQ9GVqfYyyx8OS/L66Kox+rJRNklLK7w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/get-stream/-/get-stream-4.1.0.tgz}
    name: get-stream
    version: 4.1.0
    engines: {node: '>=6'}
    dependencies:
      pump: registry.npmjs.org/pump@3.0.0
    dev: true

  registry.npmjs.org/get-stream@6.0.1:
    resolution: {integrity: sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz}
    name: get-stream
    version: 6.0.1
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/glob-parent@5.1.2:
    resolution: {integrity: sha512-AOIgSQCepiJYwP3ARnGx+5VnTu2HBYdzbGP45eLw1vr3zB3vZLeyed1sC9hnbcOc9/SrMyM5RPQrkGz4aS9Zow==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/glob-parent/-/glob-parent-5.1.2.tgz}
    name: glob-parent
    version: 5.1.2
    engines: {node: '>= 6'}
    dependencies:
      is-glob: registry.npmjs.org/is-glob@4.0.3
    dev: true

  registry.npmjs.org/glob-parent@6.0.2:
    resolution: {integrity: sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz}
    name: glob-parent
    version: 6.0.2
    engines: {node: '>=10.13.0'}
    dependencies:
      is-glob: registry.npmjs.org/is-glob@4.0.3
    dev: true

  registry.npmjs.org/glob-to-regexp@0.4.1:
    resolution: {integrity: sha512-lkX1HJXwyMcprw/5YUZc2s7DrpAiHB21/V+E1rHUrVNokkvB6bqMzT0VfV6/86ZNabt1k14YOIaT7nDvOX3Iiw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/glob-to-regexp/-/glob-to-regexp-0.4.1.tgz}
    name: glob-to-regexp
    version: 0.4.1
    dev: true

  registry.npmjs.org/glob@7.2.3:
    resolution: {integrity: sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/glob/-/glob-7.2.3.tgz}
    name: glob
    version: 7.2.3
    dependencies:
      fs.realpath: registry.npmjs.org/fs.realpath@1.0.0
      inflight: registry.npmjs.org/inflight@1.0.6
      inherits: registry.npmjs.org/inherits@2.0.4
      minimatch: registry.npmjs.org/minimatch@3.1.2
      once: registry.npmjs.org/once@1.4.0
      path-is-absolute: registry.npmjs.org/path-is-absolute@1.0.1
    dev: true

  registry.npmjs.org/globals@11.12.0:
    resolution: {integrity: sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/globals/-/globals-11.12.0.tgz}
    name: globals
    version: 11.12.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/globals@13.24.0:
    resolution: {integrity: sha512-AhO5QUcj8llrbG09iWhPU2B204J1xnPeL8kQmVorSsy+Sjj1sk8gIyh6cUocGmH4L0UuhAJy+hJMRA4mgA4mFQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/globals/-/globals-13.24.0.tgz}
    name: globals
    version: 13.24.0
    engines: {node: '>=8'}
    dependencies:
      type-fest: registry.npmjs.org/type-fest@0.20.2
    dev: true

  registry.npmjs.org/globby@11.1.0:
    resolution: {integrity: sha512-jhIXaOzy1sb8IyocaruWSn1TjmnBVs8Ayhcy83rmxNJ8q2uWKCAj3CnJY+KpGSXCueAPc0i05kVvVKtP1t9S3g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/globby/-/globby-11.1.0.tgz}
    name: globby
    version: 11.1.0
    engines: {node: '>=10'}
    dependencies:
      array-union: registry.npmjs.org/array-union@2.1.0
      dir-glob: registry.npmjs.org/dir-glob@3.0.1
      fast-glob: registry.npmjs.org/fast-glob@3.3.2
      ignore: registry.npmjs.org/ignore@5.3.1
      merge2: registry.npmjs.org/merge2@1.4.1
      slash: registry.npmjs.org/slash@3.0.0
    dev: true

  registry.npmjs.org/gopd@1.0.1:
    resolution: {integrity: sha512-d65bNlIadxvpb/A2abVdlqKqV563juRnZ1Wtk6s1sIR8uNsXR70xqIzVqxVf1eTqDunwT2MkczEeaezCKTZhwA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/gopd/-/gopd-1.0.1.tgz}
    name: gopd
    version: 1.0.1
    dependencies:
      get-intrinsic: registry.npmjs.org/get-intrinsic@1.2.4
    dev: true

  registry.npmjs.org/graceful-fs@4.2.11:
    resolution: {integrity: sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz}
    name: graceful-fs
    version: 4.2.11
    dev: true

  registry.npmjs.org/gzip-size@6.0.0:
    resolution: {integrity: sha512-ax7ZYomf6jqPTQ4+XCpUGyXKHk5WweS+e05MBO4/y3WJ5RkmPXNKvX+bx1behVILVwr6JSQvZAku021CHPXG3Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/gzip-size/-/gzip-size-6.0.0.tgz}
    name: gzip-size
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      duplexer: registry.npmjs.org/duplexer@0.1.2
    dev: true

  registry.npmjs.org/handle-thing@2.0.1:
    resolution: {integrity: sha512-9Qn4yBxelxoh2Ow62nP+Ka/kMnOXRi8BXnRaUwezLNhqelnN49xKz4F/dPP8OYLxLxq6JDtZb2i9XznUQbNPTg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/handle-thing/-/handle-thing-2.0.1.tgz}
    name: handle-thing
    version: 2.0.1
    dev: true

  registry.npmjs.org/has-flag@3.0.0:
    resolution: {integrity: sha512-sKJf1+ceQBr4SMkvQnBDNDtf4TXpVhVGateu0t918bl30FnbE2m4vNLX+VWe/dpjlb+HugGYzW7uQXH98HPEYw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/has-flag/-/has-flag-3.0.0.tgz}
    name: has-flag
    version: 3.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/has-flag@4.0.0:
    resolution: {integrity: sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz}
    name: has-flag
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/has-property-descriptors@1.0.2:
    resolution: {integrity: sha512-55JNKuIW+vq4Ke1BjOTjM2YctQIvCT7GFzHwmfZPGo5wnrgkid0YQtnAleFSqumZm4az3n2BS+erby5ipJdgrg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/has-property-descriptors/-/has-property-descriptors-1.0.2.tgz}
    name: has-property-descriptors
    version: 1.0.2
    dependencies:
      es-define-property: registry.npmjs.org/es-define-property@1.0.0
    dev: true

  registry.npmjs.org/has-proto@1.0.3:
    resolution: {integrity: sha512-SJ1amZAJUiZS+PhsVLf5tGydlaVB8EdFpaSO4gmiUKUOxk8qzn5AIy4ZeJUmh22znIdk/uMAUT2pl3FxzVUH+Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/has-proto/-/has-proto-1.0.3.tgz}
    name: has-proto
    version: 1.0.3
    engines: {node: '>= 0.4'}
    dev: true

  registry.npmjs.org/has-symbols@1.0.3:
    resolution: {integrity: sha512-l3LCuF6MgDNwTDKkdYGEihYjt5pRPbEg46rtlmnSPlUbgmB8LOIrKJbYYFBSbnPaJexMKtiPO8hmeRjRz2Td+A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/has-symbols/-/has-symbols-1.0.3.tgz}
    name: has-symbols
    version: 1.0.3
    engines: {node: '>= 0.4'}
    dev: true

  registry.npmjs.org/hash-sum@1.0.2:
    resolution: {integrity: sha512-fUs4B4L+mlt8/XAtSOGMUO1TXmAelItBPtJG7CyHJfYTdDjwisntGO2JQz7oUsatOY9o68+57eziUVNw/mRHmA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/hash-sum/-/hash-sum-1.0.2.tgz}
    name: hash-sum
    version: 1.0.2
    dev: true

  registry.npmjs.org/hash-sum@2.0.0:
    resolution: {integrity: sha512-WdZTbAByD+pHfl/g9QSsBIIwy8IT+EsPiKDs0KNX+zSHhdDLFKdZu0BQHljvO+0QI/BasbMSUa8wYNCZTvhslg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/hash-sum/-/hash-sum-2.0.0.tgz}
    name: hash-sum
    version: 2.0.0
    dev: true

  registry.npmjs.org/hasown@2.0.2:
    resolution: {integrity: sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz}
    name: hasown
    version: 2.0.2
    engines: {node: '>= 0.4'}
    dependencies:
      function-bind: registry.npmjs.org/function-bind@1.1.2
    dev: true

  registry.npmjs.org/he@1.2.0:
    resolution: {integrity: sha512-F/1DnUGPopORZi0ni+CvrCgHQ5FyEAHRLSApuYWMmrbSwoN2Mn/7k+Gl38gJnR7yyDZk6WLXwiGod1JOWNDKGw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/he/-/he-1.2.0.tgz}
    name: he
    version: 1.2.0
    hasBin: true
    dev: true

  registry.npmjs.org/highlight.js@10.7.3:
    resolution: {integrity: sha512-tzcUFauisWKNHaRkN4Wjl/ZA07gENAjFl3J/c480dprkGTg5EQstgaNFqBfUqCq54kZRIEcreTsAgF/m2quD7A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/highlight.js/-/highlight.js-10.7.3.tgz}
    name: highlight.js
    version: 10.7.3
    dev: true

  registry.npmjs.org/hosted-git-info@2.8.9:
    resolution: {integrity: sha512-mxIDAb9Lsm6DoOJ7xH+5+X4y1LU/4Hi50L9C5sIswK3JzULS4bwk1FvjdBgvYR4bzT4tuUQiC15FE2f5HbLvYw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/hosted-git-info/-/hosted-git-info-2.8.9.tgz}
    name: hosted-git-info
    version: 2.8.9
    dev: true

  registry.npmjs.org/hpack.js@2.1.6:
    resolution: {integrity: sha512-zJxVehUdMGIKsRaNt7apO2Gqp0BdqW5yaiGHXXmbpvxgBYVZnAql+BJb4RO5ad2MgpbZKn5G6nMnegrH1FcNYQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/hpack.js/-/hpack.js-2.1.6.tgz}
    name: hpack.js
    version: 2.1.6
    dependencies:
      inherits: registry.npmjs.org/inherits@2.0.4
      obuf: registry.npmjs.org/obuf@1.1.2
      readable-stream: registry.npmjs.org/readable-stream@2.3.8
      wbuf: registry.npmjs.org/wbuf@1.7.3
    dev: true

  registry.npmjs.org/html-entities@2.5.2:
    resolution: {integrity: sha512-K//PSRMQk4FZ78Kyau+mZurHn3FH0Vwr+H36eE0rPbeYkRRi9YxceYPhuN60UwWorxyKHhqoAJl2OFKa4BVtaA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/html-entities/-/html-entities-2.5.2.tgz}
    name: html-entities
    version: 2.5.2
    dev: true

  registry.npmjs.org/html-escaper@2.0.2:
    resolution: {integrity: sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz}
    name: html-escaper
    version: 2.0.2
    dev: true

  registry.npmjs.org/html-minifier-terser@6.1.0:
    resolution: {integrity: sha512-YXxSlJBZTP7RS3tWnQw74ooKa6L9b9i9QYXY21eUEvhZ3u9XLfv6OnFsQq6RxkhHygsaUMvYsZRV5rU/OVNZxw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/html-minifier-terser/-/html-minifier-terser-6.1.0.tgz}
    name: html-minifier-terser
    version: 6.1.0
    engines: {node: '>=12'}
    hasBin: true
    dependencies:
      camel-case: registry.npmjs.org/camel-case@4.1.2
      clean-css: registry.npmjs.org/clean-css@5.3.3
      commander: registry.npmjs.org/commander@8.3.0
      he: registry.npmjs.org/he@1.2.0
      param-case: registry.npmjs.org/param-case@3.0.4
      relateurl: registry.npmjs.org/relateurl@0.2.7
      terser: registry.npmjs.org/terser@5.30.3
    dev: true

  registry.npmjs.org/html-tags@2.0.0:
    resolution: {integrity: sha512-+Il6N8cCo2wB/Vd3gqy/8TZhTD3QvcVeQLCnZiGkGCH3JP28IgGAY41giccp2W4R3jfyJPAP318FQTa1yU7K7g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/html-tags/-/html-tags-2.0.0.tgz}
    name: html-tags
    version: 2.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/html-tags@3.3.1:
    resolution: {integrity: sha512-ztqyC3kLto0e9WbNp0aeP+M3kTt+nbaIveGmUxAtZa+8iFgKLUOD4YKM5j+f3QD89bra7UeumolZHKuOXnTmeQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/html-tags/-/html-tags-3.3.1.tgz}
    name: html-tags
    version: 3.3.1
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/html-webpack-plugin@5.6.0(webpack@5.91.0):
    resolution: {integrity: sha512-iwaY4wzbe48AfKLZ/Cc8k0L+FKG6oSNRaZ8x5A/T/IVDGyXcbHncM9TdDa93wn0FsSm82FhTKW7f3vS61thXAw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/html-webpack-plugin/-/html-webpack-plugin-5.6.0.tgz}
    id: registry.npmjs.org/html-webpack-plugin/5.6.0
    name: html-webpack-plugin
    version: 5.6.0
    engines: {node: '>=10.13.0'}
    peerDependencies:
      '@rspack/core': 0.x || 1.x
      webpack: ^5.20.0
    peerDependenciesMeta:
      '@rspack/core':
        optional: true
      webpack:
        optional: true
    dependencies:
      '@types/html-minifier-terser': registry.npmjs.org/@types/html-minifier-terser@6.1.0
      html-minifier-terser: registry.npmjs.org/html-minifier-terser@6.1.0
      lodash: registry.npmjs.org/lodash@4.17.21
      pretty-error: registry.npmjs.org/pretty-error@4.0.0
      tapable: registry.npmjs.org/tapable@2.2.1
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/htmlparser2@6.1.0:
    resolution: {integrity: sha512-gyyPk6rgonLFEDGoeRgQNaEUvdJ4ktTmmUh/h2t7s+M8oPpIPxgNACWa+6ESR57kXstwqPiCut0V8NRpcwgU7A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/htmlparser2/-/htmlparser2-6.1.0.tgz}
    name: htmlparser2
    version: 6.1.0
    dependencies:
      domelementtype: registry.npmjs.org/domelementtype@2.3.0
      domhandler: registry.npmjs.org/domhandler@4.3.1
      domutils: registry.npmjs.org/domutils@2.8.0
      entities: registry.npmjs.org/entities@2.2.0
    dev: true

  registry.npmjs.org/http-deceiver@1.2.7:
    resolution: {integrity: sha512-LmpOGxTfbpgtGVxJrj5k7asXHCgNZp5nLfp+hWc8QQRqtb7fUy6kRY3BO1h9ddF6yIPYUARgxGOwB42DnxIaNw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/http-deceiver/-/http-deceiver-1.2.7.tgz}
    name: http-deceiver
    version: 1.2.7
    dev: true

  registry.npmjs.org/http-errors@1.6.3:
    resolution: {integrity: sha512-lks+lVC8dgGyh97jxvxeYTWQFvh4uw4yC12gVl63Cg30sjPX4wuGcdkICVXDAESr6OJGjqGA8Iz5mkeN6zlD7A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/http-errors/-/http-errors-1.6.3.tgz}
    name: http-errors
    version: 1.6.3
    engines: {node: '>= 0.6'}
    dependencies:
      depd: registry.npmjs.org/depd@1.1.2
      inherits: registry.npmjs.org/inherits@2.0.3
      setprototypeof: registry.npmjs.org/setprototypeof@1.1.0
      statuses: registry.npmjs.org/statuses@1.5.0
    dev: true

  registry.npmjs.org/http-errors@2.0.0:
    resolution: {integrity: sha512-FtwrG/euBzaEjYeRqOgly7G0qviiXoJWnvEH2Z1plBdXgbyjv34pHTSb9zoeHMyDy33+DWy5Wt9Wo+TURtOYSQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/http-errors/-/http-errors-2.0.0.tgz}
    name: http-errors
    version: 2.0.0
    engines: {node: '>= 0.8'}
    dependencies:
      depd: registry.npmjs.org/depd@2.0.0
      inherits: registry.npmjs.org/inherits@2.0.4
      setprototypeof: registry.npmjs.org/setprototypeof@1.2.0
      statuses: registry.npmjs.org/statuses@2.0.1
      toidentifier: registry.npmjs.org/toidentifier@1.0.1
    dev: true

  registry.npmjs.org/http-parser-js@0.5.8:
    resolution: {integrity: sha512-SGeBX54F94Wgu5RH3X5jsDtf4eHyRogWX1XGT3b4HuW3tQPM4AaBzoUji/4AAJNXCEOWZ5O0DgZmJw1947gD5Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/http-parser-js/-/http-parser-js-0.5.8.tgz}
    name: http-parser-js
    version: 0.5.8
    dev: true

  registry.npmjs.org/http-proxy-middleware@2.0.6(@types/express@4.17.21)(debug@4.3.4):
    resolution: {integrity: sha512-ya/UeJ6HVBYxrgYotAZo1KvPWlgB48kUJLDePFeneHsVujFaW5WNj2NgWCAE//B1Dl02BIfYlpNgBy8Kf8Rjmw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/http-proxy-middleware/-/http-proxy-middleware-2.0.6.tgz}
    id: registry.npmjs.org/http-proxy-middleware/2.0.6
    name: http-proxy-middleware
    version: 2.0.6
    engines: {node: '>=12.0.0'}
    peerDependencies:
      '@types/express': ^4.17.13
    peerDependenciesMeta:
      '@types/express':
        optional: true
    dependencies:
      '@types/express': registry.npmjs.org/@types/express@4.17.21
      '@types/http-proxy': registry.npmjs.org/@types/http-proxy@1.17.14
      http-proxy: registry.npmjs.org/http-proxy@1.18.1(debug@4.3.4)
      is-glob: registry.npmjs.org/is-glob@4.0.3
      is-plain-obj: registry.npmjs.org/is-plain-obj@3.0.0
      micromatch: registry.npmjs.org/micromatch@4.0.5
    transitivePeerDependencies:
      - debug
    dev: true

  registry.npmjs.org/http-proxy@1.18.1(debug@4.3.4):
    resolution: {integrity: sha512-7mz/721AbnJwIVbnaSv1Cz3Am0ZLT/UBwkC92VlxhXv/k/BBQfM2fXElQNC27BVGr0uwUpplYPQM9LnaBMR5NQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/http-proxy/-/http-proxy-1.18.1.tgz}
    id: registry.npmjs.org/http-proxy/1.18.1
    name: http-proxy
    version: 1.18.1
    engines: {node: '>=8.0.0'}
    dependencies:
      eventemitter3: registry.npmjs.org/eventemitter3@4.0.7
      follow-redirects: registry.npmjs.org/follow-redirects@1.15.6(debug@4.3.4)
      requires-port: registry.npmjs.org/requires-port@1.0.0
    transitivePeerDependencies:
      - debug
    dev: true

  registry.npmjs.org/human-signals@2.1.0:
    resolution: {integrity: sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz}
    name: human-signals
    version: 2.1.0
    engines: {node: '>=10.17.0'}
    dev: true

  registry.npmjs.org/iconv-lite@0.4.24:
    resolution: {integrity: sha512-v3MXnZAcvnywkTUEZomIActle7RXXeedOR31wwl7VlyoXO4Qi9arvSenNQWne1TcRwhCL1HwLI21bEqdpj8/rA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/iconv-lite/-/iconv-lite-0.4.24.tgz}
    name: iconv-lite
    version: 0.4.24
    engines: {node: '>=0.10.0'}
    dependencies:
      safer-buffer: registry.npmjs.org/safer-buffer@2.1.2
    dev: true

  registry.npmjs.org/icss-utils@5.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-soFhflCVWLfRNOPU3iv5Z9VUdT44xFRbzjLsEzSr5AQmgqPMTHdU3PMT1Cf1ssx8fLNJDA1juftYl+PUcv3MqA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/icss-utils/-/icss-utils-5.1.0.tgz}
    id: registry.npmjs.org/icss-utils/5.1.0
    name: icss-utils
    version: 5.1.0
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
    dev: true

  registry.npmjs.org/ieee754@1.2.1:
    resolution: {integrity: sha512-dcyqhDvX1C46lXZcVqCpK+FtMRQVdIMN6/Df5js2zouUsqG7I6sFxitIC+7KYK29KdXOLHdu9zL4sFnoVQnqaA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ieee754/-/ieee754-1.2.1.tgz}
    name: ieee754
    version: 1.2.1
    dev: true

  registry.npmjs.org/ignore@4.0.6:
    resolution: {integrity: sha512-cyFDKrqc/YdcWFniJhzI42+AzS+gNwmUzOSFcRCQYwySuBBBy/KjuxWLZ/FHEH6Moq1NizMOBWyTcv8O4OZIMg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ignore/-/ignore-4.0.6.tgz}
    name: ignore
    version: 4.0.6
    engines: {node: '>= 4'}
    dev: true

  registry.npmjs.org/ignore@5.3.1:
    resolution: {integrity: sha512-5Fytz/IraMjqpwfd34ke28PTVMjZjJG2MPn5t7OE4eUCUNf8BAa7b5WUS9/Qvr6mwOQS7Mk6vdsMno5he+T8Xw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ignore/-/ignore-5.3.1.tgz}
    name: ignore
    version: 5.3.1
    engines: {node: '>= 4'}
    dev: true

  registry.npmjs.org/import-fresh@3.3.0:
    resolution: {integrity: sha512-veYYhQa+D1QBKznvhUHxb8faxlrwUnxseDAbAp457E0wLNio2bOSKnjYDhMj+YiAq61xrMGhQk9iXVk5FzgQMw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.0.tgz}
    name: import-fresh
    version: 3.3.0
    engines: {node: '>=6'}
    dependencies:
      parent-module: registry.npmjs.org/parent-module@1.0.1
      resolve-from: registry.npmjs.org/resolve-from@4.0.0
    dev: true

  registry.npmjs.org/imurmurhash@0.1.4:
    resolution: {integrity: sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz}
    name: imurmurhash
    version: 0.1.4
    engines: {node: '>=0.8.19'}
    dev: true

  registry.npmjs.org/inflight@1.0.6:
    resolution: {integrity: sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz}
    name: inflight
    version: 1.0.6
    dependencies:
      once: registry.npmjs.org/once@1.4.0
      wrappy: registry.npmjs.org/wrappy@1.0.2
    dev: true

  registry.npmjs.org/inherits@2.0.3:
    resolution: {integrity: sha512-x00IRNXNy63jwGkJmzPigoySHbaqpNuzKbBOmzK+g2OdZpQ9w+sxCN+VSB3ja7IAge2OP2qpfxTjeNcyjmW1uw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/inherits/-/inherits-2.0.3.tgz}
    name: inherits
    version: 2.0.3
    dev: true

  registry.npmjs.org/inherits@2.0.4:
    resolution: {integrity: sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz}
    name: inherits
    version: 2.0.4
    dev: true

  registry.npmjs.org/ipaddr.js@1.9.1:
    resolution: {integrity: sha512-0KI/607xoxSToH7GjN1FfSbLoU0+btTicjsQSWQlh/hZykN8KpmMf7uYwPW3R+akZ6R/w18ZlXSHBYXiYUPO3g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-1.9.1.tgz}
    name: ipaddr.js
    version: 1.9.1
    engines: {node: '>= 0.10'}
    dev: true

  registry.npmjs.org/ipaddr.js@2.1.0:
    resolution: {integrity: sha512-LlbxQ7xKzfBusov6UMi4MFpEg0m+mAm9xyNGEduwXMEDuf4WfzB/RZwMVYEd7IKGvh4IUkEXYxtAVu9T3OelJQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ipaddr.js/-/ipaddr.js-2.1.0.tgz}
    name: ipaddr.js
    version: 2.1.0
    engines: {node: '>= 10'}
    dev: true

  registry.npmjs.org/is-arrayish@0.2.1:
    resolution: {integrity: sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz}
    name: is-arrayish
    version: 0.2.1
    dev: true

  registry.npmjs.org/is-binary-path@2.1.0:
    resolution: {integrity: sha512-ZMERYes6pDydyuGidse7OsHxtbI7WVeUEozgR/g7rd0xUimYNlvZRE/K2MgZTjWy725IfelLeVcEM97mmtRGXw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-binary-path/-/is-binary-path-2.1.0.tgz}
    name: is-binary-path
    version: 2.1.0
    engines: {node: '>=8'}
    dependencies:
      binary-extensions: registry.npmjs.org/binary-extensions@2.3.0
    dev: true

  registry.npmjs.org/is-ci@1.2.1:
    resolution: {integrity: sha512-s6tfsaQaQi3JNciBH6shVqEDvhGut0SUXr31ag8Pd8BBbVVlcGfWhpPmEOoM6RJ5TFhbypvf5yyRw/VXW1IiWg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-ci/-/is-ci-1.2.1.tgz}
    name: is-ci
    version: 1.2.1
    hasBin: true
    dependencies:
      ci-info: registry.npmjs.org/ci-info@1.6.0
    dev: true

  registry.npmjs.org/is-core-module@2.13.1:
    resolution: {integrity: sha512-hHrIjvZsftOsvKSn2TRYl63zvxsgE0K+0mYMoH6gD4omR5IWB2KynivBQczo3+wF1cCkjzvptnI9Q0sPU66ilw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-core-module/-/is-core-module-2.13.1.tgz}
    name: is-core-module
    version: 2.13.1
    dependencies:
      hasown: registry.npmjs.org/hasown@2.0.2
    dev: true

  registry.npmjs.org/is-docker@2.2.1:
    resolution: {integrity: sha512-F+i2BKsFrH66iaUFc0woD8sLy8getkwTwtOBjvs56Cx4CgJDeKQeqfz8wAYiSb8JOprWhHH5p77PbmYCvvUuXQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-docker/-/is-docker-2.2.1.tgz}
    name: is-docker
    version: 2.2.1
    engines: {node: '>=8'}
    hasBin: true
    dev: true

  registry.npmjs.org/is-extglob@2.1.1:
    resolution: {integrity: sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz}
    name: is-extglob
    version: 2.1.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/is-file-esm@1.0.0:
    resolution: {integrity: sha512-rZlaNKb4Mr8WlRu2A9XdeoKgnO5aA53XdPHgCKVyCrQ/rWi89RET1+bq37Ru46obaQXeiX4vmFIm1vks41hoSA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-file-esm/-/is-file-esm-1.0.0.tgz}
    name: is-file-esm
    version: 1.0.0
    dependencies:
      read-pkg-up: registry.npmjs.org/read-pkg-up@7.0.1
    dev: true

  registry.npmjs.org/is-fullwidth-code-point@2.0.0:
    resolution: {integrity: sha512-VHskAKYM8RfSFXwee5t5cbN5PZeq1Wrh6qd5bkyiXIf6UQcN6w/A0eXM9r6t8d+GYOh+o6ZhiEnb88LN/Y8m2w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-2.0.0.tgz}
    name: is-fullwidth-code-point
    version: 2.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/is-fullwidth-code-point@3.0.0:
    resolution: {integrity: sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz}
    name: is-fullwidth-code-point
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/is-glob@4.0.3:
    resolution: {integrity: sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz}
    name: is-glob
    version: 4.0.3
    engines: {node: '>=0.10.0'}
    dependencies:
      is-extglob: registry.npmjs.org/is-extglob@2.1.1
    dev: true

  registry.npmjs.org/is-interactive@1.0.0:
    resolution: {integrity: sha512-2HvIEKRoqS62guEC+qBjpvRubdX910WCMuJTZ+I9yvqKU2/12eSL549HMwtabb4oupdj2sMP50k+XJfB/8JE6w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-interactive/-/is-interactive-1.0.0.tgz}
    name: is-interactive
    version: 1.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/is-number@7.0.0:
    resolution: {integrity: sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz}
    name: is-number
    version: 7.0.0
    engines: {node: '>=0.12.0'}
    dev: true

  registry.npmjs.org/is-plain-obj@3.0.0:
    resolution: {integrity: sha512-gwsOE28k+23GP1B6vFl1oVh/WOzmawBrKwo5Ev6wMKzPkaXaCDIQKzLnvsA42DRlbVTWorkgTKIviAKCWkfUwA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-plain-obj/-/is-plain-obj-3.0.0.tgz}
    name: is-plain-obj
    version: 3.0.0
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/is-plain-object@2.0.4:
    resolution: {integrity: sha512-h5PpgXkWitc38BBMYawTYMWJHFZJVnBquFE57xFpjB8pJFiF6gZ+bU+WyI/yqXiFR5mdLsgYNaPe8uao6Uv9Og==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-plain-object/-/is-plain-object-2.0.4.tgz}
    name: is-plain-object
    version: 2.0.4
    engines: {node: '>=0.10.0'}
    dependencies:
      isobject: registry.npmjs.org/isobject@3.0.1
    dev: true

  registry.npmjs.org/is-stream@1.1.0:
    resolution: {integrity: sha512-uQPm8kcs47jx38atAcWTVxyltQYoPT68y9aWYdV6yWXSyW8mzSat0TL6CiWdZeCdF3KrAvpVtnHbTv4RN+rqdQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-stream/-/is-stream-1.1.0.tgz}
    name: is-stream
    version: 1.1.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/is-stream@2.0.1:
    resolution: {integrity: sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz}
    name: is-stream
    version: 2.0.1
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/is-unicode-supported@0.1.0:
    resolution: {integrity: sha512-knxG2q4UC3u8stRGyAVJCOdxFmv5DZiRcdlIaAQXAbSfJya+OhopNotLQrstBhququ4ZpuKbDc/8S6mgXgPFPw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-unicode-supported/-/is-unicode-supported-0.1.0.tgz}
    name: is-unicode-supported
    version: 0.1.0
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/is-wsl@2.2.0:
    resolution: {integrity: sha512-fKzAra0rGJUUBwGBgNkHZuToZcn+TtXHpeCgmkMJMMYx1sQDYaCSyjJBSCa2nH1DGm7s3n1oBnohoVTBaN7Lww==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/is-wsl/-/is-wsl-2.2.0.tgz}
    name: is-wsl
    version: 2.2.0
    engines: {node: '>=8'}
    dependencies:
      is-docker: registry.npmjs.org/is-docker@2.2.1
    dev: true

  registry.npmjs.org/isarray@1.0.0:
    resolution: {integrity: sha512-VLghIWNM6ELQzo7zwmcg0NmTVyWKYjvIeM83yjp0wRDTmUnrM678fQbcKBo6n2CJEF0szoG//ytg+TKla89ALQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/isarray/-/isarray-1.0.0.tgz}
    name: isarray
    version: 1.0.0
    dev: true

  registry.npmjs.org/isexe@2.0.0:
    resolution: {integrity: sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz}
    name: isexe
    version: 2.0.0
    dev: true

  registry.npmjs.org/isobject@3.0.1:
    resolution: {integrity: sha512-WhB9zCku7EGTj/HQQRz5aUQEUeoQZH2bWcltRErOpymJ4boYE6wL9Tbr23krRPSZ+C5zqNSrSw+Cc7sZZ4b7vg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/isobject/-/isobject-3.0.1.tgz}
    name: isobject
    version: 3.0.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/javascript-stringify@2.1.0:
    resolution: {integrity: sha512-JVAfqNPTvNq3sB/VHQJAFxN/sPgKnsKrCwyRt15zwNCdrMMJDdcEOdubuy+DuJYYdm0ox1J4uzEuYKkN+9yhVg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/javascript-stringify/-/javascript-stringify-2.1.0.tgz}
    name: javascript-stringify
    version: 2.1.0
    dev: true

  registry.npmjs.org/jest-worker@27.5.1:
    resolution: {integrity: sha512-7vuh85V5cdDofPyxn58nrPjBktZo0u9x1g8WtjQol+jZDaE+fhN+cIvTj11GndBnMnyfrUOG1sZQxCdjKh+DKg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/jest-worker/-/jest-worker-27.5.1.tgz}
    name: jest-worker
    version: 27.5.1
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/node': registry.npmjs.org/@types/node@20.12.7
      merge-stream: registry.npmjs.org/merge-stream@2.0.0
      supports-color: registry.npmjs.org/supports-color@8.1.1
    dev: true

  registry.npmjs.org/jest-worker@28.1.3:
    resolution: {integrity: sha512-CqRA220YV/6jCo8VWvAt1KKx6eek1VIHMPeLEbpcfSfkEeWyBNppynM/o6q+Wmw+sOhos2ml34wZbSX3G13//g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/jest-worker/-/jest-worker-28.1.3.tgz}
    name: jest-worker
    version: 28.1.3
    engines: {node: ^12.13.0 || ^14.15.0 || ^16.10.0 || >=17.0.0}
    dependencies:
      '@types/node': registry.npmjs.org/@types/node@20.12.7
      merge-stream: registry.npmjs.org/merge-stream@2.0.0
      supports-color: registry.npmjs.org/supports-color@8.1.1
    dev: true

  registry.npmjs.org/joi@17.12.3:
    resolution: {integrity: sha512-2RRziagf555owrm9IRVtdKynOBeITiDpuZqIpgwqXShPncPKNiRQoiGsl/T8SQdq+8ugRzH2LqY67irr2y/d+g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/joi/-/joi-17.12.3.tgz}
    name: joi
    version: 17.12.3
    dependencies:
      '@hapi/hoek': registry.npmjs.org/@hapi/hoek@9.3.0
      '@hapi/topo': registry.npmjs.org/@hapi/topo@5.1.0
      '@sideway/address': registry.npmjs.org/@sideway/address@4.1.5
      '@sideway/formula': registry.npmjs.org/@sideway/formula@3.0.1
      '@sideway/pinpoint': registry.npmjs.org/@sideway/pinpoint@2.0.0
    dev: true

  registry.npmjs.org/js-message@1.0.7:
    resolution: {integrity: sha512-efJLHhLjIyKRewNS9EGZ4UpI8NguuL6fKkhRxVuMmrGV2xN/0APGdQYwLFky5w9naebSZ0OwAGp0G6/2Cg90rA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/js-message/-/js-message-1.0.7.tgz}
    name: js-message
    version: 1.0.7
    engines: {node: '>=0.6.0'}
    dev: true

  registry.npmjs.org/js-tokens@4.0.0:
    resolution: {integrity: sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz}
    name: js-tokens
    version: 4.0.0
    dev: true

  registry.npmjs.org/js-yaml@3.14.1:
    resolution: {integrity: sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz}
    name: js-yaml
    version: 3.14.1
    hasBin: true
    dependencies:
      argparse: registry.npmjs.org/argparse@1.0.10
      esprima: registry.npmjs.org/esprima@4.0.1
    dev: true

  registry.npmjs.org/jsesc@0.5.0:
    resolution: {integrity: sha512-uZz5UnB7u4T9LvwmFqXii7pZSouaRPorGs5who1Ip7VO0wxanFvBL7GkM6dTHlgX+jhBApRetaWpnDabOeTcnA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/jsesc/-/jsesc-0.5.0.tgz}
    name: jsesc
    version: 0.5.0
    hasBin: true
    dev: true

  registry.npmjs.org/jsesc@2.5.2:
    resolution: {integrity: sha512-OYu7XEzjkCQ3C5Ps3QIZsQfNpqoJyZZA99wd9aWd05NCtC5pWOkShK2mkL6HXQR6/Cy2lbNdPlZBpuQHXE63gA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/jsesc/-/jsesc-2.5.2.tgz}
    name: jsesc
    version: 2.5.2
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  registry.npmjs.org/json-buffer@3.0.1:
    resolution: {integrity: sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz}
    name: json-buffer
    version: 3.0.1
    dev: true

  registry.npmjs.org/json-parse-better-errors@1.0.2:
    resolution: {integrity: sha512-mrqyZKfX5EhL7hvqcV6WG1yYjnjeuYDzDhhcAAUrq8Po85NBQBJP+ZDUT75qZQ98IkUoBqdkExkukOU7Ts2wrw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/json-parse-better-errors/-/json-parse-better-errors-1.0.2.tgz}
    name: json-parse-better-errors
    version: 1.0.2
    dev: true

  registry.npmjs.org/json-parse-even-better-errors@2.3.1:
    resolution: {integrity: sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz}
    name: json-parse-even-better-errors
    version: 2.3.1
    dev: true

  registry.npmjs.org/json-schema-traverse@0.4.1:
    resolution: {integrity: sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz}
    name: json-schema-traverse
    version: 0.4.1
    dev: true

  registry.npmjs.org/json-schema-traverse@1.0.0:
    resolution: {integrity: sha512-NM8/P9n3XjXhIZn1lLhkFaACTOURQXjWhV4BA/RnOv8xvgqtqpAX9IO4mRQxSx1Rlo4tqzeqb0sOlruaOy3dug==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-1.0.0.tgz}
    name: json-schema-traverse
    version: 1.0.0
    dev: true

  registry.npmjs.org/json-stable-stringify-without-jsonify@1.0.1:
    resolution: {integrity: sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz}
    name: json-stable-stringify-without-jsonify
    version: 1.0.1
    dev: true

  registry.npmjs.org/json5@1.0.2:
    resolution: {integrity: sha512-g1MWMLBiz8FKi1e4w0UyVL3w+iJceWAFBAaBnnGKOpNa5f8TLktkbre1+s6oICydWAm+HRUGTmI+//xv2hvXYA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/json5/-/json5-1.0.2.tgz}
    name: json5
    version: 1.0.2
    hasBin: true
    dependencies:
      minimist: registry.npmjs.org/minimist@1.2.8
    dev: true

  registry.npmjs.org/json5@2.2.3:
    resolution: {integrity: sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/json5/-/json5-2.2.3.tgz}
    name: json5
    version: 2.2.3
    engines: {node: '>=6'}
    hasBin: true
    dev: true

  registry.npmjs.org/jsonfile@6.1.0:
    resolution: {integrity: sha512-5dgndWOriYSm5cnYaJNhalLNDKOqFwyDB/rr1E9ZsGciGvKPs8R2xYGCacuf3z6K1YKDz182fd+fY3cn3pMqXQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/jsonfile/-/jsonfile-6.1.0.tgz}
    name: jsonfile
    version: 6.1.0
    dependencies:
      universalify: registry.npmjs.org/universalify@2.0.1
    optionalDependencies:
      graceful-fs: registry.npmjs.org/graceful-fs@4.2.11
    dev: true

  registry.npmjs.org/keyv@4.5.4:
    resolution: {integrity: sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz}
    name: keyv
    version: 4.5.4
    dependencies:
      json-buffer: registry.npmjs.org/json-buffer@3.0.1
    dev: true

  registry.npmjs.org/kind-of@6.0.3:
    resolution: {integrity: sha512-dcS1ul+9tmeD95T+x28/ehLgd9mENa3LsvDTtzm3vyBEO7RPptvAD+t44WVXaUjTBRcrpFeFlC8WCruUR456hw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/kind-of/-/kind-of-6.0.3.tgz}
    name: kind-of
    version: 6.0.3
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/klona@2.0.6:
    resolution: {integrity: sha512-dhG34DXATL5hSxJbIexCft8FChFXtmskoZYnoPWjXQuebWYCNkVeV3KkGegCK9CP1oswI/vQibS2GY7Em/sJJA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/klona/-/klona-2.0.6.tgz}
    name: klona
    version: 2.0.6
    engines: {node: '>= 8'}
    dev: true

  registry.npmjs.org/launch-editor-middleware@2.6.1:
    resolution: {integrity: sha512-Fg/xYhf7ARmRp40n18wIfJyuAMEjXo67Yull7uF7d0OJ3qA4EYJISt1XfPPn69IIJ5jKgQwzcg6DqHYo95LL/g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/launch-editor-middleware/-/launch-editor-middleware-2.6.1.tgz}
    name: launch-editor-middleware
    version: 2.6.1
    dependencies:
      launch-editor: registry.npmjs.org/launch-editor@2.6.1
    dev: true

  registry.npmjs.org/launch-editor@2.6.1:
    resolution: {integrity: sha512-eB/uXmFVpY4zezmGp5XtU21kwo7GBbKB+EQ+UZeWtGb9yAM5xt/Evk+lYH3eRNAtId+ej4u7TYPFZ07w4s7rRw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/launch-editor/-/launch-editor-2.6.1.tgz}
    name: launch-editor
    version: 2.6.1
    dependencies:
      picocolors: registry.npmjs.org/picocolors@1.0.0
      shell-quote: registry.npmjs.org/shell-quote@1.8.1
    dev: true

  registry.npmjs.org/levn@0.4.1:
    resolution: {integrity: sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/levn/-/levn-0.4.1.tgz}
    name: levn
    version: 0.4.1
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: registry.npmjs.org/prelude-ls@1.2.1
      type-check: registry.npmjs.org/type-check@0.4.0
    dev: true

  registry.npmjs.org/lilconfig@2.1.0:
    resolution: {integrity: sha512-utWOt/GHzuUxnLKxB6dk81RoOeoNeHgbrXiuGk4yyF5qlRz+iIVWu56E2fqGHFrXz0QNUhLB/8nKqvRH66JKGQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lilconfig/-/lilconfig-2.1.0.tgz}
    name: lilconfig
    version: 2.1.0
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/lines-and-columns@1.2.4:
    resolution: {integrity: sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz}
    name: lines-and-columns
    version: 1.2.4
    dev: true

  registry.npmjs.org/loader-runner@4.3.0:
    resolution: {integrity: sha512-3R/1M+yS3j5ou80Me59j7F9IMs4PXs3VqRrm0TU3AbKPxlmpoY1TNscJV/oGJXo8qCatFGTfDbY6W6ipGOYXfg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/loader-runner/-/loader-runner-4.3.0.tgz}
    name: loader-runner
    version: 4.3.0
    engines: {node: '>=6.11.5'}
    dev: true

  registry.npmjs.org/loader-utils@1.4.2:
    resolution: {integrity: sha512-I5d00Pd/jwMD2QCduo657+YM/6L3KZu++pmX9VFncxaxvHcru9jx1lBaFft+r4Mt2jK0Yhp41XlRAihzPxHNCg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/loader-utils/-/loader-utils-1.4.2.tgz}
    name: loader-utils
    version: 1.4.2
    engines: {node: '>=4.0.0'}
    dependencies:
      big.js: registry.npmjs.org/big.js@5.2.2
      emojis-list: registry.npmjs.org/emojis-list@3.0.0
      json5: registry.npmjs.org/json5@1.0.2
    dev: true

  registry.npmjs.org/loader-utils@2.0.4:
    resolution: {integrity: sha512-xXqpXoINfFhgua9xiqD8fPFHgkoq1mmmpE92WlDbm9rNRd/EbRb+Gqf908T2DMfuHjjJlksiK2RbHVOdD/MqSw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/loader-utils/-/loader-utils-2.0.4.tgz}
    name: loader-utils
    version: 2.0.4
    engines: {node: '>=8.9.0'}
    dependencies:
      big.js: registry.npmjs.org/big.js@5.2.2
      emojis-list: registry.npmjs.org/emojis-list@3.0.0
      json5: registry.npmjs.org/json5@2.2.3
    dev: true

  registry.npmjs.org/locate-path@5.0.0:
    resolution: {integrity: sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz}
    name: locate-path
    version: 5.0.0
    engines: {node: '>=8'}
    dependencies:
      p-locate: registry.npmjs.org/p-locate@4.1.0
    dev: true

  registry.npmjs.org/lodash.debounce@4.0.8:
    resolution: {integrity: sha512-FT1yDzDYEoYWhnSGnpE/4Kj1fLZkDFyqRb7fNt6FdYOSxlUWAtp42Eh6Wb0rGIv/m9Bgo7x4GhQbm5Ys4SG5ow==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lodash.debounce/-/lodash.debounce-4.0.8.tgz}
    name: lodash.debounce
    version: 4.0.8
    dev: true

  registry.npmjs.org/lodash.defaultsdeep@4.6.1:
    resolution: {integrity: sha512-3j8wdDzYuWO3lM3Reg03MuQR957t287Rpcxp1njpEa8oDrikb+FwGdW3n+FELh/A6qib6yPit0j/pv9G/yeAqA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lodash.defaultsdeep/-/lodash.defaultsdeep-4.6.1.tgz}
    name: lodash.defaultsdeep
    version: 4.6.1
    dev: true

  registry.npmjs.org/lodash.kebabcase@4.1.1:
    resolution: {integrity: sha512-N8XRTIMMqqDgSy4VLKPnJ/+hpGZN+PHQiJnSenYqPaVV/NCqEogTnAdZLQiGKhxX+JCs8waWq2t1XHWKOmlY8g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lodash.kebabcase/-/lodash.kebabcase-4.1.1.tgz}
    name: lodash.kebabcase
    version: 4.1.1
    dev: true

  registry.npmjs.org/lodash.mapvalues@4.6.0:
    resolution: {integrity: sha512-JPFqXFeZQ7BfS00H58kClY7SPVeHertPE0lNuCyZ26/XlN8TvakYD7b9bGyNmXbT/D3BbtPAAmq90gPWqLkxlQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lodash.mapvalues/-/lodash.mapvalues-4.6.0.tgz}
    name: lodash.mapvalues
    version: 4.6.0
    dev: true

  registry.npmjs.org/lodash.memoize@4.1.2:
    resolution: {integrity: sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz}
    name: lodash.memoize
    version: 4.1.2
    dev: true

  registry.npmjs.org/lodash.merge@4.6.2:
    resolution: {integrity: sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz}
    name: lodash.merge
    version: 4.6.2
    dev: true

  registry.npmjs.org/lodash.truncate@4.4.2:
    resolution: {integrity: sha512-jttmRe7bRse52OsWIMDLaXxWqRAmtIUccAQ3garviCqJjafXOfNMO0yMfNpdD6zbGaTU0P5Nz7e7gAT6cKmJRw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lodash.truncate/-/lodash.truncate-4.4.2.tgz}
    name: lodash.truncate
    version: 4.4.2
    dev: true

  registry.npmjs.org/lodash.uniq@4.5.0:
    resolution: {integrity: sha512-xfBaXQd9ryd9dlSDvnvI0lvxfLJlYAZzXomUYzLKtUeOQvOP5piqAWuGtrhWeqaXK9hhoM/iyJc5AV+XfsX3HQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lodash.uniq/-/lodash.uniq-4.5.0.tgz}
    name: lodash.uniq
    version: 4.5.0
    dev: true

  registry.npmjs.org/lodash@4.17.21:
    resolution: {integrity: sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz}
    name: lodash
    version: 4.17.21
    dev: true

  registry.npmjs.org/log-symbols@4.1.0:
    resolution: {integrity: sha512-8XPvpAA8uyhfteu8pIvQxpJZ7SYYdpUivZpGy6sFsBuKRY/7rQGavedeB8aK+Zkyq6upMFVL/9AW6vOYzfRyLg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/log-symbols/-/log-symbols-4.1.0.tgz}
    name: log-symbols
    version: 4.1.0
    engines: {node: '>=10'}
    dependencies:
      chalk: registry.npmjs.org/chalk@4.1.2
      is-unicode-supported: registry.npmjs.org/is-unicode-supported@0.1.0
    dev: true

  registry.npmjs.org/log-update@2.3.0:
    resolution: {integrity: sha512-vlP11XfFGyeNQlmEn9tJ66rEW1coA/79m5z6BCkudjbAGE83uhAcGYrBFwfs3AdLiLzGRusRPAbSPK9xZteCmg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/log-update/-/log-update-2.3.0.tgz}
    name: log-update
    version: 2.3.0
    engines: {node: '>=4'}
    dependencies:
      ansi-escapes: registry.npmjs.org/ansi-escapes@3.2.0
      cli-cursor: registry.npmjs.org/cli-cursor@2.1.0
      wrap-ansi: registry.npmjs.org/wrap-ansi@3.0.1
    dev: true

  registry.npmjs.org/lower-case@2.0.2:
    resolution: {integrity: sha512-7fm3l3NAF9WfN6W3JOmf5drwpVqX78JtoGJ3A6W0a6ZnldM41w2fV5D490psKFTpMds8TJse/eHLFFsNHHjHgg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lower-case/-/lower-case-2.0.2.tgz}
    name: lower-case
    version: 2.0.2
    dependencies:
      tslib: registry.npmjs.org/tslib@2.6.2
    dev: true

  registry.npmjs.org/lru-cache@4.1.5:
    resolution: {integrity: sha512-sWZlbEP2OsHNkXrMl5GYk/jKk70MBng6UU4YI/qGDYbgf6YbP4EvmqISbXCoJiRKs+1bSpFHVgQxvJ17F2li5g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lru-cache/-/lru-cache-4.1.5.tgz}
    name: lru-cache
    version: 4.1.5
    dependencies:
      pseudomap: registry.npmjs.org/pseudomap@1.0.2
      yallist: registry.npmjs.org/yallist@2.1.2
    dev: true

  registry.npmjs.org/lru-cache@5.1.1:
    resolution: {integrity: sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz}
    name: lru-cache
    version: 5.1.1
    dependencies:
      yallist: registry.npmjs.org/yallist@3.1.1
    dev: true

  registry.npmjs.org/lru-cache@6.0.0:
    resolution: {integrity: sha512-Jo6dJ04CmSjuznwJSS3pUeWmd/H0ffTlkXXgwZi+eq1UCmqQwCh+eLsYOYCwY991i2Fah4h1BEMCx4qThGbsiA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/lru-cache/-/lru-cache-6.0.0.tgz}
    name: lru-cache
    version: 6.0.0
    engines: {node: '>=10'}
    dependencies:
      yallist: registry.npmjs.org/yallist@4.0.0
    dev: true

  registry.npmjs.org/magic-string@0.30.10:
    resolution: {integrity: sha512-iIRwTIf0QKV3UAnYK4PU8uiEc4SRh5jX0mwpIwETPpHdhVM4f53RSwS/vXvN1JhGX+Cs7B8qIq3d6AH49O5fAQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/magic-string/-/magic-string-0.30.10.tgz}
    name: magic-string
    version: 0.30.10
    dependencies:
      '@jridgewell/sourcemap-codec': registry.npmjs.org/@jridgewell/sourcemap-codec@1.4.15
    dev: true

  registry.npmjs.org/make-dir@3.1.0:
    resolution: {integrity: sha512-g3FeP20LNwhALb/6Cz6Dd4F2ngze0jz7tbzrD2wAV+o9FeNHe4rL+yK2md0J/fiSf1sa1ADhXqi5+oVwOM/eGw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/make-dir/-/make-dir-3.1.0.tgz}
    name: make-dir
    version: 3.1.0
    engines: {node: '>=8'}
    dependencies:
      semver: registry.npmjs.org/semver@6.3.1
    dev: true

  registry.npmjs.org/mdn-data@2.0.14:
    resolution: {integrity: sha512-dn6wd0uw5GsdswPFfsgMp5NSB0/aDe6fK94YJV/AJDYXL6HVLWBsxeq7js7Ad+mU2K9LAlwpk6kN2D5mwCPVow==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/mdn-data/-/mdn-data-2.0.14.tgz}
    name: mdn-data
    version: 2.0.14
    dev: true

  registry.npmjs.org/media-typer@0.3.0:
    resolution: {integrity: sha512-dq+qelQ9akHpcOl/gUVRTxVIOkAJ1wR3QAvb4RsVjS8oVoFjDGTc679wJYmUmknUF5HwMLOgb5O+a3KxfWapPQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/media-typer/-/media-typer-0.3.0.tgz}
    name: media-typer
    version: 0.3.0
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/memfs@3.5.3:
    resolution: {integrity: sha512-UERzLsxzllchadvbPs5aolHh65ISpKpM+ccLbOJ8/vvpBKmAWf+la7dXFy7Mr0ySHbdHrFv5kGFCUHHe6GFEmw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/memfs/-/memfs-3.5.3.tgz}
    name: memfs
    version: 3.5.3
    engines: {node: '>= 4.0.0'}
    dependencies:
      fs-monkey: registry.npmjs.org/fs-monkey@1.0.5
    dev: true

  registry.npmjs.org/merge-descriptors@1.0.1:
    resolution: {integrity: sha512-cCi6g3/Zr1iqQi6ySbseM1Xvooa98N0w31jzUYrXPX2xqObmFGHJ0tQ5u74H3mVh7wLouTseZyYIq39g8cNp1w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/merge-descriptors/-/merge-descriptors-1.0.1.tgz}
    name: merge-descriptors
    version: 1.0.1
    dev: true

  registry.npmjs.org/merge-source-map@1.1.0:
    resolution: {integrity: sha512-Qkcp7P2ygktpMPh2mCQZaf3jhN6D3Z/qVZHSdWvQ+2Ef5HgRAPBO57A77+ENm0CPx2+1Ce/MYKi3ymqdfuqibw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/merge-source-map/-/merge-source-map-1.1.0.tgz}
    name: merge-source-map
    version: 1.1.0
    dependencies:
      source-map: registry.npmjs.org/source-map@0.6.1
    dev: true

  registry.npmjs.org/merge-stream@2.0.0:
    resolution: {integrity: sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz}
    name: merge-stream
    version: 2.0.0
    dev: true

  registry.npmjs.org/merge2@1.4.1:
    resolution: {integrity: sha512-8q7VEgMJW4J8tcfVPy8g09NcQwZdbwFEqhe/WZkoIzjn/3TGDwtOCYtXGxA3O8tPzpczCCDgv+P2P5y00ZJOOg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/merge2/-/merge2-1.4.1.tgz}
    name: merge2
    version: 1.4.1
    engines: {node: '>= 8'}
    dev: true

  registry.npmjs.org/methods@1.1.2:
    resolution: {integrity: sha512-iclAHeNqNm68zFtnZ0e+1L2yUIdvzNoauKU4WBA3VvH/vPFieF7qfRlwUZU+DA9P9bPXIS90ulxoUoCH23sV2w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/methods/-/methods-1.1.2.tgz}
    name: methods
    version: 1.1.2
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/micromatch@4.0.5:
    resolution: {integrity: sha512-DMy+ERcEW2q8Z2Po+WNXuw3c5YaUSFjAO5GsJqfEl7UjvtIuFKO6ZrKvcItdy98dwFI2N1tg3zNIdKaQT+aNdA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/micromatch/-/micromatch-4.0.5.tgz}
    name: micromatch
    version: 4.0.5
    engines: {node: '>=8.6'}
    dependencies:
      braces: registry.npmjs.org/braces@3.0.2
      picomatch: registry.npmjs.org/picomatch@2.3.1
    dev: true

  registry.npmjs.org/mime-db@1.52.0:
    resolution: {integrity: sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz}
    name: mime-db
    version: 1.52.0
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/mime-types@2.1.35:
    resolution: {integrity: sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz}
    name: mime-types
    version: 2.1.35
    engines: {node: '>= 0.6'}
    dependencies:
      mime-db: registry.npmjs.org/mime-db@1.52.0
    dev: true

  registry.npmjs.org/mime@1.6.0:
    resolution: {integrity: sha512-x0Vn8spI+wuJ1O6S7gnbaQg8Pxh4NNHb7KSINmEWKiPE4RKOplvijn+NkmYmmRgP68mc70j2EbeTFRsrswaQeg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/mime/-/mime-1.6.0.tgz}
    name: mime
    version: 1.6.0
    engines: {node: '>=4'}
    hasBin: true
    dev: true

  registry.npmjs.org/mimic-fn@1.2.0:
    resolution: {integrity: sha512-jf84uxzwiuiIVKiOLpfYk7N46TSy8ubTonmneY9vrpHNAnp0QBt2BxWV9dO3/j+BoVAb+a5G6YDPW3M5HOdMWQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/mimic-fn/-/mimic-fn-1.2.0.tgz}
    name: mimic-fn
    version: 1.2.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/mimic-fn@2.1.0:
    resolution: {integrity: sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz}
    name: mimic-fn
    version: 2.1.0
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/mini-css-extract-plugin@2.9.0(webpack@5.91.0):
    resolution: {integrity: sha512-Zs1YsZVfemekSZG+44vBsYTLQORkPMwnlv+aehcxK/NLKC+EGhDB39/YePYYqx/sTk6NnYpuqikhSn7+JIevTA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/mini-css-extract-plugin/-/mini-css-extract-plugin-2.9.0.tgz}
    id: registry.npmjs.org/mini-css-extract-plugin/2.9.0
    name: mini-css-extract-plugin
    version: 2.9.0
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^5.0.0
    dependencies:
      schema-utils: registry.npmjs.org/schema-utils@4.2.0
      tapable: registry.npmjs.org/tapable@2.2.1
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/minimalistic-assert@1.0.1:
    resolution: {integrity: sha512-UtJcAD4yEaGtjPezWuO9wC4nwUnVH/8/Im3yEHQP4b67cXlD/Qr9hdITCU1xDbSEXg2XKNaP8jsReV7vQd00/A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/minimalistic-assert/-/minimalistic-assert-1.0.1.tgz}
    name: minimalistic-assert
    version: 1.0.1
    dev: true

  registry.npmjs.org/minimatch@3.1.2:
    resolution: {integrity: sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz}
    name: minimatch
    version: 3.1.2
    dependencies:
      brace-expansion: registry.npmjs.org/brace-expansion@1.1.11
    dev: true

  registry.npmjs.org/minimist@1.2.8:
    resolution: {integrity: sha512-2yyAR8qBkN3YuheJanUpWC5U3bb5osDywNB8RzDVlDwDHbocAJveqqj1u8+SVD7jkWT4yvsHCpWqqWqAxb0zCA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/minimist/-/minimist-1.2.8.tgz}
    name: minimist
    version: 1.2.8
    dev: true

  registry.npmjs.org/minipass@3.3.6:
    resolution: {integrity: sha512-DxiNidxSEK+tHG6zOIklvNOwm3hvCrbUrdtzY74U6HKTJxvIDfOUL5W5P2Ghd3DTkhhKPYGqeNUIh5qcM4YBfw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/minipass/-/minipass-3.3.6.tgz}
    name: minipass
    version: 3.3.6
    engines: {node: '>=8'}
    dependencies:
      yallist: registry.npmjs.org/yallist@4.0.0
    dev: true

  registry.npmjs.org/mkdirp@0.5.6:
    resolution: {integrity: sha512-FP+p8RB8OWpF3YZBCrP5gtADmtXApB5AMLn+vdyA+PyxCjrCs00mjyUozssO33cwDeT3wNGdLxJ5M//YqtHAJw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/mkdirp/-/mkdirp-0.5.6.tgz}
    name: mkdirp
    version: 0.5.6
    hasBin: true
    dependencies:
      minimist: registry.npmjs.org/minimist@1.2.8
    dev: true

  registry.npmjs.org/module-alias@2.2.3:
    resolution: {integrity: sha512-23g5BFj4zdQL/b6tor7Ji+QY4pEfNH784BMslY9Qb0UnJWRAt+lQGLYmRaM0KDBwIG23ffEBELhZDP2rhi9f/Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/module-alias/-/module-alias-2.2.3.tgz}
    name: module-alias
    version: 2.2.3
    dev: true

  registry.npmjs.org/mrmime@2.0.0:
    resolution: {integrity: sha512-eu38+hdgojoyq63s+yTpN4XMBdt5l8HhMhc4VKLO9KM5caLIBvUm4thi7fFaxyTmCKeNnXZ5pAlBwCUnhA09uw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/mrmime/-/mrmime-2.0.0.tgz}
    name: mrmime
    version: 2.0.0
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/ms@2.0.0:
    resolution: {integrity: sha512-Tpp60P6IUJDTuOq/5Z8cdskzJujfwqfOTkrwIwj7IRISpnkJnT6SyJ4PCPnGMoFjC9ddhal5KVIYtAt97ix05A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ms/-/ms-2.0.0.tgz}
    name: ms
    version: 2.0.0
    dev: true

  registry.npmjs.org/ms@2.1.2:
    resolution: {integrity: sha512-sGkPx+VjMtmA6MX27oA4FBFELFCZZ4S4XqeGOXCv68tT+jb3vk/RyaKWP0PTKyWtmLSM0b+adUTEvbs1PEaH2w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ms/-/ms-2.1.2.tgz}
    name: ms
    version: 2.1.2
    dev: true

  registry.npmjs.org/ms@2.1.3:
    resolution: {integrity: sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ms/-/ms-2.1.3.tgz}
    name: ms
    version: 2.1.3
    dev: true

  registry.npmjs.org/multicast-dns@7.2.5:
    resolution: {integrity: sha512-2eznPJP8z2BFLX50tf0LuODrpINqP1RVIm/CObbTcBRITQgmC/TjcREF1NeTBzIcR5XO/ukWo+YHOjBbFwIupg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/multicast-dns/-/multicast-dns-7.2.5.tgz}
    name: multicast-dns
    version: 7.2.5
    hasBin: true
    dependencies:
      dns-packet: registry.npmjs.org/dns-packet@5.6.1
      thunky: registry.npmjs.org/thunky@1.1.0
    dev: true

  registry.npmjs.org/mz@2.7.0:
    resolution: {integrity: sha512-z81GNO7nnYMEhrGh9LeymoE4+Yr0Wn5McHIZMK5cfQCl+NDX08sCZgUc9/6MHni9IWuFLm1Z3HTCXu2z9fN62Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/mz/-/mz-2.7.0.tgz}
    name: mz
    version: 2.7.0
    dependencies:
      any-promise: registry.npmjs.org/any-promise@1.3.0
      object-assign: registry.npmjs.org/object-assign@4.1.1
      thenify-all: registry.npmjs.org/thenify-all@1.6.0
    dev: true

  registry.npmjs.org/nanoid@3.3.7:
    resolution: {integrity: sha512-eSRppjcPIatRIMC1U6UngP8XFcz8MQWGQdt1MTBQ7NaAmvXDfvNxbvWV3x2y6CdEUciCSsDHDQZbhYaB8QEo2g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/nanoid/-/nanoid-3.3.7.tgz}
    name: nanoid
    version: 3.3.7
    engines: {node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1}
    hasBin: true

  registry.npmjs.org/natural-compare@1.4.0:
    resolution: {integrity: sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz}
    name: natural-compare
    version: 1.4.0
    dev: true

  registry.npmjs.org/negotiator@0.6.3:
    resolution: {integrity: sha512-+EUsqGPLsM+j/zdChZjsnX51g4XrHFOIXwfnCVPGlQk/k5giakcKsuxCObBRu6DSm9opw/O6slWbJdghQM4bBg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/negotiator/-/negotiator-0.6.3.tgz}
    name: negotiator
    version: 0.6.3
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/neo-async@2.6.2:
    resolution: {integrity: sha512-Yd3UES5mWCSqR+qNT93S3UoYUkqAZ9lLg8a7g9rimsWmYGK8cVToA4/sF3RrshdyV3sAGMXVUmpMYOw+dLpOuw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/neo-async/-/neo-async-2.6.2.tgz}
    name: neo-async
    version: 2.6.2
    dev: true

  registry.npmjs.org/nice-try@1.0.5:
    resolution: {integrity: sha512-1nh45deeb5olNY7eX82BkPO7SSxR5SSYJiPTrTdFUVYwAl8CKMA5N9PjTYkHiRjisVcxcQ1HXdLhx2qxxJzLNQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/nice-try/-/nice-try-1.0.5.tgz}
    name: nice-try
    version: 1.0.5
    dev: true

  registry.npmjs.org/no-case@3.0.4:
    resolution: {integrity: sha512-fgAN3jGAh+RoxUGZHTSOLJIqUc2wmoBwGR4tbpNAKmmovFoWq0OdRkb0VkldReO2a2iBT/OEulG9XSUc10r3zg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/no-case/-/no-case-3.0.4.tgz}
    name: no-case
    version: 3.0.4
    dependencies:
      lower-case: registry.npmjs.org/lower-case@2.0.2
      tslib: registry.npmjs.org/tslib@2.6.2
    dev: true

  registry.npmjs.org/node-fetch@2.7.0:
    resolution: {integrity: sha512-c4FRfUm/dbcWZ7U+1Wq0AwCyFL+3nt2bEw05wfxSz+DWpWsitgmSgYmy2dQdWyKC1694ELPqMs/YzUSNozLt8A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/node-fetch/-/node-fetch-2.7.0.tgz}
    name: node-fetch
    version: 2.7.0
    engines: {node: 4.x || >=6.0.0}
    peerDependencies:
      encoding: ^0.1.0
    peerDependenciesMeta:
      encoding:
        optional: true
    dependencies:
      whatwg-url: registry.npmjs.org/whatwg-url@5.0.0
    dev: true

  registry.npmjs.org/node-forge@1.3.1:
    resolution: {integrity: sha512-dPEtOeMvF9VMcYV/1Wb8CPoVAXtp6MKMlcbAt4ddqmGqUJ6fQZFXkNZNkNlfevtNkGtaSoXf/vNNNSvgrdXwtA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/node-forge/-/node-forge-1.3.1.tgz}
    name: node-forge
    version: 1.3.1
    engines: {node: '>= 6.13.0'}
    dev: true

  registry.npmjs.org/node-releases@2.0.14:
    resolution: {integrity: sha512-y10wOWt8yZpqXmOgRo77WaHEmhYQYGNA6y421PKsKYWEK8aW+cqAphborZDhqfyKrbZEN92CN1X2KbafY2s7Yw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/node-releases/-/node-releases-2.0.14.tgz}
    name: node-releases
    version: 2.0.14
    dev: true

  registry.npmjs.org/normalize-package-data@2.5.0:
    resolution: {integrity: sha512-/5CMN3T0R4XTj4DcGaexo+roZSdSFW/0AOOTROrjxzCG1wrWXEsGbRKevjlIL+ZDE4sZlJr5ED4YW0yqmkK+eA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/normalize-package-data/-/normalize-package-data-2.5.0.tgz}
    name: normalize-package-data
    version: 2.5.0
    dependencies:
      hosted-git-info: registry.npmjs.org/hosted-git-info@2.8.9
      resolve: registry.npmjs.org/resolve@1.22.8
      semver: registry.npmjs.org/semver@5.7.2
      validate-npm-package-license: registry.npmjs.org/validate-npm-package-license@3.0.4
    dev: true

  registry.npmjs.org/normalize-path@1.0.0:
    resolution: {integrity: sha512-7WyT0w8jhpDStXRq5836AMmihQwq2nrUVQrgjvUo/p/NZf9uy/MeJ246lBJVmWuYXMlJuG9BNZHF0hWjfTbQUA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/normalize-path/-/normalize-path-1.0.0.tgz}
    name: normalize-path
    version: 1.0.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/normalize-path@3.0.0:
    resolution: {integrity: sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz}
    name: normalize-path
    version: 3.0.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/normalize-range@0.1.2:
    resolution: {integrity: sha512-bdok/XvKII3nUpklnV6P2hxtMNrCboOjAcyBuQnWEhO665FwrSNRxU+AqpsyvO6LgGYPspN+lu5CLtw4jPRKNA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/normalize-range/-/normalize-range-0.1.2.tgz}
    name: normalize-range
    version: 0.1.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/normalize-url@6.1.0:
    resolution: {integrity: sha512-DlL+XwOy3NxAQ8xuC0okPgK46iuVNAK01YN7RueYBqqFeGsBjV9XmCAzAdgt+667bCl5kPh9EqKKDwnaPG1I7A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/normalize-url/-/normalize-url-6.1.0.tgz}
    name: normalize-url
    version: 6.1.0
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/normalize-wheel@1.0.1:
    resolution: {integrity: sha512-1OnlAPZ3zgrk8B91HyRj+eVv+kS5u+Z0SCsak6Xil/kmgEia50ga7zfkumayonZrImffAxPU/5WcyGhzetHNPA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/normalize-wheel/-/normalize-wheel-1.0.1.tgz}
    name: normalize-wheel
    version: 1.0.1
    dev: false

  registry.npmjs.org/npm-run-path@2.0.2:
    resolution: {integrity: sha512-lJxZYlT4DW/bRUtFh1MQIWqmLwQfAxnqWG4HhEdjMlkrJYnJn0Jrr2u3mgxqaWsdiBc76TYkTG/mhrnYTuzfHw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/npm-run-path/-/npm-run-path-2.0.2.tgz}
    name: npm-run-path
    version: 2.0.2
    engines: {node: '>=4'}
    dependencies:
      path-key: registry.npmjs.org/path-key@2.0.1
    dev: true

  registry.npmjs.org/npm-run-path@4.0.1:
    resolution: {integrity: sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz}
    name: npm-run-path
    version: 4.0.1
    engines: {node: '>=8'}
    dependencies:
      path-key: registry.npmjs.org/path-key@3.1.1
    dev: true

  registry.npmjs.org/nth-check@2.1.1:
    resolution: {integrity: sha512-lqjrjmaOoAnWfMmBPL+XNnynZh2+swxiX3WUE0s4yEHI6m+AwrK2UZOimIRl3X/4QctVqS8AiZjFqyOGrMXb/w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/nth-check/-/nth-check-2.1.1.tgz}
    name: nth-check
    version: 2.1.1
    dependencies:
      boolbase: registry.npmjs.org/boolbase@1.0.0
    dev: true

  registry.npmjs.org/object-assign@4.1.1:
    resolution: {integrity: sha512-rJgTQnkUnH1sFw8yT6VSU3zD3sWmu6sZhIseY8VX+GRu3P6F7Fu+JNDoXfklElbLJSnc3FUQHVe4cU5hj+BcUg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/object-assign/-/object-assign-4.1.1.tgz}
    name: object-assign
    version: 4.1.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/object-inspect@1.13.1:
    resolution: {integrity: sha512-5qoj1RUiKOMsCCNLV1CBiPYE10sziTsnmNxkAI/rZhiD63CF7IqdFGC/XzjWjpSgLf0LxXX3bDFIh0E18f6UhQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/object-inspect/-/object-inspect-1.13.1.tgz}
    name: object-inspect
    version: 1.13.1
    dev: true

  registry.npmjs.org/object-keys@1.1.1:
    resolution: {integrity: sha512-NuAESUOUMrlIXOfHKzD6bpPu3tYt3xvjNdRIQ+FeT0lNb4K8WR70CaDxhuNguS2XG+GjkyMwOzsN5ZktImfhLA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/object-keys/-/object-keys-1.1.1.tgz}
    name: object-keys
    version: 1.1.1
    engines: {node: '>= 0.4'}
    dev: true

  registry.npmjs.org/object.assign@4.1.5:
    resolution: {integrity: sha512-byy+U7gp+FVwmyzKPYhW2h5l3crpmGsxl7X2s8y43IgxvG4g3QZ6CffDtsNQy1WsmZpQbO+ybo0AlW7TY6DcBQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/object.assign/-/object.assign-4.1.5.tgz}
    name: object.assign
    version: 4.1.5
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: registry.npmjs.org/call-bind@1.0.7
      define-properties: registry.npmjs.org/define-properties@1.2.1
      has-symbols: registry.npmjs.org/has-symbols@1.0.3
      object-keys: registry.npmjs.org/object-keys@1.1.1
    dev: true

  registry.npmjs.org/obuf@1.1.2:
    resolution: {integrity: sha512-PX1wu0AmAdPqOL1mWhqmlOd8kOIZQwGZw6rh7uby9fTc5lhaOWFLX3I6R1hrF9k3zUY40e6igsLGkDXK92LJNg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/obuf/-/obuf-1.1.2.tgz}
    name: obuf
    version: 1.1.2
    dev: true

  registry.npmjs.org/on-finished@2.4.1:
    resolution: {integrity: sha512-oVlzkg3ENAhCk2zdv7IJwd/QUD4z2RxRwpkcGY8psCVcCYZNq4wYnVWALHM+brtuJjePWiYF/ClmuDr8Ch5+kg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/on-finished/-/on-finished-2.4.1.tgz}
    name: on-finished
    version: 2.4.1
    engines: {node: '>= 0.8'}
    dependencies:
      ee-first: registry.npmjs.org/ee-first@1.1.1
    dev: true

  registry.npmjs.org/on-headers@1.0.2:
    resolution: {integrity: sha512-pZAE+FJLoyITytdqK0U5s+FIpjN0JP3OzFi/u8Rx+EV5/W+JTWGXG8xFzevE7AjBfDqHv/8vL8qQsIhHnqRkrA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/on-headers/-/on-headers-1.0.2.tgz}
    name: on-headers
    version: 1.0.2
    engines: {node: '>= 0.8'}
    dev: true

  registry.npmjs.org/once@1.4.0:
    resolution: {integrity: sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/once/-/once-1.4.0.tgz}
    name: once
    version: 1.4.0
    dependencies:
      wrappy: registry.npmjs.org/wrappy@1.0.2
    dev: true

  registry.npmjs.org/onetime@2.0.1:
    resolution: {integrity: sha512-oyyPpiMaKARvvcgip+JV+7zci5L8D1W9RZIz2l1o08AM3pfspitVWnPt3mzHcBPp12oYMTy0pqrFs/C+m3EwsQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/onetime/-/onetime-2.0.1.tgz}
    name: onetime
    version: 2.0.1
    engines: {node: '>=4'}
    dependencies:
      mimic-fn: registry.npmjs.org/mimic-fn@1.2.0
    dev: true

  registry.npmjs.org/onetime@5.1.2:
    resolution: {integrity: sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz}
    name: onetime
    version: 5.1.2
    engines: {node: '>=6'}
    dependencies:
      mimic-fn: registry.npmjs.org/mimic-fn@2.1.0
    dev: true

  registry.npmjs.org/open@8.4.2:
    resolution: {integrity: sha512-7x81NCL719oNbsq/3mh+hVrAWmFuEYUqrq/Iw3kUzH8ReypT9QQ0BLoJS7/G9k6N81XjW4qHWtjWwe/9eLy1EQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/open/-/open-8.4.2.tgz}
    name: open
    version: 8.4.2
    engines: {node: '>=12'}
    dependencies:
      define-lazy-prop: registry.npmjs.org/define-lazy-prop@2.0.0
      is-docker: registry.npmjs.org/is-docker@2.2.1
      is-wsl: registry.npmjs.org/is-wsl@2.2.0
    dev: true

  registry.npmjs.org/opener@1.5.2:
    resolution: {integrity: sha512-ur5UIdyw5Y7yEj9wLzhqXiy6GZ3Mwx0yGI+5sMn2r0N0v3cKJvUmFH5yPP+WXh9e0xfyzyJX95D8l088DNFj7A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/opener/-/opener-1.5.2.tgz}
    name: opener
    version: 1.5.2
    hasBin: true
    dev: true

  registry.npmjs.org/optionator@0.9.3:
    resolution: {integrity: sha512-JjCoypp+jKn1ttEFExxhetCKeJt9zhAgAve5FXHixTvFDW/5aEktX9bufBKLRRMdU7bNtpLfcGu94B3cdEJgjg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/optionator/-/optionator-0.9.3.tgz}
    name: optionator
    version: 0.9.3
    engines: {node: '>= 0.8.0'}
    dependencies:
      '@aashutoshrathi/word-wrap': registry.npmjs.org/@aashutoshrathi/word-wrap@1.2.6
      deep-is: registry.npmjs.org/deep-is@0.1.4
      fast-levenshtein: registry.npmjs.org/fast-levenshtein@2.0.6
      levn: registry.npmjs.org/levn@0.4.1
      prelude-ls: registry.npmjs.org/prelude-ls@1.2.1
      type-check: registry.npmjs.org/type-check@0.4.0
    dev: true

  registry.npmjs.org/ora@5.4.1:
    resolution: {integrity: sha512-5b6Y85tPxZZ7QytO+BQzysW31HJku27cRIlkbAXaNx+BdcVi+LlRFmVXzeF6a7JCwJpyw5c4b+YSVImQIrBpuQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ora/-/ora-5.4.1.tgz}
    name: ora
    version: 5.4.1
    engines: {node: '>=10'}
    dependencies:
      bl: registry.npmjs.org/bl@4.1.0
      chalk: registry.npmjs.org/chalk@4.1.2
      cli-cursor: registry.npmjs.org/cli-cursor@3.1.0
      cli-spinners: registry.npmjs.org/cli-spinners@2.9.2
      is-interactive: registry.npmjs.org/is-interactive@1.0.0
      is-unicode-supported: registry.npmjs.org/is-unicode-supported@0.1.0
      log-symbols: registry.npmjs.org/log-symbols@4.1.0
      strip-ansi: registry.npmjs.org/strip-ansi@6.0.1
      wcwidth: registry.npmjs.org/wcwidth@1.0.1
    dev: true

  registry.npmjs.org/p-finally@1.0.0:
    resolution: {integrity: sha512-LICb2p9CB7FS+0eR1oqWnHhp0FljGLZCWBE9aix0Uye9W8LTQPwMTYVGWQWIw9RdQiDg4+epXQODwIYJtSJaow==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/p-finally/-/p-finally-1.0.0.tgz}
    name: p-finally
    version: 1.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/p-limit@2.3.0:
    resolution: {integrity: sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz}
    name: p-limit
    version: 2.3.0
    engines: {node: '>=6'}
    dependencies:
      p-try: registry.npmjs.org/p-try@2.2.0
    dev: true

  registry.npmjs.org/p-locate@4.1.0:
    resolution: {integrity: sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz}
    name: p-locate
    version: 4.1.0
    engines: {node: '>=8'}
    dependencies:
      p-limit: registry.npmjs.org/p-limit@2.3.0
    dev: true

  registry.npmjs.org/p-retry@4.6.2:
    resolution: {integrity: sha512-312Id396EbJdvRONlngUx0NydfrIQ5lsYu0znKVUzVvArzEIt08V1qhtyESbGVd1FGX7UKtiFp5uwKZdM8wIuQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/p-retry/-/p-retry-4.6.2.tgz}
    name: p-retry
    version: 4.6.2
    engines: {node: '>=8'}
    dependencies:
      '@types/retry': registry.npmjs.org/@types/retry@0.12.0
      retry: registry.npmjs.org/retry@0.13.1
    dev: true

  registry.npmjs.org/p-try@2.2.0:
    resolution: {integrity: sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz}
    name: p-try
    version: 2.2.0
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/param-case@3.0.4:
    resolution: {integrity: sha512-RXlj7zCYokReqWpOPH9oYivUzLYZ5vAPIfEmCTNViosC78F8F0H9y7T7gG2M39ymgutxF5gcFEsyZQSph9Bp3A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/param-case/-/param-case-3.0.4.tgz}
    name: param-case
    version: 3.0.4
    dependencies:
      dot-case: registry.npmjs.org/dot-case@3.0.4
      tslib: registry.npmjs.org/tslib@2.6.2
    dev: true

  registry.npmjs.org/parent-module@1.0.1:
    resolution: {integrity: sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz}
    name: parent-module
    version: 1.0.1
    engines: {node: '>=6'}
    dependencies:
      callsites: registry.npmjs.org/callsites@3.1.0
    dev: true

  registry.npmjs.org/parse-json@5.2.0:
    resolution: {integrity: sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz}
    name: parse-json
    version: 5.2.0
    engines: {node: '>=8'}
    dependencies:
      '@babel/code-frame': registry.npmjs.org/@babel/code-frame@7.24.2
      error-ex: registry.npmjs.org/error-ex@1.3.2
      json-parse-even-better-errors: registry.npmjs.org/json-parse-even-better-errors@2.3.1
      lines-and-columns: registry.npmjs.org/lines-and-columns@1.2.4
    dev: true

  registry.npmjs.org/parse5-htmlparser2-tree-adapter@6.0.1:
    resolution: {integrity: sha512-qPuWvbLgvDGilKc5BoicRovlT4MtYT6JfJyBOMDsKoiT+GiuP5qyrPCnR9HcPECIJJmZh5jRndyNThnhhb/vlA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/parse5-htmlparser2-tree-adapter/-/parse5-htmlparser2-tree-adapter-6.0.1.tgz}
    name: parse5-htmlparser2-tree-adapter
    version: 6.0.1
    dependencies:
      parse5: registry.npmjs.org/parse5@6.0.1
    dev: true

  registry.npmjs.org/parse5@5.1.1:
    resolution: {integrity: sha512-ugq4DFI0Ptb+WWjAdOK16+u/nHfiIrcE+sh8kZMaM0WllQKLI9rOUq6c2b7cwPkXdzfQESqvoqK6ug7U/Yyzug==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/parse5/-/parse5-5.1.1.tgz}
    name: parse5
    version: 5.1.1
    dev: true

  registry.npmjs.org/parse5@6.0.1:
    resolution: {integrity: sha512-Ofn/CTFzRGTTxwpNEs9PP93gXShHcTq255nzRYSKe8AkVpZY7e1fpmTfOyoIvjP5HG7Z2ZM7VS9PPhQGW2pOpw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/parse5/-/parse5-6.0.1.tgz}
    name: parse5
    version: 6.0.1
    dev: true

  registry.npmjs.org/parseurl@1.3.3:
    resolution: {integrity: sha512-CiyeOxFT/JZyN5m0z9PfXw4SCBJ6Sygz1Dpl0wqjlhDEGGBP1GnsUVEL0p63hoG1fcj3fHynXi9NYO4nWOL+qQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/parseurl/-/parseurl-1.3.3.tgz}
    name: parseurl
    version: 1.3.3
    engines: {node: '>= 0.8'}
    dev: true

  registry.npmjs.org/pascal-case@3.1.2:
    resolution: {integrity: sha512-uWlGT3YSnK9x3BQJaOdcZwrnV6hPpd8jFH1/ucpiLRPh/2zCVJKS19E4GvYHvaCcACn3foXZ0cLB9Wrx1KGe5g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/pascal-case/-/pascal-case-3.1.2.tgz}
    name: pascal-case
    version: 3.1.2
    dependencies:
      no-case: registry.npmjs.org/no-case@3.0.4
      tslib: registry.npmjs.org/tslib@2.6.2
    dev: true

  registry.npmjs.org/path-exists@4.0.0:
    resolution: {integrity: sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz}
    name: path-exists
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/path-is-absolute@1.0.1:
    resolution: {integrity: sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz}
    name: path-is-absolute
    version: 1.0.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/path-key@2.0.1:
    resolution: {integrity: sha512-fEHGKCSmUSDPv4uoj8AlD+joPlq3peND+HRYyxFz4KPw4z926S/b8rIuFs2FYJg3BwsxJf6A9/3eIdLaYC+9Dw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/path-key/-/path-key-2.0.1.tgz}
    name: path-key
    version: 2.0.1
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/path-key@3.1.1:
    resolution: {integrity: sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz}
    name: path-key
    version: 3.1.1
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/path-parse@1.0.7:
    resolution: {integrity: sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz}
    name: path-parse
    version: 1.0.7
    dev: true

  registry.npmjs.org/path-to-regexp@0.1.7:
    resolution: {integrity: sha512-5DFkuoqlv1uYQKxy8omFBeJPQcdoE07Kv2sferDCrAq1ohOU+MSDswDIbnx3YAM60qIOnYa53wBhXW0EbMonrQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/path-to-regexp/-/path-to-regexp-0.1.7.tgz}
    name: path-to-regexp
    version: 0.1.7
    dev: true

  registry.npmjs.org/path-type@4.0.0:
    resolution: {integrity: sha512-gDKb8aZMDeD/tZWs9P6+q0J9Mwkdl6xMV8TjnGP3qJVJ06bdMgkbBlLU8IdfOsIsFz2BW1rNVT3XuNEl8zPAvw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/path-type/-/path-type-4.0.0.tgz}
    name: path-type
    version: 4.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/picocolors@0.2.1:
    resolution: {integrity: sha512-cMlDqaLEqfSaW8Z7N5Jw+lyIW869EzT73/F5lhtY9cLGoVxSXznfgfXMO0Z5K0o0Q2TkTXq+0KFsdnSe3jDViA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/picocolors/-/picocolors-0.2.1.tgz}
    name: picocolors
    version: 0.2.1
    dev: true

  registry.npmjs.org/picocolors@1.0.0:
    resolution: {integrity: sha512-1fygroTLlHu66zi26VoTDv8yRgm0Fccecssto+MhsZ0D/DGW2sm8E8AjW7NU5VVTRt5GxbeZ5qBuJr+HyLYkjQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/picocolors/-/picocolors-1.0.0.tgz}
    name: picocolors
    version: 1.0.0

  registry.npmjs.org/picomatch@2.3.1:
    resolution: {integrity: sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz}
    name: picomatch
    version: 2.3.1
    engines: {node: '>=8.6'}
    dev: true

  registry.npmjs.org/pkg-dir@4.2.0:
    resolution: {integrity: sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz}
    name: pkg-dir
    version: 4.2.0
    engines: {node: '>=8'}
    dependencies:
      find-up: registry.npmjs.org/find-up@4.1.0
    dev: true

  registry.npmjs.org/portfinder@1.0.32:
    resolution: {integrity: sha512-on2ZJVVDXRADWE6jnQaX0ioEylzgBpQk8r55NE4wjXW1ZxO+BgDlY6DXwj20i0V8eB4SenDQ00WEaxfiIQPcxg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/portfinder/-/portfinder-1.0.32.tgz}
    name: portfinder
    version: 1.0.32
    engines: {node: '>= 0.12.0'}
    dependencies:
      async: registry.npmjs.org/async@2.6.4
      debug: registry.npmjs.org/debug@3.2.7
      mkdirp: registry.npmjs.org/mkdirp@0.5.6
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/postcss-calc@8.2.4(postcss@8.4.38):
    resolution: {integrity: sha512-SmWMSJmB8MRnnULldx0lQIyhSNvuDl9HfrZkaqqE/WHAhToYsAvDq+yAsA/kIyINDszOp3Rh0GFoNuH5Ypsm3Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-calc/-/postcss-calc-8.2.4.tgz}
    id: registry.npmjs.org/postcss-calc/8.2.4
    name: postcss-calc
    version: 8.2.4
    peerDependencies:
      postcss: ^8.2.2
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-selector-parser: registry.npmjs.org/postcss-selector-parser@6.0.16
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-colormin@5.3.1(postcss@8.4.38):
    resolution: {integrity: sha512-UsWQG0AqTFQmpBegeLLc1+c3jIqBNB0zlDGRWR+dQ3pRKJL1oeMzyqmH3o2PIfn9MBdNrVPWhDbT769LxCTLJQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-colormin/-/postcss-colormin-5.3.1.tgz}
    id: registry.npmjs.org/postcss-colormin/5.3.1
    name: postcss-colormin
    version: 5.3.1
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: registry.npmjs.org/browserslist@4.23.0
      caniuse-api: registry.npmjs.org/caniuse-api@3.0.0
      colord: registry.npmjs.org/colord@2.9.3
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-convert-values@5.1.3(postcss@8.4.38):
    resolution: {integrity: sha512-82pC1xkJZtcJEfiLw6UXnXVXScgtBrjlO5CBmuDQc+dlb88ZYheFsjTn40+zBVi3DkfF7iezO0nJUPLcJK3pvA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-convert-values/-/postcss-convert-values-5.1.3.tgz}
    id: registry.npmjs.org/postcss-convert-values/5.1.3
    name: postcss-convert-values
    version: 5.1.3
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: registry.npmjs.org/browserslist@4.23.0
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-discard-comments@5.1.2(postcss@8.4.38):
    resolution: {integrity: sha512-+L8208OVbHVF2UQf1iDmRcbdjJkuBF6IS29yBDSiWUIzpYaAhtNl6JYnYm12FnkeCwQqF5LeklOu6rAqgfBZqQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-discard-comments/-/postcss-discard-comments-5.1.2.tgz}
    id: registry.npmjs.org/postcss-discard-comments/5.1.2
    name: postcss-discard-comments
    version: 5.1.2
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
    dev: true

  registry.npmjs.org/postcss-discard-duplicates@5.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-zmX3IoSI2aoenxHV6C7plngHWWhUOV3sP1T8y2ifzxzbtnuhk1EdPwm0S1bIUNaJ2eNbWeGLEwzw8huPD67aQw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-discard-duplicates/-/postcss-discard-duplicates-5.1.0.tgz}
    id: registry.npmjs.org/postcss-discard-duplicates/5.1.0
    name: postcss-discard-duplicates
    version: 5.1.0
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
    dev: true

  registry.npmjs.org/postcss-discard-empty@5.1.1(postcss@8.4.38):
    resolution: {integrity: sha512-zPz4WljiSuLWsI0ir4Mcnr4qQQ5e1Ukc3i7UfE2XcrwKK2LIPIqE5jxMRxO6GbI3cv//ztXDsXwEWT3BHOGh3A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-discard-empty/-/postcss-discard-empty-5.1.1.tgz}
    id: registry.npmjs.org/postcss-discard-empty/5.1.1
    name: postcss-discard-empty
    version: 5.1.1
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
    dev: true

  registry.npmjs.org/postcss-discard-overridden@5.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-21nOL7RqWR1kasIVdKs8HNqQJhFxLsyRfAnUDm4Fe4t4mCWL9OJiHvlHPjcd8zc5Myu89b/7wZDnOSjFgeWRtw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-discard-overridden/-/postcss-discard-overridden-5.1.0.tgz}
    id: registry.npmjs.org/postcss-discard-overridden/5.1.0
    name: postcss-discard-overridden
    version: 5.1.0
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
    dev: true

  registry.npmjs.org/postcss-loader@6.2.1(postcss@8.4.38)(webpack@5.91.0):
    resolution: {integrity: sha512-WbbYpmAaKcux/P66bZ40bpWsBucjx/TTgVVzRZ9yUO8yQfVBlameJ0ZGVaPfH64hNSBh63a+ICP5nqOpBA0w+Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-loader/-/postcss-loader-6.2.1.tgz}
    id: registry.npmjs.org/postcss-loader/6.2.1
    name: postcss-loader
    version: 6.2.1
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      postcss: ^7.0.0 || ^8.0.1
      webpack: ^5.0.0
    dependencies:
      cosmiconfig: registry.npmjs.org/cosmiconfig@7.1.0
      klona: registry.npmjs.org/klona@2.0.6
      postcss: registry.npmjs.org/postcss@8.4.38
      semver: registry.npmjs.org/semver@7.6.0
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/postcss-merge-longhand@5.1.7(postcss@8.4.38):
    resolution: {integrity: sha512-YCI9gZB+PLNskrK0BB3/2OzPnGhPkBEwmwhfYk1ilBHYVAZB7/tkTHFBAnCrvBBOmeYyMYw3DMjT55SyxMBzjQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-merge-longhand/-/postcss-merge-longhand-5.1.7.tgz}
    id: registry.npmjs.org/postcss-merge-longhand/5.1.7
    name: postcss-merge-longhand
    version: 5.1.7
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
      stylehacks: registry.npmjs.org/stylehacks@5.1.1(postcss@8.4.38)
    dev: true

  registry.npmjs.org/postcss-merge-rules@5.1.4(postcss@8.4.38):
    resolution: {integrity: sha512-0R2IuYpgU93y9lhVbO/OylTtKMVcHb67zjWIfCiKR9rWL3GUk1677LAqD/BcHizukdZEjT8Ru3oHRoAYoJy44g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-merge-rules/-/postcss-merge-rules-5.1.4.tgz}
    id: registry.npmjs.org/postcss-merge-rules/5.1.4
    name: postcss-merge-rules
    version: 5.1.4
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: registry.npmjs.org/browserslist@4.23.0
      caniuse-api: registry.npmjs.org/caniuse-api@3.0.0
      cssnano-utils: registry.npmjs.org/cssnano-utils@3.1.0(postcss@8.4.38)
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-selector-parser: registry.npmjs.org/postcss-selector-parser@6.0.16
    dev: true

  registry.npmjs.org/postcss-minify-font-values@5.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-el3mYTgx13ZAPPirSVsHqFzl+BBBDrXvbySvPGFnQcTI4iNslrPaFq4muTkLZmKlGk4gyFAYUBMH30+HurREyA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-minify-font-values/-/postcss-minify-font-values-5.1.0.tgz}
    id: registry.npmjs.org/postcss-minify-font-values/5.1.0
    name: postcss-minify-font-values
    version: 5.1.0
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-minify-gradients@5.1.1(postcss@8.4.38):
    resolution: {integrity: sha512-VGvXMTpCEo4qHTNSa9A0a3D+dxGFZCYwR6Jokk+/3oB6flu2/PnPXAh2x7x52EkY5xlIHLm+Le8tJxe/7TNhzw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-minify-gradients/-/postcss-minify-gradients-5.1.1.tgz}
    id: registry.npmjs.org/postcss-minify-gradients/5.1.1
    name: postcss-minify-gradients
    version: 5.1.1
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      colord: registry.npmjs.org/colord@2.9.3
      cssnano-utils: registry.npmjs.org/cssnano-utils@3.1.0(postcss@8.4.38)
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-minify-params@5.1.4(postcss@8.4.38):
    resolution: {integrity: sha512-+mePA3MgdmVmv6g+30rn57USjOGSAyuxUmkfiWpzalZ8aiBkdPYjXWtHuwJGm1v5Ojy0Z0LaSYhHaLJQB0P8Jw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-minify-params/-/postcss-minify-params-5.1.4.tgz}
    id: registry.npmjs.org/postcss-minify-params/5.1.4
    name: postcss-minify-params
    version: 5.1.4
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: registry.npmjs.org/browserslist@4.23.0
      cssnano-utils: registry.npmjs.org/cssnano-utils@3.1.0(postcss@8.4.38)
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-minify-selectors@5.2.1(postcss@8.4.38):
    resolution: {integrity: sha512-nPJu7OjZJTsVUmPdm2TcaiohIwxP+v8ha9NehQ2ye9szv4orirRU3SDdtUmKH+10nzn0bAyOXZ0UEr7OpvLehg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-minify-selectors/-/postcss-minify-selectors-5.2.1.tgz}
    id: registry.npmjs.org/postcss-minify-selectors/5.2.1
    name: postcss-minify-selectors
    version: 5.2.1
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-selector-parser: registry.npmjs.org/postcss-selector-parser@6.0.16
    dev: true

  registry.npmjs.org/postcss-modules-extract-imports@3.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-k3kNe0aNFQDAZGbin48pL2VNidTF0w4/eASDsxlyspobzU3wZQLOGj7L9gfRe0Jo9/4uud09DsjFNH7winGv8Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-modules-extract-imports/-/postcss-modules-extract-imports-3.1.0.tgz}
    id: registry.npmjs.org/postcss-modules-extract-imports/3.1.0
    name: postcss-modules-extract-imports
    version: 3.1.0
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
    dev: true

  registry.npmjs.org/postcss-modules-local-by-default@4.0.5(postcss@8.4.38):
    resolution: {integrity: sha512-6MieY7sIfTK0hYfafw1OMEG+2bg8Q1ocHCpoWLqOKj3JXlKu4G7btkmM/B7lFubYkYWmRSPLZi5chid63ZaZYw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-modules-local-by-default/-/postcss-modules-local-by-default-4.0.5.tgz}
    id: registry.npmjs.org/postcss-modules-local-by-default/4.0.5
    name: postcss-modules-local-by-default
    version: 4.0.5
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      icss-utils: registry.npmjs.org/icss-utils@5.1.0(postcss@8.4.38)
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-selector-parser: registry.npmjs.org/postcss-selector-parser@6.0.16
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-modules-scope@3.2.0(postcss@8.4.38):
    resolution: {integrity: sha512-oq+g1ssrsZOsx9M96c5w8laRmvEu9C3adDSjI8oTcbfkrTE8hx/zfyobUoWIxaKPO8bt6S62kxpw5GqypEw1QQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-modules-scope/-/postcss-modules-scope-3.2.0.tgz}
    id: registry.npmjs.org/postcss-modules-scope/3.2.0
    name: postcss-modules-scope
    version: 3.2.0
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-selector-parser: registry.npmjs.org/postcss-selector-parser@6.0.16
    dev: true

  registry.npmjs.org/postcss-modules-values@4.0.0(postcss@8.4.38):
    resolution: {integrity: sha512-RDxHkAiEGI78gS2ofyvCsu7iycRv7oqw5xMWn9iMoR0N/7mf9D50ecQqUo5BZ9Zh2vH4bCUR/ktCqbB9m8vJjQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-modules-values/-/postcss-modules-values-4.0.0.tgz}
    id: registry.npmjs.org/postcss-modules-values/4.0.0
    name: postcss-modules-values
    version: 4.0.0
    engines: {node: ^10 || ^12 || >= 14}
    peerDependencies:
      postcss: ^8.1.0
    dependencies:
      icss-utils: registry.npmjs.org/icss-utils@5.1.0(postcss@8.4.38)
      postcss: registry.npmjs.org/postcss@8.4.38
    dev: true

  registry.npmjs.org/postcss-normalize-charset@5.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-mSgUJ+pd/ldRGVx26p2wz9dNZ7ji6Pn8VWBajMXFf8jk7vUoSrZ2lt/wZR7DtlZYKesmZI680qjr2CeFF2fbUg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-normalize-charset/-/postcss-normalize-charset-5.1.0.tgz}
    id: registry.npmjs.org/postcss-normalize-charset/5.1.0
    name: postcss-normalize-charset
    version: 5.1.0
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
    dev: true

  registry.npmjs.org/postcss-normalize-display-values@5.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-WP4KIM4o2dazQXWmFaqMmcvsKmhdINFblgSeRgn8BJ6vxaMyaJkwAzpPpuvSIoG/rmX3M+IrRZEz2H0glrQNEA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-normalize-display-values/-/postcss-normalize-display-values-5.1.0.tgz}
    id: registry.npmjs.org/postcss-normalize-display-values/5.1.0
    name: postcss-normalize-display-values
    version: 5.1.0
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-normalize-positions@5.1.1(postcss@8.4.38):
    resolution: {integrity: sha512-6UpCb0G4eofTCQLFVuI3EVNZzBNPiIKcA1AKVka+31fTVySphr3VUgAIULBhxZkKgwLImhzMR2Bw1ORK+37INg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-normalize-positions/-/postcss-normalize-positions-5.1.1.tgz}
    id: registry.npmjs.org/postcss-normalize-positions/5.1.1
    name: postcss-normalize-positions
    version: 5.1.1
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-normalize-repeat-style@5.1.1(postcss@8.4.38):
    resolution: {integrity: sha512-mFpLspGWkQtBcWIRFLmewo8aC3ImN2i/J3v8YCFUwDnPu3Xz4rLohDO26lGjwNsQxB3YF0KKRwspGzE2JEuS0g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-normalize-repeat-style/-/postcss-normalize-repeat-style-5.1.1.tgz}
    id: registry.npmjs.org/postcss-normalize-repeat-style/5.1.1
    name: postcss-normalize-repeat-style
    version: 5.1.1
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-normalize-string@5.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-oYiIJOf4T9T1N4i+abeIc7Vgm/xPCGih4bZz5Nm0/ARVJ7K6xrDlLwvwqOydvyL3RHNf8qZk6vo3aatiw/go3w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-normalize-string/-/postcss-normalize-string-5.1.0.tgz}
    id: registry.npmjs.org/postcss-normalize-string/5.1.0
    name: postcss-normalize-string
    version: 5.1.0
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-normalize-timing-functions@5.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-DOEkzJ4SAXv5xkHl0Wa9cZLF3WCBhF3o1SKVxKQAa+0pYKlueTpCgvkFAHfk+Y64ezX9+nITGrDZeVGgITJXjg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-normalize-timing-functions/-/postcss-normalize-timing-functions-5.1.0.tgz}
    id: registry.npmjs.org/postcss-normalize-timing-functions/5.1.0
    name: postcss-normalize-timing-functions
    version: 5.1.0
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-normalize-unicode@5.1.1(postcss@8.4.38):
    resolution: {integrity: sha512-qnCL5jzkNUmKVhZoENp1mJiGNPcsJCs1aaRmURmeJGES23Z/ajaln+EPTD+rBeNkSryI+2WTdW+lwcVdOikrpA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-normalize-unicode/-/postcss-normalize-unicode-5.1.1.tgz}
    id: registry.npmjs.org/postcss-normalize-unicode/5.1.1
    name: postcss-normalize-unicode
    version: 5.1.1
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: registry.npmjs.org/browserslist@4.23.0
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-normalize-url@5.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-5upGeDO+PVthOxSmds43ZeMeZfKH+/DKgGRD7TElkkyS46JXAUhMzIKiCa7BabPeIy3AQcTkXwVVN7DbqsiCew==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-normalize-url/-/postcss-normalize-url-5.1.0.tgz}
    id: registry.npmjs.org/postcss-normalize-url/5.1.0
    name: postcss-normalize-url
    version: 5.1.0
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      normalize-url: registry.npmjs.org/normalize-url@6.1.0
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-normalize-whitespace@5.1.1(postcss@8.4.38):
    resolution: {integrity: sha512-83ZJ4t3NUDETIHTa3uEg6asWjSBYL5EdkVB0sDncx9ERzOKBVJIUeDO9RyA9Zwtig8El1d79HBp0JEi8wvGQnA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-normalize-whitespace/-/postcss-normalize-whitespace-5.1.1.tgz}
    id: registry.npmjs.org/postcss-normalize-whitespace/5.1.1
    name: postcss-normalize-whitespace
    version: 5.1.1
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-ordered-values@5.1.3(postcss@8.4.38):
    resolution: {integrity: sha512-9UO79VUhPwEkzbb3RNpqqghc6lcYej1aveQteWY+4POIwlqkYE21HKWaLDF6lWNuqCobEAyTovVhtI32Rbv2RQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-ordered-values/-/postcss-ordered-values-5.1.3.tgz}
    id: registry.npmjs.org/postcss-ordered-values/5.1.3
    name: postcss-ordered-values
    version: 5.1.3
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      cssnano-utils: registry.npmjs.org/cssnano-utils@3.1.0(postcss@8.4.38)
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-reduce-initial@5.1.2(postcss@8.4.38):
    resolution: {integrity: sha512-dE/y2XRaqAi6OvjzD22pjTUQ8eOfc6m/natGHgKFBK9DxFmIm69YmaRVQrGgFlEfc1HePIurY0TmDeROK05rIg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-reduce-initial/-/postcss-reduce-initial-5.1.2.tgz}
    id: registry.npmjs.org/postcss-reduce-initial/5.1.2
    name: postcss-reduce-initial
    version: 5.1.2
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: registry.npmjs.org/browserslist@4.23.0
      caniuse-api: registry.npmjs.org/caniuse-api@3.0.0
      postcss: registry.npmjs.org/postcss@8.4.38
    dev: true

  registry.npmjs.org/postcss-reduce-transforms@5.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-2fbdbmgir5AvpW9RLtdONx1QoYG2/EtqpNQbFASDlixBbAYuTcJ0dECwlqNqH7VbaUnEnh8SrxOe2sRIn24XyQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-reduce-transforms/-/postcss-reduce-transforms-5.1.0.tgz}
    id: registry.npmjs.org/postcss-reduce-transforms/5.1.0
    name: postcss-reduce-transforms
    version: 5.1.0
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
    dev: true

  registry.npmjs.org/postcss-selector-parser@6.0.16:
    resolution: {integrity: sha512-A0RVJrX+IUkVZbW3ClroRWurercFhieevHB38sr2+l9eUClMqome3LmEmnhlNy+5Mr2EYN6B2Kaw9wYdd+VHiw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-selector-parser/-/postcss-selector-parser-6.0.16.tgz}
    name: postcss-selector-parser
    version: 6.0.16
    engines: {node: '>=4'}
    dependencies:
      cssesc: registry.npmjs.org/cssesc@3.0.0
      util-deprecate: registry.npmjs.org/util-deprecate@1.0.2
    dev: true

  registry.npmjs.org/postcss-svgo@5.1.0(postcss@8.4.38):
    resolution: {integrity: sha512-D75KsH1zm5ZrHyxPakAxJWtkyXew5qwS70v56exwvw542d9CRtTo78K0WeFxZB4G7JXKKMbEZtZayTGdIky/eA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-svgo/-/postcss-svgo-5.1.0.tgz}
    id: registry.npmjs.org/postcss-svgo/5.1.0
    name: postcss-svgo
    version: 5.1.0
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-value-parser: registry.npmjs.org/postcss-value-parser@4.2.0
      svgo: registry.npmjs.org/svgo@2.8.0
    dev: true

  registry.npmjs.org/postcss-unique-selectors@5.1.1(postcss@8.4.38):
    resolution: {integrity: sha512-5JiODlELrz8L2HwxfPnhOWZYWDxVHWL83ufOv84NrcgipI7TaeRsatAhK4Tr2/ZiYldpK/wBvw5BD3qfaK96GA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-unique-selectors/-/postcss-unique-selectors-5.1.1.tgz}
    id: registry.npmjs.org/postcss-unique-selectors/5.1.1
    name: postcss-unique-selectors
    version: 5.1.1
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-selector-parser: registry.npmjs.org/postcss-selector-parser@6.0.16
    dev: true

  registry.npmjs.org/postcss-value-parser@4.2.0:
    resolution: {integrity: sha512-1NNCs6uurfkVbeXG4S8JFT9t19m45ICnif8zWLd5oPSZ50QnwMfK+H3jv408d4jw/7Bttv5axS5IiHoLaVNHeQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss-value-parser/-/postcss-value-parser-4.2.0.tgz}
    name: postcss-value-parser
    version: 4.2.0
    dev: true

  registry.npmjs.org/postcss@7.0.39:
    resolution: {integrity: sha512-yioayjNbHn6z1/Bywyb2Y4s3yvDAeXGOyxqD+LnVOinq6Mdmd++SW2wUNVzavyyHxd6+DxzWGIuosg6P1Rj8uA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss/-/postcss-7.0.39.tgz}
    name: postcss
    version: 7.0.39
    engines: {node: '>=6.0.0'}
    dependencies:
      picocolors: registry.npmjs.org/picocolors@0.2.1
      source-map: registry.npmjs.org/source-map@0.6.1
    dev: true

  registry.npmjs.org/postcss@8.4.38:
    resolution: {integrity: sha512-Wglpdk03BSfXkHoQa3b/oulrotAkwrlLDRSOb9D0bN86FdRyE9lppSp33aHNPgBa0JKCoB+drFLZkQoRRYae5A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/postcss/-/postcss-8.4.38.tgz}
    name: postcss
    version: 8.4.38
    engines: {node: ^10 || ^12 || >=14}
    dependencies:
      nanoid: registry.npmjs.org/nanoid@3.3.7
      picocolors: registry.npmjs.org/picocolors@1.0.0
      source-map-js: registry.npmjs.org/source-map-js@1.2.0

  registry.npmjs.org/prelude-ls@1.2.1:
    resolution: {integrity: sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz}
    name: prelude-ls
    version: 1.2.1
    engines: {node: '>= 0.8.0'}
    dev: true

  registry.npmjs.org/prettier@2.8.8:
    resolution: {integrity: sha512-tdN8qQGvNjw4CHbY+XXk0JgCXn9QiF21a55rBe5LJAU+kDyC4WQn4+awm2Xfk2lQMk5fKup9XgzTZtGkjBdP9Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/prettier/-/prettier-2.8.8.tgz}
    name: prettier
    version: 2.8.8
    engines: {node: '>=10.13.0'}
    hasBin: true
    requiresBuild: true
    optional: true

  registry.npmjs.org/pretty-error@4.0.0:
    resolution: {integrity: sha512-AoJ5YMAcXKYxKhuJGdcvse+Voc6v1RgnsR3nWcYU7q4t6z0Q6T86sv5Zq8VIRbOWWFpvdGE83LtdSMNd+6Y0xw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/pretty-error/-/pretty-error-4.0.0.tgz}
    name: pretty-error
    version: 4.0.0
    dependencies:
      lodash: registry.npmjs.org/lodash@4.17.21
      renderkid: registry.npmjs.org/renderkid@3.0.0
    dev: true

  registry.npmjs.org/process-nextick-args@2.0.1:
    resolution: {integrity: sha512-3ouUOpQhtgrbOa17J7+uxOTpITYWaGP7/AhoR3+A+/1e9skrzelGi/dXzEYyvbxubEF6Wn2ypscTKiKJFFn1ag==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/process-nextick-args/-/process-nextick-args-2.0.1.tgz}
    name: process-nextick-args
    version: 2.0.1
    dev: true

  registry.npmjs.org/progress-webpack-plugin@1.0.16(webpack@5.91.0):
    resolution: {integrity: sha512-sdiHuuKOzELcBANHfrupYo+r99iPRyOnw15qX+rNlVUqXGfjXdH4IgxriKwG1kNJwVswKQHMdj1hYZMcb9jFaA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/progress-webpack-plugin/-/progress-webpack-plugin-1.0.16.tgz}
    id: registry.npmjs.org/progress-webpack-plugin/1.0.16
    name: progress-webpack-plugin
    version: 1.0.16
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^2.0.0 || ^3.0.0 || ^4.0.0 || ^5.0.0
    dependencies:
      chalk: registry.npmjs.org/chalk@2.4.2
      figures: registry.npmjs.org/figures@2.0.0
      log-update: registry.npmjs.org/log-update@2.3.0
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/progress@2.0.3:
    resolution: {integrity: sha512-7PiHtLll5LdnKIMw100I+8xJXR5gW2QwWYkT6iJva0bXitZKa/XMrSbdmg3r2Xnaidz9Qumd0VPaMrZlF9V9sA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/progress/-/progress-2.0.3.tgz}
    name: progress
    version: 2.0.3
    engines: {node: '>=0.4.0'}
    dev: true

  registry.npmjs.org/proxy-addr@2.0.7:
    resolution: {integrity: sha512-llQsMLSUDUPT44jdrU/O37qlnifitDP+ZwrmmZcoSKyLKvtZxpyV0n2/bD/N4tBAAZ/gJEdZU7KMraoK1+XYAg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/proxy-addr/-/proxy-addr-2.0.7.tgz}
    name: proxy-addr
    version: 2.0.7
    engines: {node: '>= 0.10'}
    dependencies:
      forwarded: registry.npmjs.org/forwarded@0.2.0
      ipaddr.js: registry.npmjs.org/ipaddr.js@1.9.1
    dev: true

  registry.npmjs.org/pseudomap@1.0.2:
    resolution: {integrity: sha512-b/YwNhb8lk1Zz2+bXXpS/LK9OisiZZ1SNsSLxN1x2OXVEhW2Ckr/7mWE5vrC1ZTiJlD9g19jWszTmJsB+oEpFQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/pseudomap/-/pseudomap-1.0.2.tgz}
    name: pseudomap
    version: 1.0.2
    dev: true

  registry.npmjs.org/pump@3.0.0:
    resolution: {integrity: sha512-LwZy+p3SFs1Pytd/jYct4wpv49HiYCqd9Rlc5ZVdk0V+8Yzv6jR5Blk3TRmPL1ft69TxP0IMZGJ+WPFU2BFhww==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/pump/-/pump-3.0.0.tgz}
    name: pump
    version: 3.0.0
    dependencies:
      end-of-stream: registry.npmjs.org/end-of-stream@1.4.4
      once: registry.npmjs.org/once@1.4.0
    dev: true

  registry.npmjs.org/punycode@2.3.1:
    resolution: {integrity: sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz}
    name: punycode
    version: 2.3.1
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/qs@6.11.0:
    resolution: {integrity: sha512-MvjoMCJwEarSbUYk5O+nmoSzSutSsTwF85zcHPQ9OrlFoZOYIjaqBAJIqIXjptyD5vThxGq52Xu/MaJzRkIk4Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/qs/-/qs-6.11.0.tgz}
    name: qs
    version: 6.11.0
    engines: {node: '>=0.6'}
    dependencies:
      side-channel: registry.npmjs.org/side-channel@1.0.6
    dev: true

  registry.npmjs.org/queue-microtask@1.2.3:
    resolution: {integrity: sha512-NuaNSa6flKT5JaSYQzJok04JzTL1CA6aGhv5rfLW3PgqA+M2ChpZQnAC8h8i4ZFkBS8X5RqkDBHA7r4hej3K9A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/queue-microtask/-/queue-microtask-1.2.3.tgz}
    name: queue-microtask
    version: 1.2.3
    dev: true

  registry.npmjs.org/randombytes@2.1.0:
    resolution: {integrity: sha512-vYl3iOX+4CKUWuxGi9Ukhie6fsqXqS9FE2Zaic4tNFD2N2QQaXOMFbuKK4QmDHC0JO6B1Zp41J0LpT0oR68amQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/randombytes/-/randombytes-2.1.0.tgz}
    name: randombytes
    version: 2.1.0
    dependencies:
      safe-buffer: registry.npmjs.org/safe-buffer@5.2.1
    dev: true

  registry.npmjs.org/range-parser@1.2.1:
    resolution: {integrity: sha512-Hrgsx+orqoygnmhFbKaHE6c296J+HTAQXoxEF6gNupROmmGJRoyzfG3ccAveqCBrwr/2yxQ5BVd/GTl5agOwSg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/range-parser/-/range-parser-1.2.1.tgz}
    name: range-parser
    version: 1.2.1
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/raw-body@2.5.2:
    resolution: {integrity: sha512-8zGqypfENjCIqGhgXToC8aB2r7YrBX+AQAfIPs/Mlk+BtPTztOvTS01NRW/3Eh60J+a48lt8qsCzirQ6loCVfA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/raw-body/-/raw-body-2.5.2.tgz}
    name: raw-body
    version: 2.5.2
    engines: {node: '>= 0.8'}
    dependencies:
      bytes: registry.npmjs.org/bytes@3.1.2
      http-errors: registry.npmjs.org/http-errors@2.0.0
      iconv-lite: registry.npmjs.org/iconv-lite@0.4.24
      unpipe: registry.npmjs.org/unpipe@1.0.0
    dev: true

  registry.npmjs.org/read-pkg-up@7.0.1:
    resolution: {integrity: sha512-zK0TB7Xd6JpCLmlLmufqykGE+/TlOePD6qKClNW7hHDKFh/J7/7gCWGR7joEQEW1bKq3a3yUZSObOoWLFQ4ohg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/read-pkg-up/-/read-pkg-up-7.0.1.tgz}
    name: read-pkg-up
    version: 7.0.1
    engines: {node: '>=8'}
    dependencies:
      find-up: registry.npmjs.org/find-up@4.1.0
      read-pkg: registry.npmjs.org/read-pkg@5.2.0
      type-fest: registry.npmjs.org/type-fest@0.8.1
    dev: true

  registry.npmjs.org/read-pkg@5.2.0:
    resolution: {integrity: sha512-Ug69mNOpfvKDAc2Q8DRpMjjzdtrnv9HcSMX+4VsZxD1aZ6ZzrIE7rlzXBtWTyhULSMKg076AW6WR5iZpD0JiOg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/read-pkg/-/read-pkg-5.2.0.tgz}
    name: read-pkg
    version: 5.2.0
    engines: {node: '>=8'}
    dependencies:
      '@types/normalize-package-data': registry.npmjs.org/@types/normalize-package-data@2.4.4
      normalize-package-data: registry.npmjs.org/normalize-package-data@2.5.0
      parse-json: registry.npmjs.org/parse-json@5.2.0
      type-fest: registry.npmjs.org/type-fest@0.6.0
    dev: true

  registry.npmjs.org/readable-stream@2.3.8:
    resolution: {integrity: sha512-8p0AUk4XODgIewSi0l8Epjs+EVnWiK7NoDIEGU0HhE7+ZyY8D1IMY7odu5lRrFXGg71L15KG8QrPmum45RTtdA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/readable-stream/-/readable-stream-2.3.8.tgz}
    name: readable-stream
    version: 2.3.8
    dependencies:
      core-util-is: registry.npmjs.org/core-util-is@1.0.3
      inherits: registry.npmjs.org/inherits@2.0.4
      isarray: registry.npmjs.org/isarray@1.0.0
      process-nextick-args: registry.npmjs.org/process-nextick-args@2.0.1
      safe-buffer: registry.npmjs.org/safe-buffer@5.1.2
      string_decoder: registry.npmjs.org/string_decoder@1.1.1
      util-deprecate: registry.npmjs.org/util-deprecate@1.0.2
    dev: true

  registry.npmjs.org/readable-stream@3.6.2:
    resolution: {integrity: sha512-9u/sniCrY3D5WdsERHzHE4G2YCXqoG5FTHUiCC4SIbr6XcLZBY05ya9EKjYek9O5xOAwjGq+1JdGBAS7Q9ScoA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/readable-stream/-/readable-stream-3.6.2.tgz}
    name: readable-stream
    version: 3.6.2
    engines: {node: '>= 6'}
    dependencies:
      inherits: registry.npmjs.org/inherits@2.0.4
      string_decoder: registry.npmjs.org/string_decoder@1.3.0
      util-deprecate: registry.npmjs.org/util-deprecate@1.0.2
    dev: true

  registry.npmjs.org/readdirp@3.6.0:
    resolution: {integrity: sha512-hOS089on8RduqdbhvQ5Z37A0ESjsqz6qnRcffsMU3495FuTdqSm+7bhJ29JvIOsBDEEnan5DPu9t3To9VRlMzA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/readdirp/-/readdirp-3.6.0.tgz}
    name: readdirp
    version: 3.6.0
    engines: {node: '>=8.10.0'}
    dependencies:
      picomatch: registry.npmjs.org/picomatch@2.3.1
    dev: true

  registry.npmjs.org/regenerate-unicode-properties@10.1.1:
    resolution: {integrity: sha512-X007RyZLsCJVVrjgEFVpLUTZwyOZk3oiL75ZcuYjlIWd6rNJtOjkBwQc5AsRrpbKVkxN6sklw/k/9m2jJYOf8Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/regenerate-unicode-properties/-/regenerate-unicode-properties-10.1.1.tgz}
    name: regenerate-unicode-properties
    version: 10.1.1
    engines: {node: '>=4'}
    dependencies:
      regenerate: registry.npmjs.org/regenerate@1.4.2
    dev: true

  registry.npmjs.org/regenerate@1.4.2:
    resolution: {integrity: sha512-zrceR/XhGYU/d/opr2EKO7aRHUeiBI8qjtfHqADTwZd6Szfy16la6kqD0MIUs5z5hx6AaKa+PixpPrR289+I0A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/regenerate/-/regenerate-1.4.2.tgz}
    name: regenerate
    version: 1.4.2
    dev: true

  registry.npmjs.org/regenerator-runtime@0.11.1:
    resolution: {integrity: sha512-MguG95oij0fC3QV3URf4V2SDYGJhJnJGqvIIgdECeODCT98wSWDAJ94SSuVpYQUoTcGUIL6L4yNB7j1DFFHSBg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.11.1.tgz}
    name: regenerator-runtime
    version: 0.11.1
    dev: false

  registry.npmjs.org/regenerator-runtime@0.14.1:
    resolution: {integrity: sha512-dYnhHh0nJoMfnkZs6GmmhFknAGRrLznOu5nc9ML+EJxGvrx6H7teuevqVqCuPcPK//3eDrrjQhehXVx9cnkGdw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/regenerator-runtime/-/regenerator-runtime-0.14.1.tgz}
    name: regenerator-runtime
    version: 0.14.1
    dev: true

  registry.npmjs.org/regenerator-transform@0.15.2:
    resolution: {integrity: sha512-hfMp2BoF0qOk3uc5V20ALGDS2ddjQaLrdl7xrGXvAIow7qeWRM2VA2HuCHkUKk9slq3VwEwLNK3DFBqDfPGYtg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/regenerator-transform/-/regenerator-transform-0.15.2.tgz}
    name: regenerator-transform
    version: 0.15.2
    dependencies:
      '@babel/runtime': registry.npmjs.org/@babel/runtime@7.24.4
    dev: true

  registry.npmjs.org/regexpp@3.2.0:
    resolution: {integrity: sha512-pq2bWo9mVD43nbts2wGv17XLiNLya+GklZ8kaDLV2Z08gDCsGpnKn9BFMepvWuHCbyVvY7J5o5+BVvoQbmlJLg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/regexpp/-/regexpp-3.2.0.tgz}
    name: regexpp
    version: 3.2.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/regexpu-core@5.3.2:
    resolution: {integrity: sha512-RAM5FlZz+Lhmo7db9L298p2vHP5ZywrVXmVXpmAD9GuL5MPH6t9ROw1iA/wfHkQ76Qe7AaPF0nGuim96/IrQMQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/regexpu-core/-/regexpu-core-5.3.2.tgz}
    name: regexpu-core
    version: 5.3.2
    engines: {node: '>=4'}
    dependencies:
      '@babel/regjsgen': registry.npmjs.org/@babel/regjsgen@0.8.0
      regenerate: registry.npmjs.org/regenerate@1.4.2
      regenerate-unicode-properties: registry.npmjs.org/regenerate-unicode-properties@10.1.1
      regjsparser: registry.npmjs.org/regjsparser@0.9.1
      unicode-match-property-ecmascript: registry.npmjs.org/unicode-match-property-ecmascript@2.0.0
      unicode-match-property-value-ecmascript: registry.npmjs.org/unicode-match-property-value-ecmascript@2.1.0
    dev: true

  registry.npmjs.org/regjsparser@0.9.1:
    resolution: {integrity: sha512-dQUtn90WanSNl+7mQKcXAgZxvUe7Z0SqXlgzv0za4LwiUhyzBC58yQO3liFoUgu8GiJVInAhJjkj1N0EtQ5nkQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/regjsparser/-/regjsparser-0.9.1.tgz}
    name: regjsparser
    version: 0.9.1
    hasBin: true
    dependencies:
      jsesc: registry.npmjs.org/jsesc@0.5.0
    dev: true

  registry.npmjs.org/relateurl@0.2.7:
    resolution: {integrity: sha512-G08Dxvm4iDN3MLM0EsP62EDV9IuhXPR6blNz6Utcp7zyV3tr4HVNINt6MpaRWbxoOHT3Q7YN2P+jaHX8vUbgog==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/relateurl/-/relateurl-0.2.7.tgz}
    name: relateurl
    version: 0.2.7
    engines: {node: '>= 0.10'}
    dev: true

  registry.npmjs.org/renderkid@3.0.0:
    resolution: {integrity: sha512-q/7VIQA8lmM1hF+jn+sFSPWGlMkSAeNYcPLmDQx2zzuiDfaLrOmumR8iaUKlenFgh0XRPIUeSPlH3A+AW3Z5pg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/renderkid/-/renderkid-3.0.0.tgz}
    name: renderkid
    version: 3.0.0
    dependencies:
      css-select: registry.npmjs.org/css-select@4.3.0
      dom-converter: registry.npmjs.org/dom-converter@0.2.0
      htmlparser2: registry.npmjs.org/htmlparser2@6.1.0
      lodash: registry.npmjs.org/lodash@4.17.21
      strip-ansi: registry.npmjs.org/strip-ansi@6.0.1
    dev: true

  registry.npmjs.org/require-directory@2.1.1:
    resolution: {integrity: sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz}
    name: require-directory
    version: 2.1.1
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/require-from-string@2.0.2:
    resolution: {integrity: sha512-Xf0nWe6RseziFMu+Ap9biiUbmplq6S9/p+7w7YXP/JBHhrUDDUhwa+vANyubuqfZWTveU//DYVGsDG7RKL/vEw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/require-from-string/-/require-from-string-2.0.2.tgz}
    name: require-from-string
    version: 2.0.2
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/requires-port@1.0.0:
    resolution: {integrity: sha512-KigOCHcocU3XODJxsu8i/j8T9tzT4adHiecwORRQ0ZZFcp7ahwXuRU1m+yuO90C5ZUyGeGfocHDI14M3L3yDAQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/requires-port/-/requires-port-1.0.0.tgz}
    name: requires-port
    version: 1.0.0
    dev: true

  registry.npmjs.org/resize-observer-polyfill@1.5.1:
    resolution: {integrity: sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz}
    name: resize-observer-polyfill
    version: 1.5.1
    dev: false

  registry.npmjs.org/resolve-from@4.0.0:
    resolution: {integrity: sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz}
    name: resolve-from
    version: 4.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/resolve@1.22.8:
    resolution: {integrity: sha512-oKWePCxqpd6FlLvGV1VU0x7bkPmmCNolxzjMf4NczoDnQcIWrAF+cPtZn5i6n+RfD2d9i0tzpKnG6Yk168yIyw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/resolve/-/resolve-1.22.8.tgz}
    name: resolve
    version: 1.22.8
    hasBin: true
    dependencies:
      is-core-module: registry.npmjs.org/is-core-module@2.13.1
      path-parse: registry.npmjs.org/path-parse@1.0.7
      supports-preserve-symlinks-flag: registry.npmjs.org/supports-preserve-symlinks-flag@1.0.0
    dev: true

  registry.npmjs.org/restore-cursor@2.0.0:
    resolution: {integrity: sha512-6IzJLuGi4+R14vwagDHX+JrXmPVtPpn4mffDJ1UdR7/Edm87fl6yi8mMBIVvFtJaNTUvjughmW4hwLhRG7gC1Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/restore-cursor/-/restore-cursor-2.0.0.tgz}
    name: restore-cursor
    version: 2.0.0
    engines: {node: '>=4'}
    dependencies:
      onetime: registry.npmjs.org/onetime@2.0.1
      signal-exit: registry.npmjs.org/signal-exit@3.0.7
    dev: true

  registry.npmjs.org/restore-cursor@3.1.0:
    resolution: {integrity: sha512-l+sSefzHpj5qimhFSE5a8nufZYAM3sBSVMAPtYkmC+4EH2anSGaEMXSD0izRQbu9nfyQ9y5JrVmp7E8oZrUjvA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/restore-cursor/-/restore-cursor-3.1.0.tgz}
    name: restore-cursor
    version: 3.1.0
    engines: {node: '>=8'}
    dependencies:
      onetime: registry.npmjs.org/onetime@5.1.2
      signal-exit: registry.npmjs.org/signal-exit@3.0.7
    dev: true

  registry.npmjs.org/retry@0.13.1:
    resolution: {integrity: sha512-XQBQ3I8W1Cge0Seh+6gjj03LbmRFWuoszgK9ooCpwYIrhhoO80pfq4cUkU5DkknwfOfFteRwlZ56PYOGYyFWdg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/retry/-/retry-0.13.1.tgz}
    name: retry
    version: 0.13.1
    engines: {node: '>= 4'}
    dev: true

  registry.npmjs.org/reusify@1.0.4:
    resolution: {integrity: sha512-U9nH88a3fc/ekCF1l0/UP1IosiuIjyTh7hBvXVMHYgVcfGvt897Xguj2UOLDeI5BG2m7/uwyaLVT6fbtCwTyzw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/reusify/-/reusify-1.0.4.tgz}
    name: reusify
    version: 1.0.4
    engines: {iojs: '>=1.0.0', node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/rimraf@3.0.2:
    resolution: {integrity: sha512-JZkJMZkAGFFPP2YqXZXPbMlMBgsxzE8ILs4lMIX/2o0L9UBw9O/Y3o6wFw/i9YLapcUJWwqbi3kdxIPdC62TIA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/rimraf/-/rimraf-3.0.2.tgz}
    name: rimraf
    version: 3.0.2
    hasBin: true
    dependencies:
      glob: registry.npmjs.org/glob@7.2.3
    dev: true

  registry.npmjs.org/run-parallel@1.2.0:
    resolution: {integrity: sha512-5l4VyZR86LZ/lDxZTR6jqL8AFE2S0IFLMP26AbjsLVADxHdhB/c0GUsH+y39UfCi3dzz8OlQuPmnaJOMoDHQBA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/run-parallel/-/run-parallel-1.2.0.tgz}
    name: run-parallel
    version: 1.2.0
    dependencies:
      queue-microtask: registry.npmjs.org/queue-microtask@1.2.3
    dev: true

  registry.npmjs.org/safe-buffer@5.1.2:
    resolution: {integrity: sha512-Gd2UZBJDkXlY7GbJxfsE8/nvKkUEU1G38c1siN6QP6a9PT9MmHB8GnpscSmMJSoF8LOIrt8ud/wPtojys4G6+g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.1.2.tgz}
    name: safe-buffer
    version: 5.1.2
    dev: true

  registry.npmjs.org/safe-buffer@5.2.1:
    resolution: {integrity: sha512-rp3So07KcdmmKbGvgaNxQSJr7bGVSVk5S9Eq1F+ppbRo70+YeaDxkw5Dd8NPN+GD6bjnYm2VuPuCXmpuYvmCXQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/safe-buffer/-/safe-buffer-5.2.1.tgz}
    name: safe-buffer
    version: 5.2.1
    dev: true

  registry.npmjs.org/safer-buffer@2.1.2:
    resolution: {integrity: sha512-YZo3K82SD7Riyi0E1EQPojLz7kpepnSQI9IyPbHHg1XXXevb5dJI7tpyN2ADxGcQbHG7vcyRHk0cbwqcQriUtg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/safer-buffer/-/safer-buffer-2.1.2.tgz}
    name: safer-buffer
    version: 2.1.2
    dev: true

  registry.npmjs.org/schema-utils@2.7.1:
    resolution: {integrity: sha512-SHiNtMOUGWBQJwzISiVYKu82GiV4QYGePp3odlY1tuKO7gPtphAT5R/py0fA6xtbgLL/RvtJZnU9b8s0F1q0Xg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/schema-utils/-/schema-utils-2.7.1.tgz}
    name: schema-utils
    version: 2.7.1
    engines: {node: '>= 8.9.0'}
    dependencies:
      '@types/json-schema': registry.npmjs.org/@types/json-schema@7.0.15
      ajv: registry.npmjs.org/ajv@6.12.6
      ajv-keywords: registry.npmjs.org/ajv-keywords@3.5.2(ajv@6.12.6)
    dev: true

  registry.npmjs.org/schema-utils@3.3.0:
    resolution: {integrity: sha512-pN/yOAvcC+5rQ5nERGuwrjLlYvLTbCibnZ1I7B1LaiAz9BRBlE9GMgE/eqV30P7aJQUf7Ddimy/RsbYO/GrVGg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/schema-utils/-/schema-utils-3.3.0.tgz}
    name: schema-utils
    version: 3.3.0
    engines: {node: '>= 10.13.0'}
    dependencies:
      '@types/json-schema': registry.npmjs.org/@types/json-schema@7.0.15
      ajv: registry.npmjs.org/ajv@6.12.6
      ajv-keywords: registry.npmjs.org/ajv-keywords@3.5.2(ajv@6.12.6)
    dev: true

  registry.npmjs.org/schema-utils@4.2.0:
    resolution: {integrity: sha512-L0jRsrPpjdckP3oPug3/VxNKt2trR8TcabrM6FOAAlvC/9Phcmm+cuAgTlxBqdBR1WJx7Naj9WHw+aOmheSVbw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/schema-utils/-/schema-utils-4.2.0.tgz}
    name: schema-utils
    version: 4.2.0
    engines: {node: '>= 12.13.0'}
    dependencies:
      '@types/json-schema': registry.npmjs.org/@types/json-schema@7.0.15
      ajv: registry.npmjs.org/ajv@8.12.0
      ajv-formats: registry.npmjs.org/ajv-formats@2.1.1(ajv@8.12.0)
      ajv-keywords: registry.npmjs.org/ajv-keywords@5.1.0(ajv@8.12.0)
    dev: true

  registry.npmjs.org/select-hose@2.0.0:
    resolution: {integrity: sha512-mEugaLK+YfkijB4fx0e6kImuJdCIt2LxCRcbEYPqRGCs4F2ogyfZU5IAZRdjCP8JPq2AtdNoC/Dux63d9Kiryg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/select-hose/-/select-hose-2.0.0.tgz}
    name: select-hose
    version: 2.0.0
    dev: true

  registry.npmjs.org/selfsigned@2.4.1:
    resolution: {integrity: sha512-th5B4L2U+eGLq1TVh7zNRGBapioSORUeymIydxgFpwww9d2qyKvtuPU2jJuHvYAwwqi2Y596QBL3eEqcPEYL8Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/selfsigned/-/selfsigned-2.4.1.tgz}
    name: selfsigned
    version: 2.4.1
    engines: {node: '>=10'}
    dependencies:
      '@types/node-forge': registry.npmjs.org/@types/node-forge@1.3.11
      node-forge: registry.npmjs.org/node-forge@1.3.1
    dev: true

  registry.npmjs.org/semver@5.7.2:
    resolution: {integrity: sha512-cBznnQ9KjJqU67B52RMC65CMarK2600WFnbkcaiwWq3xy/5haFJlshgnpjovMVJ+Hff49d8GEn0b87C5pDQ10g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/semver/-/semver-5.7.2.tgz}
    name: semver
    version: 5.7.2
    hasBin: true
    dev: true

  registry.npmjs.org/semver@6.3.1:
    resolution: {integrity: sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/semver/-/semver-6.3.1.tgz}
    name: semver
    version: 6.3.1
    hasBin: true
    dev: true

  registry.npmjs.org/semver@7.6.0:
    resolution: {integrity: sha512-EnwXhrlwXMk9gKu5/flx5sv/an57AkRplG3hTK68W7FRDN+k+OWBj65M7719OkA82XLBxrcX0KSHj+X5COhOVg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/semver/-/semver-7.6.0.tgz}
    name: semver
    version: 7.6.0
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      lru-cache: registry.npmjs.org/lru-cache@6.0.0
    dev: true

  registry.npmjs.org/send@0.18.0:
    resolution: {integrity: sha512-qqWzuOjSFOuqPjFe4NOsMLafToQQwBSOEpS+FwEt3A2V3vKubTquT3vmLTQpFgMXp8AlFWFuP1qKaJZOtPpVXg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/send/-/send-0.18.0.tgz}
    name: send
    version: 0.18.0
    engines: {node: '>= 0.8.0'}
    dependencies:
      debug: registry.npmjs.org/debug@2.6.9
      depd: registry.npmjs.org/depd@2.0.0
      destroy: registry.npmjs.org/destroy@1.2.0
      encodeurl: registry.npmjs.org/encodeurl@1.0.2
      escape-html: registry.npmjs.org/escape-html@1.0.3
      etag: registry.npmjs.org/etag@1.8.1
      fresh: registry.npmjs.org/fresh@0.5.2
      http-errors: registry.npmjs.org/http-errors@2.0.0
      mime: registry.npmjs.org/mime@1.6.0
      ms: registry.npmjs.org/ms@2.1.3
      on-finished: registry.npmjs.org/on-finished@2.4.1
      range-parser: registry.npmjs.org/range-parser@1.2.1
      statuses: registry.npmjs.org/statuses@2.0.1
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/serialize-javascript@6.0.2:
    resolution: {integrity: sha512-Saa1xPByTTq2gdeFZYLLo+RFE35NHZkAbqZeWNd3BpzppeVisAqpDjcp8dyf6uIvEqJRd46jemmyA4iFIeVk8g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/serialize-javascript/-/serialize-javascript-6.0.2.tgz}
    name: serialize-javascript
    version: 6.0.2
    dependencies:
      randombytes: registry.npmjs.org/randombytes@2.1.0
    dev: true

  registry.npmjs.org/serve-index@1.9.1:
    resolution: {integrity: sha512-pXHfKNP4qujrtteMrSBb0rc8HJ9Ms/GrXwcUtUtD5s4ewDJI8bT3Cz2zTVRMKtri49pLx2e0Ya8ziP5Ya2pZZw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/serve-index/-/serve-index-1.9.1.tgz}
    name: serve-index
    version: 1.9.1
    engines: {node: '>= 0.8.0'}
    dependencies:
      accepts: registry.npmjs.org/accepts@1.3.8
      batch: registry.npmjs.org/batch@0.6.1
      debug: registry.npmjs.org/debug@2.6.9
      escape-html: registry.npmjs.org/escape-html@1.0.3
      http-errors: registry.npmjs.org/http-errors@1.6.3
      mime-types: registry.npmjs.org/mime-types@2.1.35
      parseurl: registry.npmjs.org/parseurl@1.3.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/serve-static@1.15.0:
    resolution: {integrity: sha512-XGuRDNjXUijsUL0vl6nSD7cwURuzEgglbOaFuZM9g3kwDXOWVTck0jLzjPzGD+TazWbboZYu52/9/XPdUgne9g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/serve-static/-/serve-static-1.15.0.tgz}
    name: serve-static
    version: 1.15.0
    engines: {node: '>= 0.8.0'}
    dependencies:
      encodeurl: registry.npmjs.org/encodeurl@1.0.2
      escape-html: registry.npmjs.org/escape-html@1.0.3
      parseurl: registry.npmjs.org/parseurl@1.3.3
      send: registry.npmjs.org/send@0.18.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/set-function-length@1.2.2:
    resolution: {integrity: sha512-pgRc4hJ4/sNjWCSS9AmnS40x3bNMDTknHgL5UaMBTMyJnU90EgWh1Rz+MC9eFu4BuN/UwZjKQuY/1v3rM7HMfg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/set-function-length/-/set-function-length-1.2.2.tgz}
    name: set-function-length
    version: 1.2.2
    engines: {node: '>= 0.4'}
    dependencies:
      define-data-property: registry.npmjs.org/define-data-property@1.1.4
      es-errors: registry.npmjs.org/es-errors@1.3.0
      function-bind: registry.npmjs.org/function-bind@1.1.2
      get-intrinsic: registry.npmjs.org/get-intrinsic@1.2.4
      gopd: registry.npmjs.org/gopd@1.0.1
      has-property-descriptors: registry.npmjs.org/has-property-descriptors@1.0.2
    dev: true

  registry.npmjs.org/setprototypeof@1.1.0:
    resolution: {integrity: sha512-BvE/TwpZX4FXExxOxZyRGQQv651MSwmWKZGqvmPcRIjDqWub67kTKuIMx43cZZrS/cBBzwBcNDWoFxt2XEFIpQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.1.0.tgz}
    name: setprototypeof
    version: 1.1.0
    dev: true

  registry.npmjs.org/setprototypeof@1.2.0:
    resolution: {integrity: sha512-E5LDX7Wrp85Kil5bhZv46j8jOeboKq5JMmYM3gVGdGH8xFpPWXUMsNrlODCrkoxMEeNi/XZIwuRvY4XNwYMJpw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/setprototypeof/-/setprototypeof-1.2.0.tgz}
    name: setprototypeof
    version: 1.2.0
    dev: true

  registry.npmjs.org/shallow-clone@3.0.1:
    resolution: {integrity: sha512-/6KqX+GVUdqPuPPd2LxDDxzX6CAbjJehAAOKlNpqqUpAqPM6HeL8f+o3a+JsyGjn2lv0WY8UsTgUJjU9Ok55NA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/shallow-clone/-/shallow-clone-3.0.1.tgz}
    name: shallow-clone
    version: 3.0.1
    engines: {node: '>=8'}
    dependencies:
      kind-of: registry.npmjs.org/kind-of@6.0.3
    dev: true

  registry.npmjs.org/shebang-command@1.2.0:
    resolution: {integrity: sha512-EV3L1+UQWGor21OmnvojK36mhg+TyIKDh3iFBKBohr5xeXIhNBcx8oWdgkTEEQ+BEFFYdLRuqMfd5L84N1V5Vg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/shebang-command/-/shebang-command-1.2.0.tgz}
    name: shebang-command
    version: 1.2.0
    engines: {node: '>=0.10.0'}
    dependencies:
      shebang-regex: registry.npmjs.org/shebang-regex@1.0.0
    dev: true

  registry.npmjs.org/shebang-command@2.0.0:
    resolution: {integrity: sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz}
    name: shebang-command
    version: 2.0.0
    engines: {node: '>=8'}
    dependencies:
      shebang-regex: registry.npmjs.org/shebang-regex@3.0.0
    dev: true

  registry.npmjs.org/shebang-regex@1.0.0:
    resolution: {integrity: sha512-wpoSFAxys6b2a2wHZ1XpDSgD7N9iVjg29Ph9uV/uaP9Ex/KXlkTZTeddxDPSYQpgvzKLGJke2UU0AzoGCjNIvQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/shebang-regex/-/shebang-regex-1.0.0.tgz}
    name: shebang-regex
    version: 1.0.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/shebang-regex@3.0.0:
    resolution: {integrity: sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz}
    name: shebang-regex
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/shell-quote@1.8.1:
    resolution: {integrity: sha512-6j1W9l1iAs/4xYBI1SYOVZyFcCis9b4KCLQ8fgAGG07QvzaRLVVRQvAy85yNmmZSjYjg4MWh4gNvlPujU/5LpA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.1.tgz}
    name: shell-quote
    version: 1.8.1
    dev: true

  registry.npmjs.org/side-channel@1.0.6:
    resolution: {integrity: sha512-fDW/EZ6Q9RiO8eFG8Hj+7u/oW+XrPTIChwCOM2+th2A6OblDtYYIpve9m+KvI9Z4C9qSEXlaGR6bTEYHReuglA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/side-channel/-/side-channel-1.0.6.tgz}
    name: side-channel
    version: 1.0.6
    engines: {node: '>= 0.4'}
    dependencies:
      call-bind: registry.npmjs.org/call-bind@1.0.7
      es-errors: registry.npmjs.org/es-errors@1.3.0
      get-intrinsic: registry.npmjs.org/get-intrinsic@1.2.4
      object-inspect: registry.npmjs.org/object-inspect@1.13.1
    dev: true

  registry.npmjs.org/signal-exit@3.0.7:
    resolution: {integrity: sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz}
    name: signal-exit
    version: 3.0.7
    dev: true

  registry.npmjs.org/sirv@2.0.4:
    resolution: {integrity: sha512-94Bdh3cC2PKrbgSOUqTiGPWVZeSiXfKOVZNJniWoqrWrRkB1CJzBU3NEbiTsPcYy1lDsANA/THzS+9WBiy5nfQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/sirv/-/sirv-2.0.4.tgz}
    name: sirv
    version: 2.0.4
    engines: {node: '>= 10'}
    dependencies:
      '@polka/url': registry.npmjs.org/@polka/url@1.0.0-next.25
      mrmime: registry.npmjs.org/mrmime@2.0.0
      totalist: registry.npmjs.org/totalist@3.0.1
    dev: true

  registry.npmjs.org/slash@3.0.0:
    resolution: {integrity: sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/slash/-/slash-3.0.0.tgz}
    name: slash
    version: 3.0.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/slice-ansi@4.0.0:
    resolution: {integrity: sha512-qMCMfhY040cVHT43K9BFygqYbUPFZKHOg7K73mtTWJRb8pyP3fzf4Ixd5SzdEJQ6MRUg/WBnOLxghZtKKurENQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/slice-ansi/-/slice-ansi-4.0.0.tgz}
    name: slice-ansi
    version: 4.0.0
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: registry.npmjs.org/ansi-styles@4.3.0
      astral-regex: registry.npmjs.org/astral-regex@2.0.0
      is-fullwidth-code-point: registry.npmjs.org/is-fullwidth-code-point@3.0.0
    dev: true

  registry.npmjs.org/sockjs@0.3.24:
    resolution: {integrity: sha512-GJgLTZ7vYb/JtPSSZ10hsOYIvEYsjbNU+zPdIHcUaWVNUEPivzxku31865sSSud0Da0W4lEeOPlmw93zLQchuQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/sockjs/-/sockjs-0.3.24.tgz}
    name: sockjs
    version: 0.3.24
    dependencies:
      faye-websocket: registry.npmjs.org/faye-websocket@0.11.4
      uuid: registry.npmjs.org/uuid@8.3.2
      websocket-driver: registry.npmjs.org/websocket-driver@0.7.4
    dev: true

  registry.npmjs.org/source-map-js@1.2.0:
    resolution: {integrity: sha512-itJW8lvSA0TXEphiRoawsCksnlf8SyvmFzIhltqAHluXd88pkCd+cXJVHTDwdCr0IzwptSm035IHQktUu1QUMg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.0.tgz}
    name: source-map-js
    version: 1.2.0
    engines: {node: '>=0.10.0'}

  registry.npmjs.org/source-map-support@0.5.21:
    resolution: {integrity: sha512-uBHU3L3czsIyYXKX88fdrGovxdSCoTGDRZ6SYXtSRxLZUzHg5P/66Ht6uoUlHu9EZod+inXhKo3qQgwXUT/y1w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.21.tgz}
    name: source-map-support
    version: 0.5.21
    dependencies:
      buffer-from: registry.npmjs.org/buffer-from@1.1.2
      source-map: registry.npmjs.org/source-map@0.6.1
    dev: true

  registry.npmjs.org/source-map@0.6.1:
    resolution: {integrity: sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz}
    name: source-map
    version: 0.6.1
    engines: {node: '>=0.10.0'}

  registry.npmjs.org/spdx-correct@3.2.0:
    resolution: {integrity: sha512-kN9dJbvnySHULIluDHy32WHRUu3Og7B9sbY7tsFLctQkIqnMh3hErYgdMjTYuqmcXX+lK5T1lnUt3G7zNswmZA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/spdx-correct/-/spdx-correct-3.2.0.tgz}
    name: spdx-correct
    version: 3.2.0
    dependencies:
      spdx-expression-parse: registry.npmjs.org/spdx-expression-parse@3.0.1
      spdx-license-ids: registry.npmjs.org/spdx-license-ids@3.0.17
    dev: true

  registry.npmjs.org/spdx-exceptions@2.5.0:
    resolution: {integrity: sha512-PiU42r+xO4UbUS1buo3LPJkjlO7430Xn5SVAhdpzzsPHsjbYVflnnFdATgabnLude+Cqu25p6N+g2lw/PFsa4w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/spdx-exceptions/-/spdx-exceptions-2.5.0.tgz}
    name: spdx-exceptions
    version: 2.5.0
    dev: true

  registry.npmjs.org/spdx-expression-parse@3.0.1:
    resolution: {integrity: sha512-cbqHunsQWnJNE6KhVSMsMeH5H/L9EpymbzqTQ3uLwNCLZ1Q481oWaofqH7nO6V07xlXwY6PhQdQ2IedWx/ZK4Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/spdx-expression-parse/-/spdx-expression-parse-3.0.1.tgz}
    name: spdx-expression-parse
    version: 3.0.1
    dependencies:
      spdx-exceptions: registry.npmjs.org/spdx-exceptions@2.5.0
      spdx-license-ids: registry.npmjs.org/spdx-license-ids@3.0.17
    dev: true

  registry.npmjs.org/spdx-license-ids@3.0.17:
    resolution: {integrity: sha512-sh8PWc/ftMqAAdFiBu6Fy6JUOYjqDJBJvIhpfDMyHrr0Rbp5liZqd4TjtQ/RgfLjKFZb+LMx5hpml5qOWy0qvg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/spdx-license-ids/-/spdx-license-ids-3.0.17.tgz}
    name: spdx-license-ids
    version: 3.0.17
    dev: true

  registry.npmjs.org/spdy-transport@3.0.0:
    resolution: {integrity: sha512-hsLVFE5SjA6TCisWeJXFKniGGOpBgMLmerfO2aCyCU5s7nJ/rpAepqmFifv/GCbSbueEeAJJnmSQ2rKC/g8Fcw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/spdy-transport/-/spdy-transport-3.0.0.tgz}
    name: spdy-transport
    version: 3.0.0
    dependencies:
      debug: registry.npmjs.org/debug@4.3.4
      detect-node: registry.npmjs.org/detect-node@2.1.0
      hpack.js: registry.npmjs.org/hpack.js@2.1.6
      obuf: registry.npmjs.org/obuf@1.1.2
      readable-stream: registry.npmjs.org/readable-stream@3.6.2
      wbuf: registry.npmjs.org/wbuf@1.7.3
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/spdy@4.0.2:
    resolution: {integrity: sha512-r46gZQZQV+Kl9oItvl1JZZqJKGr+oEkB08A6BzkiR7593/7IbtuncXHd2YoYeTsG4157ZssMu9KYvUHLcjcDoA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/spdy/-/spdy-4.0.2.tgz}
    name: spdy
    version: 4.0.2
    engines: {node: '>=6.0.0'}
    dependencies:
      debug: registry.npmjs.org/debug@4.3.4
      handle-thing: registry.npmjs.org/handle-thing@2.0.1
      http-deceiver: registry.npmjs.org/http-deceiver@1.2.7
      select-hose: registry.npmjs.org/select-hose@2.0.0
      spdy-transport: registry.npmjs.org/spdy-transport@3.0.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/sprintf-js@1.0.3:
    resolution: {integrity: sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz}
    name: sprintf-js
    version: 1.0.3
    dev: true

  registry.npmjs.org/ssri@8.0.1:
    resolution: {integrity: sha512-97qShzy1AiyxvPNIkLWoGua7xoQzzPjQ0HAH4B0rWKo7SZ6USuPcrUiAFrws0UH8RrbWmgq3LMTObhPIHbbBeQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ssri/-/ssri-8.0.1.tgz}
    name: ssri
    version: 8.0.1
    engines: {node: '>= 8'}
    dependencies:
      minipass: registry.npmjs.org/minipass@3.3.6
    dev: true

  registry.npmjs.org/stable@0.1.8:
    resolution: {integrity: sha512-ji9qxRnOVfcuLDySj9qzhGSEFVobyt1kIOSkj1qZzYLzq7Tos/oUUWvotUPQLlrsidqsK6tBH89Bc9kL5zHA6w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/stable/-/stable-0.1.8.tgz}
    name: stable
    version: 0.1.8
    deprecated: 'Modern JS already guarantees Array#sort() is a stable sort, so this library is deprecated. See the compatibility table on MDN: https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort#browser_compatibility'
    dev: true

  registry.npmjs.org/stackframe@1.3.4:
    resolution: {integrity: sha512-oeVtt7eWQS+Na6F//S4kJ2K2VbRlS9D43mAlMyVpVWovy9o+jfgH8O9agzANzaiLjclA0oYzUXEM4PurhSUChw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/stackframe/-/stackframe-1.3.4.tgz}
    name: stackframe
    version: 1.3.4
    dev: true

  registry.npmjs.org/statuses@1.5.0:
    resolution: {integrity: sha512-OpZ3zP+jT1PI7I8nemJX4AKmAX070ZkYPVWV/AaKTJl+tXCTGyVdC1a4SL8RUQYEwk/f34ZX8UTykN68FwrqAA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/statuses/-/statuses-1.5.0.tgz}
    name: statuses
    version: 1.5.0
    engines: {node: '>= 0.6'}
    dev: true

  registry.npmjs.org/statuses@2.0.1:
    resolution: {integrity: sha512-RwNA9Z/7PrK06rYLIzFMlaF+l73iwpzsqRIFgbMLbTcLD6cOao82TaWefPXQvB2fOC4AjuYSEndS7N/mTCbkdQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/statuses/-/statuses-2.0.1.tgz}
    name: statuses
    version: 2.0.1
    engines: {node: '>= 0.8'}
    dev: true

  registry.npmjs.org/string-width@2.1.1:
    resolution: {integrity: sha512-nOqH59deCq9SRHlxq1Aw85Jnt4w6KvLKqWVik6oA9ZklXLNIOlqg4F2yrT1MVaTjAqvVwdfeZ7w7aCvJD7ugkw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/string-width/-/string-width-2.1.1.tgz}
    name: string-width
    version: 2.1.1
    engines: {node: '>=4'}
    dependencies:
      is-fullwidth-code-point: registry.npmjs.org/is-fullwidth-code-point@2.0.0
      strip-ansi: registry.npmjs.org/strip-ansi@4.0.0
    dev: true

  registry.npmjs.org/string-width@4.2.3:
    resolution: {integrity: sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz}
    name: string-width
    version: 4.2.3
    engines: {node: '>=8'}
    dependencies:
      emoji-regex: registry.npmjs.org/emoji-regex@8.0.0
      is-fullwidth-code-point: registry.npmjs.org/is-fullwidth-code-point@3.0.0
      strip-ansi: registry.npmjs.org/strip-ansi@6.0.1
    dev: true

  registry.npmjs.org/string_decoder@1.1.1:
    resolution: {integrity: sha512-n/ShnvDi6FHbbVfviro+WojiFzv+s8MPMHBczVePfUpDJLwoLT0ht1l4YwBCbi8pJAveEEdnkHyPyTP/mzRfwg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/string_decoder/-/string_decoder-1.1.1.tgz}
    name: string_decoder
    version: 1.1.1
    dependencies:
      safe-buffer: registry.npmjs.org/safe-buffer@5.1.2
    dev: true

  registry.npmjs.org/string_decoder@1.3.0:
    resolution: {integrity: sha512-hkRX8U1WjJFd8LsDJ2yQ/wWWxaopEsABU1XfkM8A+j0+85JAGppt16cr1Whg6KIbb4okU6Mql6BOj+uup/wKeA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/string_decoder/-/string_decoder-1.3.0.tgz}
    name: string_decoder
    version: 1.3.0
    dependencies:
      safe-buffer: registry.npmjs.org/safe-buffer@5.2.1
    dev: true

  registry.npmjs.org/strip-ansi@4.0.0:
    resolution: {integrity: sha512-4XaJ2zQdCzROZDivEVIDPkcQn8LMFSa8kj8Gxb/Lnwzv9A8VctNZ+lfivC/sV3ivW8ElJTERXZoPBRrZKkNKow==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/strip-ansi/-/strip-ansi-4.0.0.tgz}
    name: strip-ansi
    version: 4.0.0
    engines: {node: '>=4'}
    dependencies:
      ansi-regex: registry.npmjs.org/ansi-regex@3.0.1
    dev: true

  registry.npmjs.org/strip-ansi@6.0.1:
    resolution: {integrity: sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz}
    name: strip-ansi
    version: 6.0.1
    engines: {node: '>=8'}
    dependencies:
      ansi-regex: registry.npmjs.org/ansi-regex@5.0.1
    dev: true

  registry.npmjs.org/strip-eof@1.0.0:
    resolution: {integrity: sha512-7FCwGGmx8mD5xQd3RPUvnSpUXHM3BWuzjtpD4TXsfcZ9EL4azvVVUscFYwD9nx8Kh+uCBC00XBtAykoMHwTh8Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/strip-eof/-/strip-eof-1.0.0.tgz}
    name: strip-eof
    version: 1.0.0
    engines: {node: '>=0.10.0'}
    dev: true

  registry.npmjs.org/strip-final-newline@2.0.0:
    resolution: {integrity: sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz}
    name: strip-final-newline
    version: 2.0.0
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/strip-indent@2.0.0:
    resolution: {integrity: sha512-RsSNPLpq6YUL7QYy44RnPVTn/lcVZtb48Uof3X5JLbF4zD/Gs7ZFDv2HWol+leoQN2mT86LAzSshGfkTlSOpsA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/strip-indent/-/strip-indent-2.0.0.tgz}
    name: strip-indent
    version: 2.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/strip-json-comments@3.1.1:
    resolution: {integrity: sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz}
    name: strip-json-comments
    version: 3.1.1
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/stylehacks@5.1.1(postcss@8.4.38):
    resolution: {integrity: sha512-sBpcd5Hx7G6seo7b1LkpttvTz7ikD0LlH5RmdcBNb6fFR0Fl7LQwHDFr300q4cwUqi+IYrFGmsIHieMBfnN/Bw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/stylehacks/-/stylehacks-5.1.1.tgz}
    id: registry.npmjs.org/stylehacks/5.1.1
    name: stylehacks
    version: 5.1.1
    engines: {node: ^10 || ^12 || >=14.0}
    peerDependencies:
      postcss: ^8.2.15
    dependencies:
      browserslist: registry.npmjs.org/browserslist@4.23.0
      postcss: registry.npmjs.org/postcss@8.4.38
      postcss-selector-parser: registry.npmjs.org/postcss-selector-parser@6.0.16
    dev: true

  registry.npmjs.org/supports-color@5.5.0:
    resolution: {integrity: sha512-QjVjwdXIt408MIiAqCX4oUKsgU2EqAGzs2Ppkm4aQYbjm+ZEWEcW4SfFNTr4uMNZma0ey4f5lgLrkB0aX0QMow==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/supports-color/-/supports-color-5.5.0.tgz}
    name: supports-color
    version: 5.5.0
    engines: {node: '>=4'}
    dependencies:
      has-flag: registry.npmjs.org/has-flag@3.0.0
    dev: true

  registry.npmjs.org/supports-color@7.2.0:
    resolution: {integrity: sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz}
    name: supports-color
    version: 7.2.0
    engines: {node: '>=8'}
    dependencies:
      has-flag: registry.npmjs.org/has-flag@4.0.0
    dev: true

  registry.npmjs.org/supports-color@8.1.1:
    resolution: {integrity: sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz}
    name: supports-color
    version: 8.1.1
    engines: {node: '>=10'}
    dependencies:
      has-flag: registry.npmjs.org/has-flag@4.0.0
    dev: true

  registry.npmjs.org/supports-preserve-symlinks-flag@1.0.0:
    resolution: {integrity: sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz}
    name: supports-preserve-symlinks-flag
    version: 1.0.0
    engines: {node: '>= 0.4'}
    dev: true

  registry.npmjs.org/svg-tags@1.0.0:
    resolution: {integrity: sha512-ovssysQTa+luh7A5Weu3Rta6FJlFBBbInjOh722LIt6klpU2/HtdUbszju/G4devcvk8PGt7FCLv5wftu3THUA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/svg-tags/-/svg-tags-1.0.0.tgz}
    name: svg-tags
    version: 1.0.0
    dev: true

  registry.npmjs.org/svgo@2.8.0:
    resolution: {integrity: sha512-+N/Q9kV1+F+UeWYoSiULYo4xYSDQlTgb+ayMobAXPwMnLvop7oxKMo9OzIrX5x3eS4L4f2UHhc9axXwY8DpChg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/svgo/-/svgo-2.8.0.tgz}
    name: svgo
    version: 2.8.0
    engines: {node: '>=10.13.0'}
    hasBin: true
    dependencies:
      '@trysound/sax': registry.npmjs.org/@trysound/sax@0.2.0
      commander: registry.npmjs.org/commander@7.2.0
      css-select: registry.npmjs.org/css-select@4.3.0
      css-tree: registry.npmjs.org/css-tree@1.1.3
      csso: registry.npmjs.org/csso@4.2.0
      picocolors: registry.npmjs.org/picocolors@1.0.0
      stable: registry.npmjs.org/stable@0.1.8
    dev: true

  registry.npmjs.org/table@6.8.2:
    resolution: {integrity: sha512-w2sfv80nrAh2VCbqR5AK27wswXhqcck2AhfnNW76beQXskGZ1V12GwS//yYVa3d3fcvAip2OUnbDAjW2k3v9fA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/table/-/table-6.8.2.tgz}
    name: table
    version: 6.8.2
    engines: {node: '>=10.0.0'}
    dependencies:
      ajv: registry.npmjs.org/ajv@8.12.0
      lodash.truncate: registry.npmjs.org/lodash.truncate@4.4.2
      slice-ansi: registry.npmjs.org/slice-ansi@4.0.0
      string-width: registry.npmjs.org/string-width@4.2.3
      strip-ansi: registry.npmjs.org/strip-ansi@6.0.1
    dev: true

  registry.npmjs.org/tapable@2.2.1:
    resolution: {integrity: sha512-GNzQvQTOIP6RyTfE2Qxb8ZVlNmw0n88vp1szwWRimP02mnTsx3Wtn5qRdqY9w2XduFNUgvOwhNnQsjwCp+kqaQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/tapable/-/tapable-2.2.1.tgz}
    name: tapable
    version: 2.2.1
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/terser-webpack-plugin@5.3.10(webpack@5.91.0):
    resolution: {integrity: sha512-BKFPWlPDndPs+NGGCr1U59t0XScL5317Y0UReNrHaw9/FwhPENlq6bfgs+4yPfyP51vqC1bQ4rp1EfXW5ZSH9w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/terser-webpack-plugin/-/terser-webpack-plugin-5.3.10.tgz}
    id: registry.npmjs.org/terser-webpack-plugin/5.3.10
    name: terser-webpack-plugin
    version: 5.3.10
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      '@swc/core': '*'
      esbuild: '*'
      uglify-js: '*'
      webpack: ^5.1.0
    peerDependenciesMeta:
      '@swc/core':
        optional: true
      esbuild:
        optional: true
      uglify-js:
        optional: true
    dependencies:
      '@jridgewell/trace-mapping': registry.npmjs.org/@jridgewell/trace-mapping@0.3.25
      jest-worker: registry.npmjs.org/jest-worker@27.5.1
      schema-utils: registry.npmjs.org/schema-utils@3.3.0
      serialize-javascript: registry.npmjs.org/serialize-javascript@6.0.2
      terser: registry.npmjs.org/terser@5.30.3
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/terser@5.30.3:
    resolution: {integrity: sha512-STdUgOUx8rLbMGO9IOwHLpCqolkDITFFQSMYYwKE1N2lY6MVSaeoi10z/EhWxRc6ybqoVmKSkhKYH/XUpl7vSA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/terser/-/terser-5.30.3.tgz}
    name: terser
    version: 5.30.3
    engines: {node: '>=10'}
    hasBin: true
    dependencies:
      '@jridgewell/source-map': registry.npmjs.org/@jridgewell/source-map@0.3.6
      acorn: registry.npmjs.org/acorn@8.11.3
      commander: registry.npmjs.org/commander@2.20.3
      source-map-support: registry.npmjs.org/source-map-support@0.5.21
    dev: true

  registry.npmjs.org/text-table@0.2.0:
    resolution: {integrity: sha512-N+8UisAXDGk8PFXP4HAzVR9nbfmVJ3zYLAWiTIoqC5v5isinhr+r5uaO8+7r3BMfuNIufIsA7RdpVgacC2cSpw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/text-table/-/text-table-0.2.0.tgz}
    name: text-table
    version: 0.2.0
    dev: true

  registry.npmjs.org/thenify-all@1.6.0:
    resolution: {integrity: sha512-RNxQH/qI8/t3thXJDwcstUO4zeqo64+Uy/+sNVRBx4Xn2OX+OZ9oP+iJnNFqplFra2ZUVeKCSa2oVWi3T4uVmA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/thenify-all/-/thenify-all-1.6.0.tgz}
    name: thenify-all
    version: 1.6.0
    engines: {node: '>=0.8'}
    dependencies:
      thenify: registry.npmjs.org/thenify@3.3.1
    dev: true

  registry.npmjs.org/thenify@3.3.1:
    resolution: {integrity: sha512-RVZSIV5IG10Hk3enotrhvz0T9em6cyHBLkH/YAZuKqd8hRkKhSfCGIcP2KUY0EPxndzANBmNllzWPwak+bheSw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/thenify/-/thenify-3.3.1.tgz}
    name: thenify
    version: 3.3.1
    dependencies:
      any-promise: registry.npmjs.org/any-promise@1.3.0
    dev: true

  registry.npmjs.org/thread-loader@3.0.4(webpack@5.91.0):
    resolution: {integrity: sha512-ByaL2TPb+m6yArpqQUZvP+5S1mZtXsEP7nWKKlAUTm7fCml8kB5s1uI3+eHRP2bk5mVYfRSBI7FFf+tWEyLZwA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/thread-loader/-/thread-loader-3.0.4.tgz}
    id: registry.npmjs.org/thread-loader/3.0.4
    name: thread-loader
    version: 3.0.4
    engines: {node: '>= 10.13.0'}
    peerDependencies:
      webpack: ^4.27.0 || ^5.0.0
    dependencies:
      json-parse-better-errors: registry.npmjs.org/json-parse-better-errors@1.0.2
      loader-runner: registry.npmjs.org/loader-runner@4.3.0
      loader-utils: registry.npmjs.org/loader-utils@2.0.4
      neo-async: registry.npmjs.org/neo-async@2.6.2
      schema-utils: registry.npmjs.org/schema-utils@3.3.0
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/throttle-debounce@1.1.0:
    resolution: {integrity: sha512-XH8UiPCQcWNuk2LYePibW/4qL97+ZQ1AN3FNXwZRBNPPowo/NRU5fAlDCSNBJIYCKbioZfuYtMhG4quqoJhVzg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/throttle-debounce/-/throttle-debounce-1.1.0.tgz}
    name: throttle-debounce
    version: 1.1.0
    engines: {node: '>=4'}
    dev: false

  registry.npmjs.org/thunky@1.1.0:
    resolution: {integrity: sha512-eHY7nBftgThBqOyHGVN+l8gF0BucP09fMo0oO/Lb0w1OF80dJv+lDVpXG60WMQvkcxAkNybKsrEIE3ZtKGmPrA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/thunky/-/thunky-1.1.0.tgz}
    name: thunky
    version: 1.1.0
    dev: true

  registry.npmjs.org/to-fast-properties@2.0.0:
    resolution: {integrity: sha512-/OaKK0xYrs3DmxRYqL/yDc+FxFUVYhDlXMhRmv3z915w2HF1tnN1omB354j8VUGO/hbRzyD6Y3sA7v7GS/ceog==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/to-fast-properties/-/to-fast-properties-2.0.0.tgz}
    name: to-fast-properties
    version: 2.0.0
    engines: {node: '>=4'}

  registry.npmjs.org/to-regex-range@5.0.1:
    resolution: {integrity: sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz}
    name: to-regex-range
    version: 5.0.1
    engines: {node: '>=8.0'}
    dependencies:
      is-number: registry.npmjs.org/is-number@7.0.0
    dev: true

  registry.npmjs.org/toidentifier@1.0.1:
    resolution: {integrity: sha512-o5sSPKEkg/DIQNmH43V0/uerLrpzVedkUh8tGNvaeXpfpuwjKenlSox/2O/BTlZUtEe+JG7s5YhEz608PlAHRA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/toidentifier/-/toidentifier-1.0.1.tgz}
    name: toidentifier
    version: 1.0.1
    engines: {node: '>=0.6'}
    dev: true

  registry.npmjs.org/totalist@3.0.1:
    resolution: {integrity: sha512-sf4i37nQ2LBx4m3wB74y+ubopq6W/dIzXg0FDGjsYnZHVa1Da8FH853wlL2gtUhg+xJXjfk3kUZS3BRoQeoQBQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/totalist/-/totalist-3.0.1.tgz}
    name: totalist
    version: 3.0.1
    engines: {node: '>=6'}
    dev: true

  registry.npmjs.org/tr46@0.0.3:
    resolution: {integrity: sha512-N3WMsuqV66lT30CrXNbEjx4GEwlow3v6rr4mCcv6prnfwhS01rkgyFdjPNBYd9br7LpXV1+Emh01fHnq2Gdgrw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/tr46/-/tr46-0.0.3.tgz}
    name: tr46
    version: 0.0.3
    dev: true

  registry.npmjs.org/tslib@2.6.2:
    resolution: {integrity: sha512-AEYxH93jGFPn/a2iVAwW87VuUIkR1FVUKB77NwMF7nBTDkDrrT/Hpt/IrCJ0QXhW27jTBDcf5ZY7w6RiqTMw2Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/tslib/-/tslib-2.6.2.tgz}
    name: tslib
    version: 2.6.2
    dev: true

  registry.npmjs.org/type-check@0.4.0:
    resolution: {integrity: sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz}
    name: type-check
    version: 0.4.0
    engines: {node: '>= 0.8.0'}
    dependencies:
      prelude-ls: registry.npmjs.org/prelude-ls@1.2.1
    dev: true

  registry.npmjs.org/type-fest@0.20.2:
    resolution: {integrity: sha512-Ne+eE4r0/iWnpAxD852z3A+N0Bt5RN//NjJwRd2VFHEmrywxf5vsZlh4R6lixl6B+wz/8d+maTSAkN1FIkI3LQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/type-fest/-/type-fest-0.20.2.tgz}
    name: type-fest
    version: 0.20.2
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/type-fest@0.6.0:
    resolution: {integrity: sha512-q+MB8nYR1KDLrgr4G5yemftpMC7/QLqVndBmEEdqzmNj5dcFOO4Oo8qlwZE3ULT3+Zim1F8Kq4cBnikNhlCMlg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/type-fest/-/type-fest-0.6.0.tgz}
    name: type-fest
    version: 0.6.0
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/type-fest@0.8.1:
    resolution: {integrity: sha512-4dbzIzqvjtgiM5rw1k5rEHtBANKmdudhGyBEajN01fEyhaAIhsoKNy6y7+IN93IfpFtwY9iqi7kD+xwKhQsNJA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/type-fest/-/type-fest-0.8.1.tgz}
    name: type-fest
    version: 0.8.1
    engines: {node: '>=8'}
    dev: true

  registry.npmjs.org/type-is@1.6.18:
    resolution: {integrity: sha512-TkRKr9sUTxEH8MdfuCSP7VizJyzRNMjj2J2do2Jr3Kym598JVdEksuzPQCnlFPW4ky9Q+iA+ma9BGm06XQBy8g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/type-is/-/type-is-1.6.18.tgz}
    name: type-is
    version: 1.6.18
    engines: {node: '>= 0.6'}
    dependencies:
      media-typer: registry.npmjs.org/media-typer@0.3.0
      mime-types: registry.npmjs.org/mime-types@2.1.35
    dev: true

  registry.npmjs.org/undici-types@5.26.5:
    resolution: {integrity: sha512-JlCMO+ehdEIKqlFxk6IfVoAUVmgz7cU7zD/h9XZ0qzeosSHmUJVOzSQvvYSYWXkFXC+IfLKSIffhv0sVZup6pA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/undici-types/-/undici-types-5.26.5.tgz}
    name: undici-types
    version: 5.26.5
    dev: true

  registry.npmjs.org/unicode-canonical-property-names-ecmascript@2.0.0:
    resolution: {integrity: sha512-yY5PpDlfVIU5+y/BSCxAJRBIS1Zc2dDG3Ujq+sR0U+JjUevW2JhocOF+soROYDSaAezOzOKuyyixhD6mBknSmQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/unicode-canonical-property-names-ecmascript/-/unicode-canonical-property-names-ecmascript-2.0.0.tgz}
    name: unicode-canonical-property-names-ecmascript
    version: 2.0.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/unicode-match-property-ecmascript@2.0.0:
    resolution: {integrity: sha512-5kaZCrbp5mmbz5ulBkDkbY0SsPOjKqVS35VpL9ulMPfSl0J0Xsm+9Evphv9CoIZFwre7aJoa94AY6seMKGVN5Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/unicode-match-property-ecmascript/-/unicode-match-property-ecmascript-2.0.0.tgz}
    name: unicode-match-property-ecmascript
    version: 2.0.0
    engines: {node: '>=4'}
    dependencies:
      unicode-canonical-property-names-ecmascript: registry.npmjs.org/unicode-canonical-property-names-ecmascript@2.0.0
      unicode-property-aliases-ecmascript: registry.npmjs.org/unicode-property-aliases-ecmascript@2.1.0
    dev: true

  registry.npmjs.org/unicode-match-property-value-ecmascript@2.1.0:
    resolution: {integrity: sha512-qxkjQt6qjg/mYscYMC0XKRn3Rh0wFPlfxB0xkt9CfyTvpX1Ra0+rAmdX2QyAobptSEvuy4RtpPRui6XkV+8wjA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/unicode-match-property-value-ecmascript/-/unicode-match-property-value-ecmascript-2.1.0.tgz}
    name: unicode-match-property-value-ecmascript
    version: 2.1.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/unicode-property-aliases-ecmascript@2.1.0:
    resolution: {integrity: sha512-6t3foTQI9qne+OZoVQB/8x8rk2k1eVy1gRXhV3oFQ5T6R1dqQ1xtin3XqSlx3+ATBkliTaR/hHyJBm+LVPNM8w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/unicode-property-aliases-ecmascript/-/unicode-property-aliases-ecmascript-2.1.0.tgz}
    name: unicode-property-aliases-ecmascript
    version: 2.1.0
    engines: {node: '>=4'}
    dev: true

  registry.npmjs.org/universalify@2.0.1:
    resolution: {integrity: sha512-gptHNQghINnc/vTGIk0SOFGFNXw7JVrlRUtConJRlvaw6DuX0wO5Jeko9sWrMBhh+PsYAZ7oXAiOnf/UKogyiw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/universalify/-/universalify-2.0.1.tgz}
    name: universalify
    version: 2.0.1
    engines: {node: '>= 10.0.0'}
    dev: true

  registry.npmjs.org/unpipe@1.0.0:
    resolution: {integrity: sha512-pjy2bYhSsufwWlKwPc+l3cN7+wuJlK6uz0YdJEOlQDbl6jo/YlPi4mb8agUkVC8BF7V8NuzeyPNqRksA3hztKQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/unpipe/-/unpipe-1.0.0.tgz}
    name: unpipe
    version: 1.0.0
    engines: {node: '>= 0.8'}
    dev: true

  registry.npmjs.org/update-browserslist-db@1.0.13(browserslist@4.23.0):
    resolution: {integrity: sha512-xebP81SNcPuNpPP3uzeW1NYXxI3rxyJzF3pD6sH4jE7o/IX+WtSpwnVU+qIsDPyk0d3hmFQ7mjqc6AtV604hbg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.0.13.tgz}
    id: registry.npmjs.org/update-browserslist-db/1.0.13
    name: update-browserslist-db
    version: 1.0.13
    hasBin: true
    peerDependencies:
      browserslist: '>= 4.21.0'
    dependencies:
      browserslist: registry.npmjs.org/browserslist@4.23.0
      escalade: registry.npmjs.org/escalade@3.1.2
      picocolors: registry.npmjs.org/picocolors@1.0.0
    dev: true

  registry.npmjs.org/uri-js@4.4.1:
    resolution: {integrity: sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz}
    name: uri-js
    version: 4.4.1
    dependencies:
      punycode: registry.npmjs.org/punycode@2.3.1
    dev: true

  registry.npmjs.org/util-deprecate@1.0.2:
    resolution: {integrity: sha512-EPD5q1uXyFxJpCrLnCc1nHnq3gOa6DZBocAIiI2TaSCA7VCJ1UJDMagCzIkXNsUYfD1daK//LTEQ8xiIbrHtcw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/util-deprecate/-/util-deprecate-1.0.2.tgz}
    name: util-deprecate
    version: 1.0.2
    dev: true

  registry.npmjs.org/utila@0.4.0:
    resolution: {integrity: sha512-Z0DbgELS9/L/75wZbro8xAnT50pBVFQZ+hUEueGDU5FN51YSCYM+jdxsfCiHjwNP/4LCDD0i/graKpeBnOXKRA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/utila/-/utila-0.4.0.tgz}
    name: utila
    version: 0.4.0
    dev: true

  registry.npmjs.org/utils-merge@1.0.1:
    resolution: {integrity: sha512-pMZTvIkT1d+TFGvDOqodOclx0QWkkgi6Tdoa8gC8ffGAAqz9pzPTZWAybbsHHoED/ztMtkv/VoYTYyShUn81hA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/utils-merge/-/utils-merge-1.0.1.tgz}
    name: utils-merge
    version: 1.0.1
    engines: {node: '>= 0.4.0'}
    dev: true

  registry.npmjs.org/uuid@8.3.2:
    resolution: {integrity: sha512-+NYs2QeMWy+GWFOEm9xnn6HCDp0l7QBD7ml8zLUmJ+93Q5NF0NocErnwkTkXVFNiX3/fpC6afS8Dhb/gz7R7eg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/uuid/-/uuid-8.3.2.tgz}
    name: uuid
    version: 8.3.2
    hasBin: true
    dev: true

  registry.npmjs.org/v8-compile-cache@2.4.0:
    resolution: {integrity: sha512-ocyWc3bAHBB/guyqJQVI5o4BZkPhznPYUG2ea80Gond/BgNWpap8TOmLSeeQG7bnh2KMISxskdADG59j7zruhw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/v8-compile-cache/-/v8-compile-cache-2.4.0.tgz}
    name: v8-compile-cache
    version: 2.4.0
    dev: true

  registry.npmjs.org/validate-npm-package-license@3.0.4:
    resolution: {integrity: sha512-DpKm2Ui/xN7/HQKCtpZxoRWBhZ9Z0kqtygG8XCgNQ8ZlDnxuQmWhj566j8fN4Cu3/JmbhsDo7fcAJq4s9h27Ew==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/validate-npm-package-license/-/validate-npm-package-license-3.0.4.tgz}
    name: validate-npm-package-license
    version: 3.0.4
    dependencies:
      spdx-correct: registry.npmjs.org/spdx-correct@3.2.0
      spdx-expression-parse: registry.npmjs.org/spdx-expression-parse@3.0.1
    dev: true

  registry.npmjs.org/vary@1.1.2:
    resolution: {integrity: sha512-BNGbWLfd0eUPabhkXUVm0j8uuvREyTh5ovRa/dyow/BqAbZJyC+5fU+IzQOzmAKzYqYRAISoRhdQr3eIZ/PXqg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/vary/-/vary-1.1.2.tgz}
    name: vary
    version: 1.1.2
    engines: {node: '>= 0.8'}
    dev: true

  registry.npmjs.org/vue-demi@0.14.7(vue@2.7.16):
    resolution: {integrity: sha512-EOG8KXDQNwkJILkx/gPcoL/7vH+hORoBaKgGe+6W7VFMvCYJfmF2dGbvgDroVnI8LU7/kTu8mbjRZGBU1z9NTA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/vue-demi/-/vue-demi-0.14.7.tgz}
    id: registry.npmjs.org/vue-demi/0.14.7
    name: vue-demi
    version: 0.14.7
    engines: {node: '>=12'}
    hasBin: true
    requiresBuild: true
    peerDependencies:
      '@vue/composition-api': ^1.0.0-rc.1
      vue: ^3.0.0-0 || ^2.6.0
    peerDependenciesMeta:
      '@vue/composition-api':
        optional: true
    dependencies:
      vue: registry.npmjs.org/vue@2.7.16
    dev: false

  registry.npmjs.org/vue-eslint-parser@8.3.0(eslint@7.32.0):
    resolution: {integrity: sha512-dzHGG3+sYwSf6zFBa0Gi9ZDshD7+ad14DGOdTLjruRVgZXe2J+DcZ9iUhyR48z5g1PqRa20yt3Njna/veLJL/g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/vue-eslint-parser/-/vue-eslint-parser-8.3.0.tgz}
    id: registry.npmjs.org/vue-eslint-parser/8.3.0
    name: vue-eslint-parser
    version: 8.3.0
    engines: {node: ^12.22.0 || ^14.17.0 || >=16.0.0}
    peerDependencies:
      eslint: '>=6.0.0'
    dependencies:
      debug: registry.npmjs.org/debug@4.3.4
      eslint: registry.npmjs.org/eslint@7.32.0
      eslint-scope: registry.npmjs.org/eslint-scope@7.2.2
      eslint-visitor-keys: registry.npmjs.org/eslint-visitor-keys@3.4.3
      espree: registry.npmjs.org/espree@9.6.1
      esquery: registry.npmjs.org/esquery@1.5.0
      lodash: registry.npmjs.org/lodash@4.17.21
      semver: registry.npmjs.org/semver@7.6.0
    transitivePeerDependencies:
      - supports-color
    dev: true

  registry.npmjs.org/vue-hot-reload-api@2.3.4:
    resolution: {integrity: sha512-BXq3jwIagosjgNVae6tkHzzIk6a8MHFtzAdwhnV5VlvPTFxDCvIttgSiHWjdGoTJvXtmRu5HacExfdarRcFhog==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/vue-hot-reload-api/-/vue-hot-reload-api-2.3.4.tgz}
    name: vue-hot-reload-api
    version: 2.3.4
    dev: true

  registry.npmjs.org/vue-loader@15.11.1(css-loader@6.11.0)(vue-template-compiler@2.7.16)(webpack@5.91.0):
    resolution: {integrity: sha512-0iw4VchYLePqJfJu9s62ACWUXeSqM30SQqlIftbYWM3C+jpPcEHKSPUZBLjSF9au4HTHQ/naF6OGnO3Q/qGR3Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/vue-loader/-/vue-loader-15.11.1.tgz}
    id: registry.npmjs.org/vue-loader/15.11.1
    name: vue-loader
    version: 15.11.1
    peerDependencies:
      '@vue/compiler-sfc': ^3.0.8
      cache-loader: '*'
      css-loader: '*'
      prettier: '*'
      vue-template-compiler: '*'
      webpack: ^3.0.0 || ^4.1.0 || ^5.0.0-0
    peerDependenciesMeta:
      '@vue/compiler-sfc':
        optional: true
      cache-loader:
        optional: true
      prettier:
        optional: true
      vue-template-compiler:
        optional: true
    dependencies:
      '@vue/component-compiler-utils': registry.npmjs.org/@vue/component-compiler-utils@3.3.0
      css-loader: registry.npmjs.org/css-loader@6.11.0(webpack@5.91.0)
      hash-sum: registry.npmjs.org/hash-sum@1.0.2
      loader-utils: registry.npmjs.org/loader-utils@1.4.2
      vue-hot-reload-api: registry.npmjs.org/vue-hot-reload-api@2.3.4
      vue-style-loader: registry.npmjs.org/vue-style-loader@4.1.3
      vue-template-compiler: registry.npmjs.org/vue-template-compiler@2.7.16
      webpack: registry.npmjs.org/webpack@5.91.0
    transitivePeerDependencies:
      - arc-templates
      - atpl
      - babel-core
      - bracket-template
      - coffee-script
      - dot
      - dust
      - dustjs-helpers
      - dustjs-linkedin
      - eco
      - ect
      - ejs
      - haml-coffee
      - hamlet
      - hamljs
      - handlebars
      - hogan.js
      - htmling
      - jade
      - jazz
      - jqtpl
      - just
      - liquid-node
      - liquor
      - lodash
      - marko
      - mote
      - mustache
      - nunjucks
      - plates
      - pug
      - qejs
      - ractive
      - razor-tmpl
      - react
      - react-dom
      - slm
      - squirrelly
      - swig
      - swig-templates
      - teacup
      - templayed
      - then-jade
      - then-pug
      - tinyliquid
      - toffee
      - twig
      - twing
      - underscore
      - vash
      - velocityjs
      - walrus
      - whiskers
    dev: true

  registry.npmjs.org/vue-loader@17.4.2(vue@2.7.16)(webpack@5.91.0):
    resolution: {integrity: sha512-yTKOA4R/VN4jqjw4y5HrynFL8AK0Z3/Jt7eOJXEitsm0GMRHDBjCfCiuTiLP7OESvsZYo2pATCWhDqxC5ZrM6w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/vue-loader/-/vue-loader-17.4.2.tgz}
    id: registry.npmjs.org/vue-loader/17.4.2
    name: vue-loader
    version: 17.4.2
    peerDependencies:
      '@vue/compiler-sfc': '*'
      vue: '*'
      webpack: ^4.1.0 || ^5.0.0-0
    peerDependenciesMeta:
      '@vue/compiler-sfc':
        optional: true
      vue:
        optional: true
    dependencies:
      chalk: registry.npmjs.org/chalk@4.1.2
      hash-sum: registry.npmjs.org/hash-sum@2.0.0
      vue: registry.npmjs.org/vue@2.7.16
      watchpack: registry.npmjs.org/watchpack@2.4.1
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/vue-router@3.6.5(vue@2.7.16):
    resolution: {integrity: sha512-VYXZQLtjuvKxxcshuRAwjHnciqZVoXAjTjcqBTz4rKc8qih9g9pI3hbDjmqXaHdgL3v8pV6P8Z335XvHzESxLQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/vue-router/-/vue-router-3.6.5.tgz}
    id: registry.npmjs.org/vue-router/3.6.5
    name: vue-router
    version: 3.6.5
    peerDependencies:
      vue: ^2
    dependencies:
      vue: registry.npmjs.org/vue@2.7.16
    dev: false

  registry.npmjs.org/vue-style-loader@4.1.3:
    resolution: {integrity: sha512-sFuh0xfbtpRlKfm39ss/ikqs9AbKCoXZBpHeVZ8Tx650o0k0q/YCM7FRvigtxpACezfq6af+a7JeqVTWvncqDg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/vue-style-loader/-/vue-style-loader-4.1.3.tgz}
    name: vue-style-loader
    version: 4.1.3
    dependencies:
      hash-sum: registry.npmjs.org/hash-sum@1.0.2
      loader-utils: registry.npmjs.org/loader-utils@1.4.2
    dev: true

  registry.npmjs.org/vue-template-compiler@2.7.16:
    resolution: {integrity: sha512-AYbUWAJHLGGQM7+cNTELw+KsOG9nl2CnSv467WobS5Cv9uk3wFcnr1Etsz2sEIHEZvw1U+o9mRlEO6QbZvUPGQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/vue-template-compiler/-/vue-template-compiler-2.7.16.tgz}
    name: vue-template-compiler
    version: 2.7.16
    dependencies:
      de-indent: registry.npmjs.org/de-indent@1.0.2
      he: registry.npmjs.org/he@1.2.0
    dev: true

  registry.npmjs.org/vue-template-es2015-compiler@1.9.1:
    resolution: {integrity: sha512-4gDntzrifFnCEvyoO8PqyJDmguXgVPxKiIxrBKjIowvL9l+N66196+72XVYR8BBf1Uv1Fgt3bGevJ+sEmxfZzw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/vue-template-es2015-compiler/-/vue-template-es2015-compiler-1.9.1.tgz}
    name: vue-template-es2015-compiler
    version: 1.9.1
    dev: true

  registry.npmjs.org/vue@2.7.16:
    resolution: {integrity: sha512-4gCtFXaAA3zYZdTp5s4Hl2sozuySsgz4jy1EnpBHNfpMa9dK1ZCG7viqBPCwXtmgc8nHqUsAu3G4gtmXkkY3Sw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/vue/-/vue-2.7.16.tgz}
    name: vue
    version: 2.7.16
    deprecated: Vue 2 has reached EOL and is no longer actively maintained. See https://v2.vuejs.org/eol/ for more details.
    dependencies:
      '@vue/compiler-sfc': registry.npmjs.org/@vue/compiler-sfc@2.7.16
      csstype: registry.npmjs.org/csstype@3.1.3

  registry.npmjs.org/watchpack@2.4.1:
    resolution: {integrity: sha512-8wrBCMtVhqcXP2Sup1ctSkga6uc2Bx0IIvKyT7yTFier5AXHooSI+QyQQAtTb7+E0IUCCKyTFmXqdqgum2XWGg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/watchpack/-/watchpack-2.4.1.tgz}
    name: watchpack
    version: 2.4.1
    engines: {node: '>=10.13.0'}
    dependencies:
      glob-to-regexp: registry.npmjs.org/glob-to-regexp@0.4.1
      graceful-fs: registry.npmjs.org/graceful-fs@4.2.11
    dev: true

  registry.npmjs.org/wbuf@1.7.3:
    resolution: {integrity: sha512-O84QOnr0icsbFGLS0O3bI5FswxzRr8/gHwWkDlQFskhSPryQXvrTMxjxGP4+iWYoauLoBvfDpkrOauZ+0iZpDA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/wbuf/-/wbuf-1.7.3.tgz}
    name: wbuf
    version: 1.7.3
    dependencies:
      minimalistic-assert: registry.npmjs.org/minimalistic-assert@1.0.1
    dev: true

  registry.npmjs.org/wcwidth@1.0.1:
    resolution: {integrity: sha512-XHPEwS0q6TaxcvG85+8EYkbiCux2XtWG2mkc47Ng2A77BQu9+DqIOJldST4HgPkuea7dvKSj5VgX3P1d4rW8Tg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/wcwidth/-/wcwidth-1.0.1.tgz}
    name: wcwidth
    version: 1.0.1
    dependencies:
      defaults: registry.npmjs.org/defaults@1.0.4
    dev: true

  registry.npmjs.org/webidl-conversions@3.0.1:
    resolution: {integrity: sha512-2JAn3z8AR6rjK8Sm8orRC0h/bcl/DqL7tRPdGZ4I1CjdF+EaMLmYxBHyXuKL849eucPFhvBoxMsflfOb8kxaeQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/webidl-conversions/-/webidl-conversions-3.0.1.tgz}
    name: webidl-conversions
    version: 3.0.1
    dev: true

  registry.npmjs.org/webpack-bundle-analyzer@4.10.2:
    resolution: {integrity: sha512-vJptkMm9pk5si4Bv922ZbKLV8UTT4zib4FPgXMhgzUny0bfDDkLXAVQs3ly3fS4/TN9ROFtb0NFrm04UXFE/Vw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/webpack-bundle-analyzer/-/webpack-bundle-analyzer-4.10.2.tgz}
    name: webpack-bundle-analyzer
    version: 4.10.2
    engines: {node: '>= 10.13.0'}
    hasBin: true
    dependencies:
      '@discoveryjs/json-ext': registry.npmjs.org/@discoveryjs/json-ext@0.5.7
      acorn: registry.npmjs.org/acorn@8.11.3
      acorn-walk: registry.npmjs.org/acorn-walk@8.3.2
      commander: registry.npmjs.org/commander@7.2.0
      debounce: registry.npmjs.org/debounce@1.2.1
      escape-string-regexp: registry.npmjs.org/escape-string-regexp@4.0.0
      gzip-size: registry.npmjs.org/gzip-size@6.0.0
      html-escaper: registry.npmjs.org/html-escaper@2.0.2
      opener: registry.npmjs.org/opener@1.5.2
      picocolors: registry.npmjs.org/picocolors@1.0.0
      sirv: registry.npmjs.org/sirv@2.0.4
      ws: registry.npmjs.org/ws@7.5.9
    transitivePeerDependencies:
      - bufferutil
      - utf-8-validate
    dev: true

  registry.npmjs.org/webpack-chain@6.5.1:
    resolution: {integrity: sha512-7doO/SRtLu8q5WM0s7vPKPWX580qhi0/yBHkOxNkv50f6qB76Zy9o2wRTrrPULqYTvQlVHuvbA8v+G5ayuUDsA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/webpack-chain/-/webpack-chain-6.5.1.tgz}
    name: webpack-chain
    version: 6.5.1
    engines: {node: '>=8'}
    deprecated: Package no longer supported. Contact Support at https://www.npmjs.com/support for more info.
    dependencies:
      deepmerge: registry.npmjs.org/deepmerge@1.5.2
      javascript-stringify: registry.npmjs.org/javascript-stringify@2.1.0
    dev: true

  registry.npmjs.org/webpack-dev-middleware@5.3.4(webpack@5.91.0):
    resolution: {integrity: sha512-BVdTqhhs+0IfoeAf7EoH5WE+exCmqGerHfDM0IL096Px60Tq2Mn9MAbnaGUe6HiMa41KMCYF19gyzZmBcq/o4Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/webpack-dev-middleware/-/webpack-dev-middleware-5.3.4.tgz}
    id: registry.npmjs.org/webpack-dev-middleware/5.3.4
    name: webpack-dev-middleware
    version: 5.3.4
    engines: {node: '>= 12.13.0'}
    peerDependencies:
      webpack: ^4.0.0 || ^5.0.0
    dependencies:
      colorette: registry.npmjs.org/colorette@2.0.20
      memfs: registry.npmjs.org/memfs@3.5.3
      mime-types: registry.npmjs.org/mime-types@2.1.35
      range-parser: registry.npmjs.org/range-parser@1.2.1
      schema-utils: registry.npmjs.org/schema-utils@4.2.0
      webpack: registry.npmjs.org/webpack@5.91.0
    dev: true

  registry.npmjs.org/webpack-dev-server@4.15.2(debug@4.3.4)(webpack@5.91.0):
    resolution: {integrity: sha512-0XavAZbNJ5sDrCbkpWL8mia0o5WPOd2YGtxrEiZkBK9FjLppIUK2TgxK6qGD2P3hUXTJNNPVibrerKcx5WkR1g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/webpack-dev-server/-/webpack-dev-server-4.15.2.tgz}
    id: registry.npmjs.org/webpack-dev-server/4.15.2
    name: webpack-dev-server
    version: 4.15.2
    engines: {node: '>= 12.13.0'}
    hasBin: true
    peerDependencies:
      webpack: ^4.37.0 || ^5.0.0
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack:
        optional: true
      webpack-cli:
        optional: true
    dependencies:
      '@types/bonjour': registry.npmjs.org/@types/bonjour@3.5.13
      '@types/connect-history-api-fallback': registry.npmjs.org/@types/connect-history-api-fallback@1.5.4
      '@types/express': registry.npmjs.org/@types/express@4.17.21
      '@types/serve-index': registry.npmjs.org/@types/serve-index@1.9.4
      '@types/serve-static': registry.npmjs.org/@types/serve-static@1.15.7
      '@types/sockjs': registry.npmjs.org/@types/sockjs@0.3.36
      '@types/ws': registry.npmjs.org/@types/ws@8.5.10
      ansi-html-community: registry.npmjs.org/ansi-html-community@0.0.8
      bonjour-service: registry.npmjs.org/bonjour-service@1.2.1
      chokidar: registry.npmjs.org/chokidar@3.6.0
      colorette: registry.npmjs.org/colorette@2.0.20
      compression: registry.npmjs.org/compression@1.7.4
      connect-history-api-fallback: registry.npmjs.org/connect-history-api-fallback@2.0.0
      default-gateway: registry.npmjs.org/default-gateway@6.0.3
      express: registry.npmjs.org/express@4.19.2
      graceful-fs: registry.npmjs.org/graceful-fs@4.2.11
      html-entities: registry.npmjs.org/html-entities@2.5.2
      http-proxy-middleware: registry.npmjs.org/http-proxy-middleware@2.0.6(@types/express@4.17.21)(debug@4.3.4)
      ipaddr.js: registry.npmjs.org/ipaddr.js@2.1.0
      launch-editor: registry.npmjs.org/launch-editor@2.6.1
      open: registry.npmjs.org/open@8.4.2
      p-retry: registry.npmjs.org/p-retry@4.6.2
      rimraf: registry.npmjs.org/rimraf@3.0.2
      schema-utils: registry.npmjs.org/schema-utils@4.2.0
      selfsigned: registry.npmjs.org/selfsigned@2.4.1
      serve-index: registry.npmjs.org/serve-index@1.9.1
      sockjs: registry.npmjs.org/sockjs@0.3.24
      spdy: registry.npmjs.org/spdy@4.0.2
      webpack: registry.npmjs.org/webpack@5.91.0
      webpack-dev-middleware: registry.npmjs.org/webpack-dev-middleware@5.3.4(webpack@5.91.0)
      ws: registry.npmjs.org/ws@8.16.0
    transitivePeerDependencies:
      - bufferutil
      - debug
      - supports-color
      - utf-8-validate
    dev: true

  registry.npmjs.org/webpack-merge@5.10.0:
    resolution: {integrity: sha512-+4zXKdx7UnO+1jaN4l2lHVD+mFvnlZQP/6ljaJVb4SZiwIKeUnrT5l0gkT8z+n4hKpC+jpOv6O9R+gLtag7pSA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/webpack-merge/-/webpack-merge-5.10.0.tgz}
    name: webpack-merge
    version: 5.10.0
    engines: {node: '>=10.0.0'}
    dependencies:
      clone-deep: registry.npmjs.org/clone-deep@4.0.1
      flat: registry.npmjs.org/flat@5.0.2
      wildcard: registry.npmjs.org/wildcard@2.0.1
    dev: true

  registry.npmjs.org/webpack-sources@3.2.3:
    resolution: {integrity: sha512-/DyMEOrDgLKKIG0fmvtz+4dUX/3Ghozwgm6iPp8KRhvn+eQf9+Q7GWxVNMk3+uCPWfdXYC4ExGBckIXdFEfH1w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/webpack-sources/-/webpack-sources-3.2.3.tgz}
    name: webpack-sources
    version: 3.2.3
    engines: {node: '>=10.13.0'}
    dev: true

  registry.npmjs.org/webpack-virtual-modules@0.4.6:
    resolution: {integrity: sha512-5tyDlKLqPfMqjT3Q9TAqf2YqjwmnUleZwzJi1A5qXnlBCdj2AtOJ6wAWdglTIDOPgOiOrXeBeFcsQ8+aGQ6QbA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/webpack-virtual-modules/-/webpack-virtual-modules-0.4.6.tgz}
    name: webpack-virtual-modules
    version: 0.4.6
    dev: true

  registry.npmjs.org/webpack@5.91.0:
    resolution: {integrity: sha512-rzVwlLeBWHJbmgTC/8TvAcu5vpJNII+MelQpylD4jNERPwpBJOE2lEcko1zJX3QJeLjTTAnQxn/OJ8bjDzVQaw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/webpack/-/webpack-5.91.0.tgz}
    name: webpack
    version: 5.91.0
    engines: {node: '>=10.13.0'}
    hasBin: true
    peerDependencies:
      webpack-cli: '*'
    peerDependenciesMeta:
      webpack-cli:
        optional: true
    dependencies:
      '@types/eslint-scope': registry.npmjs.org/@types/eslint-scope@3.7.7
      '@types/estree': registry.npmjs.org/@types/estree@1.0.5
      '@webassemblyjs/ast': registry.npmjs.org/@webassemblyjs/ast@1.12.1
      '@webassemblyjs/wasm-edit': registry.npmjs.org/@webassemblyjs/wasm-edit@1.12.1
      '@webassemblyjs/wasm-parser': registry.npmjs.org/@webassemblyjs/wasm-parser@1.12.1
      acorn: registry.npmjs.org/acorn@8.11.3
      acorn-import-assertions: registry.npmjs.org/acorn-import-assertions@1.9.0(acorn@8.11.3)
      browserslist: registry.npmjs.org/browserslist@4.23.0
      chrome-trace-event: registry.npmjs.org/chrome-trace-event@1.0.3
      enhanced-resolve: registry.npmjs.org/enhanced-resolve@5.16.0
      es-module-lexer: registry.npmjs.org/es-module-lexer@1.5.0
      eslint-scope: registry.npmjs.org/eslint-scope@5.1.1
      events: registry.npmjs.org/events@3.3.0
      glob-to-regexp: registry.npmjs.org/glob-to-regexp@0.4.1
      graceful-fs: registry.npmjs.org/graceful-fs@4.2.11
      json-parse-even-better-errors: registry.npmjs.org/json-parse-even-better-errors@2.3.1
      loader-runner: registry.npmjs.org/loader-runner@4.3.0
      mime-types: registry.npmjs.org/mime-types@2.1.35
      neo-async: registry.npmjs.org/neo-async@2.6.2
      schema-utils: registry.npmjs.org/schema-utils@3.3.0
      tapable: registry.npmjs.org/tapable@2.2.1
      terser-webpack-plugin: registry.npmjs.org/terser-webpack-plugin@5.3.10(webpack@5.91.0)
      watchpack: registry.npmjs.org/watchpack@2.4.1
      webpack-sources: registry.npmjs.org/webpack-sources@3.2.3
    transitivePeerDependencies:
      - '@swc/core'
      - esbuild
      - uglify-js
    dev: true

  registry.npmjs.org/websocket-driver@0.7.4:
    resolution: {integrity: sha512-b17KeDIQVjvb0ssuSDF2cYXSg2iztliJ4B9WdsuB6J952qCPKmnVq4DyW5motImXHDC1cBT/1UezrJVsKw5zjg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/websocket-driver/-/websocket-driver-0.7.4.tgz}
    name: websocket-driver
    version: 0.7.4
    engines: {node: '>=0.8.0'}
    dependencies:
      http-parser-js: registry.npmjs.org/http-parser-js@0.5.8
      safe-buffer: registry.npmjs.org/safe-buffer@5.2.1
      websocket-extensions: registry.npmjs.org/websocket-extensions@0.1.4
    dev: true

  registry.npmjs.org/websocket-extensions@0.1.4:
    resolution: {integrity: sha512-OqedPIGOfsDlo31UNwYbCFMSaO9m9G/0faIHj5/dZFDMFqPTcx6UwqyOy3COEaEOg/9VsGIpdqn62W5KhoKSpg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/websocket-extensions/-/websocket-extensions-0.1.4.tgz}
    name: websocket-extensions
    version: 0.1.4
    engines: {node: '>=0.8.0'}
    dev: true

  registry.npmjs.org/whatwg-fetch@3.6.20:
    resolution: {integrity: sha512-EqhiFU6daOA8kpjOWTL0olhVOF3i7OrFzSYiGsEMB8GcXS+RrzauAERX65xMeNWVqxA6HXH2m69Z9LaKKdisfg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/whatwg-fetch/-/whatwg-fetch-3.6.20.tgz}
    name: whatwg-fetch
    version: 3.6.20
    dev: true

  registry.npmjs.org/whatwg-url@5.0.0:
    resolution: {integrity: sha512-saE57nupxk6v3HY35+jzBwYa0rKSy0XR8JSxZPwgLr7ys0IBzhGviA1/TUGJLmSVqs8pb9AnvICXEuOHLprYTw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/whatwg-url/-/whatwg-url-5.0.0.tgz}
    name: whatwg-url
    version: 5.0.0
    dependencies:
      tr46: registry.npmjs.org/tr46@0.0.3
      webidl-conversions: registry.npmjs.org/webidl-conversions@3.0.1
    dev: true

  registry.npmjs.org/which@1.3.1:
    resolution: {integrity: sha512-HxJdYWq1MTIQbJ3nw0cqssHoTNU267KlrDuGZ1WYlxDStUtKUhOaJmh112/TZmHxxUfuJqPXSOm7tDyas0OSIQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/which/-/which-1.3.1.tgz}
    name: which
    version: 1.3.1
    hasBin: true
    dependencies:
      isexe: registry.npmjs.org/isexe@2.0.0
    dev: true

  registry.npmjs.org/which@2.0.2:
    resolution: {integrity: sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/which/-/which-2.0.2.tgz}
    name: which
    version: 2.0.2
    engines: {node: '>= 8'}
    hasBin: true
    dependencies:
      isexe: registry.npmjs.org/isexe@2.0.0
    dev: true

  registry.npmjs.org/wildcard@2.0.1:
    resolution: {integrity: sha512-CC1bOL87PIWSBhDcTrdeLo6eGT7mCFtrg0uIJtqJUFyK+eJnzl8A1niH56uu7KMa5XFrtiV+AQuHO3n7DsHnLQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/wildcard/-/wildcard-2.0.1.tgz}
    name: wildcard
    version: 2.0.1
    dev: true

  registry.npmjs.org/wrap-ansi@3.0.1:
    resolution: {integrity: sha512-iXR3tDXpbnTpzjKSylUJRkLuOrEC7hwEB221cgn6wtF8wpmz28puFXAEfPT5zrjM3wahygB//VuWEr1vTkDcNQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-3.0.1.tgz}
    name: wrap-ansi
    version: 3.0.1
    engines: {node: '>=4'}
    dependencies:
      string-width: registry.npmjs.org/string-width@2.1.1
      strip-ansi: registry.npmjs.org/strip-ansi@4.0.0
    dev: true

  registry.npmjs.org/wrap-ansi@7.0.0:
    resolution: {integrity: sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz}
    name: wrap-ansi
    version: 7.0.0
    engines: {node: '>=10'}
    dependencies:
      ansi-styles: registry.npmjs.org/ansi-styles@4.3.0
      string-width: registry.npmjs.org/string-width@4.2.3
      strip-ansi: registry.npmjs.org/strip-ansi@6.0.1
    dev: true

  registry.npmjs.org/wrappy@1.0.2:
    resolution: {integrity: sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz}
    name: wrappy
    version: 1.0.2
    dev: true

  registry.npmjs.org/ws@7.5.9:
    resolution: {integrity: sha512-F+P9Jil7UiSKSkppIiD94dN07AwvFixvLIj1Og1Rl9GGMuNipJnV9JzjD6XuqmAeiswGvUmNLjr5cFuXwNS77Q==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ws/-/ws-7.5.9.tgz}
    name: ws
    version: 7.5.9
    engines: {node: '>=8.3.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: ^5.0.2
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true
    dev: true

  registry.npmjs.org/ws@8.16.0:
    resolution: {integrity: sha512-HS0c//TP7Ina87TfiPUz1rQzMhHrl/SG2guqRcTOIUYD2q8uhUdNHZYJUaQ8aTGPzCh+c6oawMKW35nFl1dxyQ==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/ws/-/ws-8.16.0.tgz}
    name: ws
    version: 8.16.0
    engines: {node: '>=10.0.0'}
    peerDependencies:
      bufferutil: ^4.0.1
      utf-8-validate: '>=5.0.2'
    peerDependenciesMeta:
      bufferutil:
        optional: true
      utf-8-validate:
        optional: true
    dev: true

  registry.npmjs.org/y18n@5.0.8:
    resolution: {integrity: sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz}
    name: y18n
    version: 5.0.8
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/yallist@2.1.2:
    resolution: {integrity: sha512-ncTzHV7NvsQZkYe1DW7cbDLm0YpzHmZF5r/iyP3ZnQtMiJ+pjzisCiMNI+Sj+xQF5pXhSHxSB3uDbsBTzY/c2A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/yallist/-/yallist-2.1.2.tgz}
    name: yallist
    version: 2.1.2
    dev: true

  registry.npmjs.org/yallist@3.1.1:
    resolution: {integrity: sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz}
    name: yallist
    version: 3.1.1
    dev: true

  registry.npmjs.org/yallist@4.0.0:
    resolution: {integrity: sha512-3wdGidZyq5PB084XLES5TpOSRA3wjXAlIWMhum2kRcv/41Sn2emQ0dycQW4uZXLejwKvg6EsvbdlVL+FYEct7A==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/yallist/-/yallist-4.0.0.tgz}
    name: yallist
    version: 4.0.0
    dev: true

  registry.npmjs.org/yaml@1.10.2:
    resolution: {integrity: sha512-r3vXyErRCYJ7wg28yvBY5VSoAF8ZvlcW9/BwUzEtUsjvX/DKs24dIkuwjtuprwJJHsbyUbLApepYTR1BN4uHrg==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/yaml/-/yaml-1.10.2.tgz}
    name: yaml
    version: 1.10.2
    engines: {node: '>= 6'}
    dev: true

  registry.npmjs.org/yargs-parser@20.2.9:
    resolution: {integrity: sha512-y11nGElTIV+CT3Zv9t7VKl+Q3hTQoT9a1Qzezhhl6Rp21gJ/IVTW7Z3y9EWXhuUBC2Shnf+DX0antecpAwSP8w==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/yargs-parser/-/yargs-parser-20.2.9.tgz}
    name: yargs-parser
    version: 20.2.9
    engines: {node: '>=10'}
    dev: true

  registry.npmjs.org/yargs@16.2.0:
    resolution: {integrity: sha512-D1mvvtDG0L5ft/jGWkLpG1+m0eQxOfaBvTNELraWj22wSVUMWxZUvYgJYcKh6jGGIkJFhH4IZPQhR4TKpc8mBw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/yargs/-/yargs-16.2.0.tgz}
    name: yargs
    version: 16.2.0
    engines: {node: '>=10'}
    dependencies:
      cliui: registry.npmjs.org/cliui@7.0.4
      escalade: registry.npmjs.org/escalade@3.1.2
      get-caller-file: registry.npmjs.org/get-caller-file@2.0.5
      require-directory: registry.npmjs.org/require-directory@2.1.1
      string-width: registry.npmjs.org/string-width@4.2.3
      y18n: registry.npmjs.org/y18n@5.0.8
      yargs-parser: registry.npmjs.org/yargs-parser@20.2.9
    dev: true

  registry.npmjs.org/yorkie@2.0.0:
    resolution: {integrity: sha512-jcKpkthap6x63MB4TxwCyuIGkV0oYP/YRyuQU5UO0Yz/E/ZAu+653/uov+phdmO54n6BcvFRyyt0RRrWdN2mpw==, registry: http://registry.npmjs.com/, tarball: https://registry.npmjs.org/yorkie/-/yorkie-2.0.0.tgz}
    name: yorkie
    version: 2.0.0
    engines: {node: '>=4'}
    requiresBuild: true
    dependencies:
      execa: registry.npmjs.org/execa@0.8.0
      is-ci: registry.npmjs.org/is-ci@1.2.1
      normalize-path: registry.npmjs.org/normalize-path@1.0.0
      strip-indent: registry.npmjs.org/strip-indent@2.0.0
    dev: true
